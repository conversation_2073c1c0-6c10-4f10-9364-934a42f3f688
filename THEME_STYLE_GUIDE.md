# Dark Mode Theme Style Guide

## 🎯 **Consistent Theme Implementation**

This guide ensures ALL pages automatically support light/dark themes without manual adjustments.

## 📋 **Required Includes**

**Every PHP page MUST include:**
```php
require_once 'includes/theme.php';
```

## 🎨 **Theme Component Classes**

**ALWAYS use these instead of hardcoded colors:**

### **Text Colors**
```php
// Page headers and primary text
class="text-3xl font-bold text-gray-900 dark:text-white"

// Secondary text and descriptions  
class="text-gray-600 dark:text-gray-300"

// Muted text and help text
class="text-gray-500 dark:text-gray-400"

// Using theme functions (PREFERRED)
class="<?php echo getThemeComponentClasses('text-primary'); ?>"
class="<?php echo getThemeComponentClasses('text-secondary'); ?>"
class="<?php echo getThemeComponentClasses('text-muted'); ?>"
```

### **Backgrounds & Cards**
```php
// Card backgrounds
class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow"

// Card headers with borders
class="<?php echo getThemeComponentClasses('card-header'); ?>"

// Dividers and borders
class="border-t <?php echo getThemeComponentClasses('divider'); ?>"
```

### **Form Elements**
```php
// Input fields (text, email, number, textarea, select)
class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"

// Form labels
class="block text-sm font-medium text-gray-900 dark:text-white mb-2"

// Checkboxes and radio buttons
class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
```

### **Buttons**
```php
// Primary buttons
class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200"

// Secondary buttons  
class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200"
```

### **Tables**
```php
// Table container
<div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow overflow-hidden">

// Table body
<tbody class="<?php echo getThemeComponentClasses('card'); ?> divide-y <?php echo getThemeComponentClasses('divider'); ?>">

// Table headers
<thead class="bg-gray-50 dark:bg-gray-800">
  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
```

## 🚫 **NEVER Use These Hardcoded Classes**

```css
/* ❌ WRONG - Will break in dark mode */
bg-white
text-gray-900
text-gray-700
text-gray-600
border-gray-200
input-field
btn-primary
btn-secondary

/* ✅ CORRECT - Theme-aware */
<?php echo getThemeComponentClasses('card'); ?>
<?php echo getThemeComponentClasses('text-primary'); ?>
<?php echo getThemeComponentClasses('input'); ?>
<?php echo getThemeComponentClasses('button-primary'); ?>
```

## 📄 **Page Template Structure**

```php
<?php
require_once 'config.php';
require_once 'auth.php';
require_once 'includes/theme.php'; // ← REQUIRED!

// Page logic here...

$pageTitle = 'Page Name';
include 'templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Page Title</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Page description</p>
        </div>
    </div>
    
    <!-- Content Cards -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Section Title</h2>
        </div>
        
        <div class="p-6">
            <!-- Content here -->
        </div>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
```

## 🔧 **Form Template**

```php
<form method="POST" class="p-6">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    
    <div class="space-y-6">
        <div>
            <label for="field_name" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Field Label
            </label>
            <input 
                type="text" 
                id="field_name" 
                name="field_name" 
                class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                required
            >
        </div>
    </div>
    
    <div class="mt-6 pt-6 border-t <?php echo getThemeComponentClasses('divider'); ?>">
        <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
            Submit
        </button>
    </div>
</form>
```

## 📊 **Table Template**

```php
<div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow overflow-hidden">
    <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
        <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Table Title</h2>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y <?php echo getThemeComponentClasses('divider'); ?>">
            <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Column Header
                    </th>
                </tr>
            </thead>
            <tbody class="<?php echo getThemeComponentClasses('card'); ?> divide-y <?php echo getThemeComponentClasses('divider'); ?>">
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                        Cell Content
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
```

## ✅ **Quality Checklist**

Before deploying any page, verify:

- [ ] `require_once 'includes/theme.php';` included
- [ ] No hardcoded `bg-white` classes
- [ ] No hardcoded `text-gray-900` or `text-gray-700` classes  
- [ ] All form inputs use `getThemeComponentClasses('input')`
- [ ] All buttons use theme component classes
- [ ] All cards use `getThemeComponentClasses('card')`
- [ ] All headers use `text-gray-900 dark:text-white` pattern
- [ ] Page tested in both light and dark modes

## 🎯 **Result**

Following this guide ensures:
- ✅ **Automatic theme support** for all new pages
- ✅ **Consistent styling** across the application  
- ✅ **No manual theme adjustments** needed
- ✅ **Professional appearance** in both modes
- ✅ **Future-proof** theme implementation
