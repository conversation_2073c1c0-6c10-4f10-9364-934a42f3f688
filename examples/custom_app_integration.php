<?php
/**
 * Example Custom App Integration
 * Demonstrates how to integrate a custom app with the dashboard update system
 */

require_once '../includes/update_system.php';

/**
 * Example Custom App Class
 * This demonstrates how a custom app should implement the update interface
 */
class ExampleCustomApp implements CustomAppUpdateInterface {
    private $appId = 'example_app';
    private $appName = 'Example Custom App';
    private $currentVersion = '1.0.0';
    private $updateUrl = 'https://api.example.com/updates';
    
    public function __construct() {
        // Register this app with the update system
        $this->registerWithUpdateSystem();
    }
    
    /**
     * Register this app with the dashboard update system
     */
    private function registerWithUpdateSystem() {
        $updateSystem = new UpdateSystem();
        
        $updateSystem->registerCustomApp($this->appId, [
            'name' => $this->appName,
            'version' => $this->currentVersion,
            'update_url' => $this->updateUrl,
            'update_handler' => [$this, 'checkForUpdates'],
            'dependencies' => [
                'dashboard' => '>=1.0.0',
                'php' => '>=7.4'
            ]
        ]);
    }
    
    /**
     * Check for updates (required by interface)
     */
    public function checkForUpdates() {
        // In a real implementation, this would check your update server
        // For demo purposes, we'll simulate an available update
        
        $latestVersion = '1.1.0';
        $hasUpdate = version_compare($latestVersion, $this->currentVersion, '>');
        
        if ($hasUpdate) {
            return [
                'available' => true,
                'current_version' => $this->currentVersion,
                'new_version' => $latestVersion,
                'release_notes' => 'Bug fixes and new features',
                'download_url' => $this->updateUrl . '/v' . $latestVersion,
                'size' => '1.2 MB',
                'critical' => false,
                'compatibility' => [
                    'dashboard' => '>=1.0.0',
                    'php' => '>=7.4'
                ]
            ];
        }
        
        return [
            'available' => false,
            'current_version' => $this->currentVersion
        ];
    }
    
    /**
     * Download update (required by interface)
     */
    public function downloadUpdate($updateInfo) {
        $downloadUrl = $updateInfo['download_url'];
        $tempFile = sys_get_temp_dir() . '/update_' . $this->appId . '_' . time() . '.zip';
        
        // In a real implementation, download the update file
        // For demo, we'll just create a placeholder
        file_put_contents($tempFile, 'Demo update package for ' . $this->appName);
        
        return [
            'success' => true,
            'file_path' => $tempFile,
            'checksum' => md5_file($tempFile)
        ];
    }
    
    /**
     * Apply update (required by interface)
     */
    public function applyUpdate($updatePath) {
        // In a real implementation, this would:
        // 1. Verify the update package
        // 2. Extract files
        // 3. Run migration scripts
        // 4. Update configuration
        // 5. Clear caches
        
        // For demo purposes, we'll simulate the process
        $steps = [
            'Verifying update package...',
            'Extracting files...',
            'Running database migrations...',
            'Updating configuration...',
            'Clearing caches...',
            'Finalizing update...'
        ];
        
        foreach ($steps as $step) {
            // Log each step
            error_log("Custom App Update: $step");
            usleep(500000); // Simulate processing time
        }
        
        // Update version
        $this->currentVersion = '1.1.0';
        
        return [
            'success' => true,
            'message' => 'Update applied successfully',
            'new_version' => $this->currentVersion
        ];
    }
    
    /**
     * Rollback update (required by interface)
     */
    public function rollbackUpdate($backupPath) {
        // In a real implementation, this would:
        // 1. Verify backup integrity
        // 2. Stop app services
        // 3. Restore files from backup
        // 4. Restore database if needed
        // 5. Restart services
        
        return [
            'success' => true,
            'message' => 'Rollback completed successfully'
        ];
    }
    
    /**
     * Get current version (required by interface)
     */
    public function getVersion() {
        return $this->currentVersion;
    }
    
    /**
     * Custom method: Get app status
     */
    public function getStatus() {
        return [
            'app_id' => $this->appId,
            'name' => $this->appName,
            'version' => $this->currentVersion,
            'status' => 'running',
            'last_update_check' => date('Y-m-d H:i:s'),
            'update_channel' => 'stable'
        ];
    }
}

/**
 * Alternative Update Handler Function
 * For apps that prefer function-based approach instead of class-based
 */
function myCustomAppUpdateHandler() {
    return [
        'available' => false,
        'current_version' => '2.0.0',
        'message' => 'No updates available'
    ];
}

/**
 * Example usage in a custom app
 */
function initializeCustomApp() {
    // Method 1: Class-based approach (recommended)
    $customApp = new ExampleCustomApp();
    
    // Method 2: Function-based approach
    $updateSystem = new UpdateSystem();
    $updateSystem->registerCustomApp('my_function_app', [
        'name' => 'Function-based App',
        'version' => '2.0.0',
        'update_handler' => 'myCustomAppUpdateHandler'
    ]);
    
    // Method 3: URL-based approach
    $updateSystem->registerCustomApp('my_url_app', [
        'name' => 'URL-based App',
        'version' => '1.5.0',
        'update_url' => 'https://api.myapp.com/check-updates'
    ]);
}

/**
 * Example Update Server Response Format
 * This is what your update server should return when checked
 */
function exampleUpdateServerResponse() {
    return [
        'available' => true,
        'current_version' => '1.0.0',
        'new_version' => '1.1.0',
        'release_notes' => 'Bug fixes and performance improvements',
        'download_url' => 'https://api.myapp.com/downloads/v1.1.0.zip',
        'size' => '2.5 MB',
        'critical' => false,
        'compatibility' => [
            'dashboard' => '>=1.0.0',
            'php' => '>=7.4',
            'mysql' => '>=5.7'
        ],
        'changelog' => [
            'Fixed memory leak in data processing',
            'Added new API endpoints',
            'Improved error handling',
            'Updated dependencies'
        ]
    ];
}

// Initialize the example app (uncomment to test)
// initializeCustomApp();
