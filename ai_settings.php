<?php
/**
 * AI Settings Page
 * User-accessible page for configuring AI models and API keys
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/ai_config.php';
require_once 'includes/ai_client.php';
require_once 'includes/theme.php';
require_once 'includes/toast.php';
require_once 'includes/logger.php';

// Require user to be logged in
requireAuth();

$currentUser = getCurrentUser();
$message = '';
$messageType = 'info';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        toastError('Invalid security token.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_ai_settings':
                $settings = [
                    'active_model' => $_POST['active_model'] ?? null,
                    'anthropic_api_key' => $_POST['anthropic_api_key'] ?? '',
                    'google_api_key' => $_POST['google_api_key'] ?? '',
                    'openai_api_key' => $_POST['openai_api_key'] ?? ''
                ];
                
                // Validate that if a model is selected, the corresponding API key is provided
                if ($settings['active_model']) {
                    $provider = getModelProvider($settings['active_model']);
                    $keyField = $provider . '_api_key';
                    
                    if (empty($settings[$keyField])) {
                        toastError('Please provide an API key for the selected AI model.');
                        break;
                    }
                }
                
                if (updateUserAISettings($currentUser['id'], $settings)) {
                    toastSuccess('AI settings updated successfully.');
                    
                    // Log the activity
                    logActivity($currentUser['id'], 'ai_settings_updated', 'Updated AI configuration');
                } else {
                    toastError('Failed to update AI settings.');
                }
                break;
                
            case 'test_connection':
                try {
                    $client = createAIClient();
                    $result = $client->testConnection();
                    
                    if ($result['success']) {
                        toastSuccess('AI connection test successful! Response: ' . substr($result['response'], 0, 100) . '...');
                    } else {
                        toastError('AI connection test failed: ' . $result['message']);
                    }
                } catch (Exception $e) {
                    toastError('AI connection test failed: ' . $e->getMessage());
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: ai_settings.php');
        exit();
    }
}

// Get current AI settings
$aiSettings = getUserAISettings($currentUser['id']);
$availableModels = getAvailableAIModels();
$providers = getAIProviders();

// Set page title
$pageTitle = 'AI Settings';
include 'templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">AI Settings</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-1">Configure your AI models and API keys</p>
    </div>

    <!-- AI Status Card -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">AI Status</h2>
        </div>
        
        <div class="p-6">
            <?php if (currentUserHasAI()): ?>
                <?php $activeConfig = getActiveAIConfig($currentUser['id']); ?>
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">AI Configured</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Active Model: <?php echo htmlspecialchars($activeConfig['display_name']); ?>
                        </p>
                    </div>
                </div>
                
                <!-- Test Connection Button -->
                <form method="POST" class="mt-4">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="test_connection">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                        <?php echo getIcon('connection-link', 'text-base'); ?> Test Connection
                    </button>
                </form>
            <?php else: ?>
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">AI Not Configured</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Please select a model and provide an API key below
                        </p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- AI Configuration Form -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">AI Configuration</h2>
        </div>
        
        <form method="POST" class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="update_ai_settings">
            
            <div class="space-y-6">
                <!-- Model Selection -->
                <div>
                    <label for="active_model" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Select AI Model
                    </label>
                    <select 
                        id="active_model" 
                        name="active_model" 
                        class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                        onchange="toggleAPIKeyFields()"
                    >
                        <option value="">-- Select an AI Model --</option>
                        <?php foreach ($providers as $providerId => $provider): ?>
                            <optgroup label="<?php echo htmlspecialchars($provider['name']); ?>">
                                <?php foreach ($provider['models'] as $modelId): ?>
                                    <option 
                                        value="<?php echo htmlspecialchars($modelId); ?>"
                                        data-provider="<?php echo htmlspecialchars($providerId); ?>"
                                        <?php echo $aiSettings['active_model'] === $modelId ? 'selected' : ''; ?>
                                    >
                                        <?php echo htmlspecialchars($availableModels[$modelId]); ?>
                                    </option>
                                <?php endforeach; ?>
                            </optgroup>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Choose the AI model you want to use for your applications
                    </p>
                </div>

                <!-- API Key Fields -->
                <div class="space-y-4">
                    <!-- Anthropic API Key -->
                    <div id="anthropic_key_field" class="hidden">
                        <label for="anthropic_api_key" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Anthropic API Key
                        </label>
                        <input 
                            type="password" 
                            id="anthropic_api_key" 
                            name="anthropic_api_key" 
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo htmlspecialchars($aiSettings['anthropic_api_key']); ?>"
                            placeholder="sk-ant-..."
                        >
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Get your API key from <a href="https://console.anthropic.com/" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline">Anthropic Console</a>
                        </p>
                    </div>

                    <!-- Google API Key -->
                    <div id="google_key_field" class="hidden">
                        <label for="google_api_key" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Google API Key
                        </label>
                        <input 
                            type="password" 
                            id="google_api_key" 
                            name="google_api_key" 
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo htmlspecialchars($aiSettings['google_api_key']); ?>"
                            placeholder="AIza..."
                        >
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline">Google AI Studio</a>
                        </p>
                    </div>

                    <!-- OpenAI API Key -->
                    <div id="openai_key_field" class="hidden">
                        <label for="openai_api_key" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                            OpenAI API Key
                        </label>
                        <input 
                            type="password" 
                            id="openai_api_key" 
                            name="openai_api_key" 
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo htmlspecialchars($aiSettings['openai_api_key']); ?>"
                            placeholder="sk-..."
                        >
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline">OpenAI Platform</a>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6">
                <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                    <?php echo getIcon('backup-disk', 'text-base'); ?> Save AI Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function toggleAPIKeyFields() {
    const modelSelect = document.getElementById('active_model');
    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
    const provider = selectedOption.getAttribute('data-provider');
    
    // Hide all API key fields
    document.getElementById('anthropic_key_field').classList.add('hidden');
    document.getElementById('google_key_field').classList.add('hidden');
    document.getElementById('openai_key_field').classList.add('hidden');
    
    // Show the relevant API key field
    if (provider) {
        document.getElementById(provider + '_key_field').classList.remove('hidden');
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleAPIKeyFields();
});
</script>

<?php include 'templates/footer.php'; ?>
