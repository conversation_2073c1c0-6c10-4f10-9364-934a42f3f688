<?php
/**
 * User Login Page
 * Handles user authentication
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/theme.php';

// Redirect if already logged in
redirectIfAuthenticated();

// Initialize variables
$errors = [];
$username = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Get form data
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Attempt login
        if (!empty($username) && !empty($password)) {
            $result = loginUser($username, $password);
            
            if ($result['success']) {
                setFlashMessage('Welcome back, ' . htmlspecialchars($username) . '!', 'success');
                header('Location: dashboard.php');
                exit();
            } else {
                $errors[] = $result['message'];
            }
        } else {
            $errors[] = 'Please enter both username and password.';
        }
    }
}

// Set page title
$pageTitle = 'Login';

// Include header
include 'templates/header.php';
?>

<div class="max-w-md mx-auto">
    <!-- Login Card -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> p-8 rounded-lg shadow">
        <div class="text-center mb-8">
            <div class="mb-4"><?php echo getIcon('app-logo', 'text-4xl'); ?></div>
            <h1 class="text-2xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">Welcome Back</h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-2">Sign in to your <?php echo APP_NAME; ?> account</p>
        </div>
        
        <!-- Display Errors -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <!-- Login Form -->
        <form method="POST" action="" id="loginForm" novalidate>
            <!-- CSRF Token -->
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <!-- Username Field -->
            <div class="mb-6">
                <label for="username" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                    Username
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                    required 
                    value="<?php echo htmlspecialchars($username); ?>"
                    placeholder="Enter your username"
                    autocomplete="username"
                >
            </div>
            
            <!-- Password Field -->
            <div class="mb-6">
                <label for="password" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                    Password
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 pr-10 rounded-lg"
                        required
                        placeholder="Enter your password"
                        autocomplete="current-password"
                    >
                    <button
                        type="button"
                        id="togglePassword"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                    >
                        <svg id="eyeIcon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <input
                        id="remember_me"
                        name="remember_me"
                        type="checkbox"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                    >
                    <label for="remember_me" class="ml-2 block text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                        Remember me
                    </label>
                </div>

                <div class="text-sm">
                    <a href="#" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                        Forgot password?
                    </a>
                </div>
            </div>
            
            <!-- Submit Button -->
            <button 
                type="submit" 
                class="w-full <?php echo getThemeComponentClasses('button-primary'); ?> text-lg py-3 mb-4 rounded-lg font-medium transition duration-200"
                onclick="return validateForm('loginForm')"
            >
                Sign In
            </button>
        </form>
        
        <!-- Registration Link -->
        <div class="text-center">
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                Don't have an account?
                <a href="register.php" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">Create one</a>
            </p>
        </div>
    </div>
</div>

<!-- Additional JavaScript for login form -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');
    
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            // Toggle eye icon
            if (type === 'password') {
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                `;
            } else {
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                `;
            }
        });
    }
    
    // Auto-focus username field
    const usernameInput = document.getElementById('username');
    if (usernameInput && !usernameInput.value) {
        usernameInput.focus();
    }
});
</script>

<?php include 'templates/footer.php'; ?>
