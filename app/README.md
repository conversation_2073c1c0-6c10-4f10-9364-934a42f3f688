# 📱 Custom Apps Directory

This directory contains custom applications that integrate with the AI Dashboard. Each app in this directory is automatically included in the dashboard's management functionality including backup/restore, updates, logs, and more.

## 🏗️ Directory Structure

```
app/
├── README.md                    # This file
├── sample-app/                  # Example integrated app
│   ├── index.php               # Main app entry point
│   ├── config.json             # App configuration
│   ├── includes/               # App-specific includes
│   │   ├── app.php            # Main app class
│   │   ├── ai-integration.php  # AI API integration
│   │   └── data-storage.php   # Flat file data operations
│   ├── assets/                 # App assets
│   │   ├── css/               # Stylesheets
│   │   ├── js/                # JavaScript files
│   │   └── images/            # App images
│   ├── templates/              # App templates
│   │   ├── header.php         # App header
│   │   ├── footer.php         # App footer
│   │   └── main.php           # Main template
│   ├── api/                    # API endpoints
│   │   ├── chat.php           # AI chat endpoint
│   │   └── data.php           # Data API
│   ├── data/                   # Flat file data storage
│   │   ├── chats/             # Chat conversations
│   │   ├── user_data/         # User-specific data
│   │   ├── settings/          # App settings
│   │   └── .htaccess          # Access protection
│   ├── logs/                   # App-specific logs
│   └── VERSION                 # App version file
└── your-app/                   # Your custom app here
    └── ...
```

## 🚀 Features

### Dashboard Integration
- **Automatic Discovery** - Apps are automatically detected by the dashboard
- **Unified Management** - Backup, restore, update through dashboard interface
- **Centralized Logging** - App logs integrated with dashboard logging system
- **Theme Integration** - Apps inherit dashboard theme and styling
- **Authentication** - Apps use dashboard authentication system

### AI Integration
- **AI API Access** - Direct integration with dashboard AI settings
- **Chat Interface** - Ready-to-use AI chat components
- **Context Sharing** - Share context between dashboard and apps
- **Model Selection** - Use configured AI models from dashboard

### Update System
- **Version Management** - Automatic version tracking
- **Update API** - Integrate with dashboard update system
- **Backup Integration** - Automatic backups before updates
- **Rollback Support** - Easy restoration from backups

## 📋 App Requirements

### Mandatory Files
1. **`config.json`** - App configuration and metadata
2. **`VERSION`** - Current app version
3. **`index.php`** - Main entry point

### Recommended Structure
1. **`includes/app.php`** - Main app class implementing dashboard interfaces
2. **`includes/ai-integration.php`** - AI API integration
3. **`api/`** - API endpoints for app functionality
4. **`templates/`** - App-specific templates
5. **`assets/`** - CSS, JS, and image assets

## 🔧 Integration Interfaces

### UpdateInterface
```php
interface CustomAppUpdateInterface {
    public function checkForUpdates();
    public function downloadUpdate($updateInfo);
    public function applyUpdate($updatePath);
    public function rollbackUpdate($backupPath);
    public function getVersion();
}
```

### LoggingInterface
```php
interface AppLoggingInterface {
    public function logActivity($action, $description, $data = []);
    public function getAppLogs($limit = 100);
    public function clearLogs();
}
```

### AIInterface
```php
interface AppAIInterface {
    public function getAIConfig();
    public function sendAIRequest($message, $context = []);
    public function getAIModels();
}
```

## 📝 Quick Start

1. **Copy sample-app** to create your new app
2. **Update config.json** with your app details
3. **Modify includes/app.php** with your app logic
4. **Customize templates** for your app UI
5. **Test integration** through dashboard interface

### 🗄️ **Flat File Data Storage**

Apps use flat file storage following the same pattern as the AI Dashboard:

```php
// Example: Using flat file data storage
class MyAppData {
    private $dataDir;

    public function __construct() {
        $this->dataDir = __DIR__ . '/data';
        $this->initializeDataDirectory();
    }

    private function initializeDataDirectory() {
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }

        // Protect data directory
        $htaccessFile = $this->dataDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }

    public function saveData($key, $data) {
        $file = $this->dataDir . '/' . $key . '.json';
        return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT));
    }

    public function loadData($key) {
        $file = $this->dataDir . '/' . $key . '.json';
        if (!file_exists($file)) return null;

        $content = file_get_contents($file);
        return json_decode($content, true);
    }
}
```

## 🔒 Security

- Apps inherit dashboard security settings
- Authentication required for app access
- CSRF protection enabled by default
- Input validation and sanitization required
- Data directory protected with .htaccess

## 📊 Monitoring

- App activities logged to dashboard activity log
- Performance metrics tracked
- Error logging integrated with dashboard
- Health checks available through dashboard

## 🎯 Best Practices

1. **Follow dashboard conventions** for consistent UX
2. **Use dashboard themes** for visual consistency
3. **Implement proper error handling** for reliability
4. **Log important activities** for audit trails
5. **Test backup/restore** functionality regularly
6. **Document your app** for maintainability

## 🆘 Support

- Check sample-app for implementation examples
- Review dashboard documentation
- Use dashboard logging for debugging
- Test in dashboard demo mode first
