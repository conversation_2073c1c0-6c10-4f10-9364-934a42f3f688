<?php
/**
 * Test Mini-App Integration
 * 
 * Simple test script to verify the integration system is working
 */

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../auth.php';
require_once __DIR__ . '/../../config.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/includes/app.php';
require_once __DIR__ . '/includes/mini-app-integration.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get the app instance
    $app = $GLOBALS['chat_bot_app'];
    
    if (!$app) {
        throw new Exception('App instance not found');
    }
    
    $dataStorage = $app->getDataStorage();
    $aiIntegration = $app->getAIIntegration();
    $userId = $_SESSION['user_id'];
    
    if (!$userId) {
        throw new Exception('User not logged in');
    }
    
    // Create integration instance
    $integration = new MiniAppIntegration($dataStorage, $aiIntegration, $userId);
    
    // Test sending a simple message
    $result = $integration->sendToChat(
        'test',
        'This is a test message from the integration system',
        'text',
        ['test' => true, 'timestamp' => date('Y-m-d H:i:s')]
    );
    
    echo json_encode([
        'test_result' => $result,
        'app_instance' => 'OK',
        'user_id' => $userId,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
