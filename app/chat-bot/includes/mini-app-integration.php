<?php
/**
 * Chat Bot - Mini App Integration System
 * 
 * This class handles integration between mini-apps and the chat bot,
 * including message passing, context sharing, and tool invocation.
 */

class MiniAppIntegration {
    private $dataStorage;
    private $aiIntegration;
    private $userId;
    
    public function __construct($dataStorage, $aiIntegration, $userId) {
        $this->dataStorage = $dataStorage;
        $this->aiIntegration = $aiIntegration;
        $this->userId = $userId;
    }
    
    /**
     * Send content from mini-app to chat
     */
    public function sendToChat($miniAppId, $content, $contentType = 'text', $metadata = []) {
        try {
            // Validate input
            if (empty($content)) {
                throw new Exception('Content cannot be empty');
            }

            if (empty($miniAppId)) {
                throw new Exception('Mini app ID cannot be empty');
            }

            // Format the message based on content type
            $formattedMessage = $this->formatContentForChat($miniAppId, $content, $contentType, $metadata);

            // Create a new conversation or get the active one
            $conversationId = $this->getOrCreateActiveConversation();

            // Send the message to chat (this will be displayed as a user message)
            $saveResult = $this->saveUserMessage($conversationId, $formattedMessage, $miniAppId, $metadata);

            if (!$saveResult) {
                throw new Exception('Failed to save message to chat');
            }

            // Generate AI response if needed
            $aiResponse = $this->generateAIResponse($formattedMessage, $conversationId, $miniAppId, $metadata);

            return [
                'success' => true,
                'conversation_id' => $conversationId,
                'message' => $formattedMessage,
                'ai_response' => $aiResponse,
                'mini_app_id' => $miniAppId
            ];

        } catch (Exception $e) {
            // Log the error for debugging
            error_log("MiniAppIntegration::sendToChat error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Register mini-app integration with chat bot
     */
    public function registerMiniApp($miniAppId, $capabilities = []) {
        try {
            $integrationData = [
                'mini_app_id' => $miniAppId,
                'capabilities' => $capabilities,
                'registered_at' => date('Y-m-d H:i:s'),
                'user_id' => $this->userId,
                'status' => 'active'
            ];
            
            $success = $this->dataStorage->storeData(
                $this->userId, 
                'mini_app_integrations', 
                $miniAppId, 
                $integrationData,
                ['type' => 'integration', 'source' => 'mini_app']
            );
            
            return [
                'success' => $success,
                'message' => $success ? 'Mini-app integrated successfully' : 'Failed to integrate mini-app'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get available mini-app tools for AI
     */
    public function getAvailableTools() {
        try {
            $integrations = $this->dataStorage->getData($this->userId, 'mini_app_integrations');
            $tools = [];
            
            foreach ($integrations as $integration) {
                $data = $integration['data_value'];
                if ($data['status'] === 'active' && !empty($data['capabilities'])) {
                    $tools[$data['mini_app_id']] = $data['capabilities'];
                }
            }
            
            return $tools;
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Invoke mini-app tool from chat
     */
    public function invokeTool($miniAppId, $toolName, $parameters = []) {
        try {
            // Validate mini-app is integrated
            $integration = $this->dataStorage->getData($this->userId, 'mini_app_integrations', $miniAppId);
            if (empty($integration)) {
                throw new Exception('Mini-app not integrated');
            }
            
            // Call the mini-app API
            $result = $this->callMiniAppAPI($miniAppId, $toolName, $parameters);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Format content for chat display
     */
    private function formatContentForChat($miniAppId, $content, $contentType, $metadata) {
        $appName = $this->getMiniAppName($miniAppId);
        
        switch ($contentType) {
            case 'calculation':
                return "📊 **{$appName} Result**\n\n" . 
                       "Expression: `{$metadata['expression']}`\n" .
                       "Result: **{$content}**";
                       
            case 'note':
                $title = !empty($metadata['title']) ? $metadata['title'] : 'Quick Note';
                return "📝 **{$appName} - {$title}**\n\n{$content}";
                
            case 'conversion':
                return "🔄 **{$appName} Conversion**\n\n" .
                       "{$metadata['from_value']} {$metadata['from_unit']} = **{$content}** {$metadata['to_unit']}";
                       
            case 'password':
                return "🔐 **{$appName} Generated Password**\n\n" .
                       "Password: `{$content}`\n" .
                       "Strength: {$metadata['strength']}\n" .
                       "Length: {$metadata['length']} characters";
                       
            case 'color_palette':
                $colors = implode(', ', $metadata['colors']);
                return "🎨 **{$appName} Color Palette**\n\n" .
                       "Theme: {$metadata['theme']}\n" .
                       "Colors: {$colors}";
                       
            default:
                return "🔧 **{$appName}**\n\n{$content}";
        }
    }
    
    /**
     * Get or create active conversation
     */
    private function getOrCreateActiveConversation() {
        // For now, create a new conversation ID
        // In a full implementation, this would check for active conversations
        return 'conv_' . uniqid();
    }

    /**
     * Save user message from mini-app
     */
    private function saveUserMessage($conversationId, $message, $miniAppId, $metadata) {
        // Save the message without AI response first
        return $this->dataStorage->saveChatMessage(
            $this->userId,
            $conversationId,
            $message,
            '', // No AI response yet
            'mini-app',
            0, // No tokens used
            'mini_app_integration',
            ['mini_app_id' => $miniAppId, 'metadata' => $metadata]
        );
    }
    
    /**
     * Generate AI response to mini-app content
     */
    private function generateAIResponse($message, $conversationId, $miniAppId, $metadata) {
        try {
            // Create context for AI about the mini-app integration
            $context = [
                'mini_app_id' => $miniAppId,
                'mini_app_name' => $this->getMiniAppName($miniAppId),
                'content_metadata' => $metadata,
                'integration_context' => 'This content was sent from a mini-app. Acknowledge it and offer relevant assistance.'
            ];

            // Get AI response
            $response = $this->aiIntegration->chat($message, [], $context);

            if (isset($response['success']) && $response['success']) {
                $aiResponseText = $response['response'];

                // Update the conversation with the AI response
                $this->dataStorage->saveChatMessage(
                    $this->userId,
                    $conversationId,
                    $message,
                    $aiResponseText,
                    $response['model'] ?? 'unknown',
                    $response['tokens_used'] ?? 0,
                    'mini_app_integration',
                    ['mini_app_id' => $miniAppId, 'ai_context' => $context]
                );

                return $aiResponseText;
            } else {
                // If AI response failed, provide a default response
                $defaultResponse = 'I received your content from ' . $this->getMiniAppName($miniAppId) . '. How can I help you with it?';

                // Save the default response
                $this->dataStorage->saveChatMessage(
                    $this->userId,
                    $conversationId,
                    $message,
                    $defaultResponse,
                    'default',
                    0,
                    'mini_app_integration',
                    ['mini_app_id' => $miniAppId, 'fallback' => true]
                );

                return $defaultResponse;
            }

        } catch (Exception $e) {
            $fallbackResponse = 'I received your content from the mini-app. How can I assist you?';

            // Save the fallback response
            try {
                $this->dataStorage->saveChatMessage(
                    $this->userId,
                    $conversationId,
                    $message,
                    $fallbackResponse,
                    'fallback',
                    0,
                    'mini_app_integration',
                    ['mini_app_id' => $miniAppId, 'error' => $e->getMessage()]
                );
            } catch (Exception $saveError) {
                // If even saving fails, just return the response
            }

            return $fallbackResponse;
        }
    }
    
    /**
     * Get mini-app display name
     */
    private function getMiniAppName($miniAppId) {
        $names = [
            'calculator' => 'Calculator',
            'notes' => 'Quick Notes',
            'converter' => 'Unit Converter',
            'colors' => 'Color Palette',
            'password' => 'Password Generator'
        ];
        
        return $names[$miniAppId] ?? ucfirst($miniAppId);
    }
    
    /**
     * Call mini-app API
     */
    private function callMiniAppAPI($miniAppId, $toolName, $parameters) {
        // This would make HTTP requests to mini-app APIs
        // For now, return a placeholder response
        return [
            'success' => true,
            'result' => 'Tool invoked successfully',
            'mini_app_id' => $miniAppId,
            'tool_name' => $toolName,
            'parameters' => $parameters
        ];
    }
    
    /**
     * Get integration status for mini-app
     */
    public function getIntegrationStatus($miniAppId) {
        try {
            $integration = $this->dataStorage->getData($this->userId, 'mini_app_integrations', $miniAppId);
            
            if (empty($integration)) {
                return [
                    'integrated' => false,
                    'status' => 'not_integrated'
                ];
            }
            
            $data = $integration[0]['data_value'] ?? [];
            
            return [
                'integrated' => true,
                'status' => $data['status'] ?? 'unknown',
                'capabilities' => $data['capabilities'] ?? [],
                'registered_at' => $data['registered_at'] ?? null
            ];
            
        } catch (Exception $e) {
            return [
                'integrated' => false,
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Remove mini-app integration
     */
    public function removeMiniAppIntegration($miniAppId) {
        try {
            $success = $this->dataStorage->deleteData($this->userId, 'mini_app_integrations', $miniAppId);
            
            return [
                'success' => $success,
                'message' => $success ? 'Mini-app integration removed' : 'Failed to remove integration'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
