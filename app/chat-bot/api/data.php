<?php
/**
 * Chat Bot - Knowledge Base API Endpoint
 *
 * This endpoint handles CRUD operations for Chat Bot knowledge base
 * and provides data management for the AI assistant.
 */

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../config.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Set JSON response header
header('Content-Type: application/json');

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$dataStorage = $app->getDataStorage();

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        handleGetData();
        break;
    case 'POST':
        handleCreateData();
        break;
    case 'PUT':
        handleUpdateData();
        break;
    case 'DELETE':
        handleDeleteData();
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

/**
 * Handle GET request - Retrieve data
 */
function handleGetData() {
    global $app, $dataStorage;
    
    try {
        $userId = $_SESSION['user_id'];
        $dataType = $_GET['type'] ?? 'facts';
        $dataKey = $_GET['key'] ?? null;
        
        if ($dataKey) {
            // Get specific data item
            $data = $dataStorage->getData($userId, $dataType, $dataKey);
            
            if (!$data) {
                http_response_code(404);
                echo json_encode(['error' => 'Data not found']);
                return;
            }
            
            echo json_encode([
                'success' => true,
                'data' => $data
            ]);
        } else {
            // Get all data of type
            $data = $dataStorage->getData($userId, $dataType);
            
            echo json_encode([
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ]);
        }
        
        // Log activity
        $app->logActivity('data.retrieved', 'Data retrieved', [
            'data_type' => $dataType,
            'data_key' => $dataKey,
            'user_id' => $userId
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Handle POST request - Create data
 */
function handleCreateData() {
    global $app, $dataStorage;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $userId = $_SESSION['user_id'];
        $dataType = $input['type'] ?? 'facts';
        $dataKey = $input['key'] ?? uniqid('data_');
        $dataValue = $input['value'] ?? [];
        $metadata = $input['metadata'] ?? [];
        
        if (empty($dataValue)) {
            throw new Exception('Data value is required');
        }
        
        // Store data
        $success = $dataStorage->storeData($userId, $dataType, $dataKey, $dataValue, $metadata);
        
        if (!$success) {
            throw new Exception('Failed to store data');
        }
        
        // Log activity
        $app->logActivity('data.created', 'Data created', [
            'data_type' => $dataType,
            'data_key' => $dataKey,
            'user_id' => $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Data created successfully',
            'data_key' => $dataKey
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Handle PUT request - Update data
 */
function handleUpdateData() {
    global $app, $dataStorage;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $userId = $_SESSION['user_id'];
        $dataType = $input['type'] ?? 'facts';
        $dataKey = $input['key'] ?? null;
        $dataValue = $input['value'] ?? [];
        $metadata = $input['metadata'] ?? [];
        
        if (!$dataKey) {
            throw new Exception('Data key is required for updates');
        }
        
        if (empty($dataValue)) {
            throw new Exception('Data value is required');
        }
        
        // Check if data exists
        $existing = $dataStorage->getData($userId, $dataType, $dataKey);
        if (!$existing) {
            http_response_code(404);
            echo json_encode(['error' => 'Data not found']);
            return;
        }
        
        // Update data
        $success = $dataStorage->storeData($userId, $dataType, $dataKey, $dataValue, $metadata);
        
        if (!$success) {
            throw new Exception('Failed to update data');
        }
        
        // Log activity
        $app->logActivity('data.updated', 'Data updated', [
            'data_type' => $dataType,
            'data_key' => $dataKey,
            'user_id' => $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Data updated successfully'
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Handle DELETE request - Delete data
 */
function handleDeleteData() {
    global $app, $dataStorage;
    
    try {
        $userId = $_SESSION['user_id'];
        $dataType = $_GET['type'] ?? 'facts';
        $dataKey = $_GET['key'] ?? null;
        
        if (!$dataKey) {
            throw new Exception('Data key is required for deletion');
        }
        
        // Check if data exists
        $existing = $dataStorage->getData($userId, $dataType, $dataKey);
        if (!$existing) {
            http_response_code(404);
            echo json_encode(['error' => 'Data not found']);
            return;
        }
        
        // Delete data
        $success = $dataStorage->deleteData($userId, $dataType, $dataKey);
        
        if (!$success) {
            throw new Exception('Failed to delete data');
        }
        
        // Log activity
        $app->logActivity('data.deleted', 'Data deleted', [
            'data_type' => $dataType,
            'data_key' => $dataKey,
            'user_id' => $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Data deleted successfully'
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>
