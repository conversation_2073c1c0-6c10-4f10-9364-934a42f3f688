<?php
/**
 * Chat Bot - Chat Integration API
 * 
 * This endpoint handles integration between mini-apps and the chat interface,
 * allowing mini-apps to send messages directly to active chat conversations.
 */

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../config.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';
require_once __DIR__ . '/../includes/mini-app-integration.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get the app instance
    $app = $GLOBALS['chat_bot_app'];
    $dataStorage = $app->getDataStorage();
    $aiIntegration = $app->getAIIntegration();
    $userId = $_SESSION['user_id'];
    
    // Create integration instance
    $integration = new MiniAppIntegration($dataStorage, $aiIntegration, $userId);
    
    // Handle different HTTP methods
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            handlePostRequest($integration, $dataStorage, $aiIntegration, $userId);
            break;
            
        case 'GET':
            handleGetRequest($dataStorage, $userId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}

/**
 * Handle POST requests
 */
function handlePostRequest($integration, $dataStorage, $aiIntegration, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'send_message':
            $result = sendMessageToChat($input, $integration, $dataStorage, $aiIntegration, $userId);
            echo json_encode($result);
            break;
            
        case 'get_conversation':
            $result = getConversationMessages($input, $dataStorage, $userId);
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Handle GET requests
 */
function handleGetRequest($dataStorage, $userId) {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_recent_conversations':
            $conversations = $dataStorage->getUserConversations($userId, 10);
            echo json_encode([
                'success' => true,
                'conversations' => $conversations
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Send message to chat from mini-app
 */
function sendMessageToChat($input, $integration, $dataStorage, $aiIntegration, $userId) {
    try {
        $miniAppId = $input['mini_app_id'] ?? '';
        $content = $input['content'] ?? '';
        $contentType = $input['content_type'] ?? 'text';
        $metadata = $input['metadata'] ?? [];
        $conversationId = $input['conversation_id'] ?? null;
        
        if (empty($miniAppId) || empty($content)) {
            throw new Exception('Mini app ID and content are required');
        }
        
        // If no conversation ID provided, create a new one
        if (empty($conversationId)) {
            $conversationId = 'conv_' . uniqid();
        }
        
        // Format the message for chat display
        $formattedMessage = formatMiniAppMessage($miniAppId, $content, $contentType, $metadata);
        
        // Save the message as a user message
        $success = $dataStorage->saveChatMessage(
            $userId,
            $conversationId,
            $formattedMessage,
            '', // No AI response yet
            'mini-app',
            0, // No tokens used
            'mini_app_integration',
            ['mini_app_id' => $miniAppId, 'metadata' => $metadata]
        );
        
        if (!$success) {
            throw new Exception('Failed to save message to chat');
        }
        
        // Generate AI response
        $aiResponse = generateAIResponseToMiniApp($formattedMessage, $conversationId, $miniAppId, $metadata, $aiIntegration, $dataStorage, $userId);
        
        return [
            'success' => true,
            'conversation_id' => $conversationId,
            'message' => $formattedMessage,
            'ai_response' => $aiResponse,
            'mini_app_id' => $miniAppId,
            'redirect_url' => "?route=/chat&conversation_id={$conversationId}"
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get conversation messages
 */
function getConversationMessages($input, $dataStorage, $userId) {
    try {
        $conversationId = $input['conversation_id'] ?? '';
        
        if (empty($conversationId)) {
            throw new Exception('Conversation ID is required');
        }
        
        $messages = $dataStorage->getChatHistory($conversationId, 50);
        
        // Filter messages for this user
        $userMessages = array_filter($messages, function($message) use ($userId) {
            return $message['user_id'] === $userId;
        });
        
        return [
            'success' => true,
            'conversation_id' => $conversationId,
            'messages' => array_values($userMessages)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Format mini-app message for chat display
 */
function formatMiniAppMessage($miniAppId, $content, $contentType, $metadata) {
    $appNames = [
        'calculator' => 'Calculator',
        'notes' => 'Quick Notes',
        'converter' => 'Unit Converter',
        'colors' => 'Color Palette',
        'password' => 'Password Generator'
    ];
    
    $appName = $appNames[$miniAppId] ?? ucfirst($miniAppId);
    
    switch ($contentType) {
        case 'calculation':
            return "📊 **{$appName} Result**\n\n" . 
                   "Expression: `{$metadata['expression']}`\n" .
                   "Result: **{$content}**";
                   
        case 'note':
            $title = !empty($metadata['title']) ? $metadata['title'] : 'Quick Note';
            return "📝 **{$appName} - {$title}**\n\n{$content}";
            
        case 'conversion':
            return "🔄 **{$appName} Conversion**\n\n" .
                   "{$metadata['from_value']} {$metadata['from_unit']} = **{$content}** {$metadata['to_unit']}";
                   
        case 'password':
            return "🔐 **{$appName} Generated Password**\n\n" .
                   "Password: `{$content}`\n" .
                   "Strength: {$metadata['strength']}\n" .
                   "Length: {$metadata['length']} characters";
                   
        case 'color_palette':
            $colors = implode(', ', $metadata['colors']);
            return "🎨 **{$appName} Color Palette**\n\n" .
                   "Theme: {$metadata['theme']}\n" .
                   "Colors: {$colors}";
                   
        default:
            return "🔧 **{$appName}**\n\n{$content}";
    }
}

/**
 * Generate AI response to mini-app content
 */
function generateAIResponseToMiniApp($message, $conversationId, $miniAppId, $metadata, $aiIntegration, $dataStorage, $userId) {
    try {
        // Create context for AI about the mini-app integration
        $context = [
            'mini_app_id' => $miniAppId,
            'mini_app_name' => getMiniAppName($miniAppId),
            'content_metadata' => $metadata,
            'integration_context' => 'This content was sent from a mini-app. Acknowledge it and offer relevant assistance or ask follow-up questions.'
        ];
        
        // Get AI response
        $response = $aiIntegration->chat($message, [], $context);
        
        if ($response['success']) {
            // Save the AI response
            $dataStorage->saveChatMessage(
                $userId,
                $conversationId,
                $message,
                $response['response'],
                $response['model'] ?? 'unknown',
                $response['tokens_used'] ?? 0,
                'mini_app_integration',
                ['mini_app_id' => $miniAppId, 'ai_context' => $context]
            );
            
            return $response['response'];
        }
        
        return 'I received your content from ' . getMiniAppName($miniAppId) . '. How can I help you with it?';
        
    } catch (Exception $e) {
        return 'I received your content from the mini-app. How can I assist you?';
    }
}

/**
 * Get mini-app display name
 */
function getMiniAppName($miniAppId) {
    $names = [
        'calculator' => 'Calculator',
        'notes' => 'Quick Notes',
        'converter' => 'Unit Converter',
        'colors' => 'Color Palette',
        'password' => 'Password Generator'
    ];
    
    return $names[$miniAppId] ?? ucfirst($miniAppId);
}
