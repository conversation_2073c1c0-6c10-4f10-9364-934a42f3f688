<?php
/**
 * Chat Bot - Activity Logging API
 *
 * This endpoint handles activity logging for the Chat Bot app
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get the app instance
    $app = $GLOBALS['chat_bot_app'];
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $action = $input['action'] ?? 'chatbot.activity';
    $description = $input['description'] ?? 'Chat Bot activity logged';
    $data = $input['data'] ?? [];
    
    // Add user context
    $data['user_id'] = $_SESSION['user_id'];
    $data['username'] = $_SESSION['username'];
    $data['timestamp'] = date('Y-m-d H:i:s');
    
    // Log the activity
    $app->logActivity($action, $description, $data);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Activity logged successfully'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
