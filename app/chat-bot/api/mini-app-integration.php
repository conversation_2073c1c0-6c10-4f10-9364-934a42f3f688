<?php
/**
 * Chat Bot - Mini App Integration API
 * 
 * This endpoint handles integration requests between mini-apps and the chat bot.
 */

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../config.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';
require_once __DIR__ . '/../includes/mini-app-integration.php';

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get the app instance
    $app = $GLOBALS['chat_bot_app'];
    $dataStorage = $app->getDataStorage();
    $aiIntegration = $app->getAIIntegration();
    $userId = $_SESSION['user_id'];
    
    // Create integration instance
    $integration = new MiniAppIntegration($dataStorage, $aiIntegration, $userId);
    
    // Handle different HTTP methods
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            handlePostRequest($integration);
            break;
            
        case 'GET':
            handleGetRequest($integration);
            break;
            
        case 'DELETE':
            handleDeleteRequest($integration);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}

/**
 * Handle POST requests
 */
function handlePostRequest($integration) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'send_to_chat':
            $result = $integration->sendToChat(
                $input['mini_app_id'] ?? '',
                $input['content'] ?? '',
                $input['content_type'] ?? 'text',
                $input['metadata'] ?? []
            );
            echo json_encode($result);
            break;
            
        case 'register_mini_app':
            $result = $integration->registerMiniApp(
                $input['mini_app_id'] ?? '',
                $input['capabilities'] ?? []
            );
            echo json_encode($result);
            break;
            
        case 'invoke_tool':
            $result = $integration->invokeTool(
                $input['mini_app_id'] ?? '',
                $input['tool_name'] ?? '',
                $input['parameters'] ?? []
            );
            echo json_encode($result);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Handle GET requests
 */
function handleGetRequest($integration) {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_tools':
            $tools = $integration->getAvailableTools();
            echo json_encode([
                'success' => true,
                'tools' => $tools
            ]);
            break;
            
        case 'get_status':
            $miniAppId = $_GET['mini_app_id'] ?? '';
            if (empty($miniAppId)) {
                http_response_code(400);
                echo json_encode(['error' => 'Mini app ID required']);
                return;
            }
            
            $status = $integration->getIntegrationStatus($miniAppId);
            echo json_encode([
                'success' => true,
                'status' => $status
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

/**
 * Handle DELETE requests
 */
function handleDeleteRequest($integration) {
    $miniAppId = $_GET['mini_app_id'] ?? '';
    
    if (empty($miniAppId)) {
        http_response_code(400);
        echo json_encode(['error' => 'Mini app ID required']);
        return;
    }
    
    $result = $integration->removeMiniAppIntegration($miniAppId);
    echo json_encode($result);
}
