<?php
/**
 * Simple Debug Test
 */

echo "Starting debug test...\n";

// Test 1: Check if files exist
$files = [
    __DIR__ . '/includes/app.php',
    __DIR__ . '/includes/mini-app-integration.php',
    __DIR__ . '/includes/data-storage.php',
    __DIR__ . '/includes/ai-integration.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✓ File exists: " . basename($file) . "\n";
    } else {
        echo "✗ File missing: " . basename($file) . "\n";
    }
}

// Test 2: Try to include files
try {
    require_once __DIR__ . '/../../config.php';
    echo "✓ Config loaded\n";
} catch (Exception $e) {
    echo "✗ Config error: " . $e->getMessage() . "\n";
}

try {
    require_once __DIR__ . '/includes/app.php';
    echo "✓ App class loaded\n";
} catch (Exception $e) {
    echo "✗ App class error: " . $e->getMessage() . "\n";
}

try {
    require_once __DIR__ . '/includes/mini-app-integration.php';
    echo "✓ Integration class loaded\n";
} catch (Exception $e) {
    echo "✗ Integration class error: " . $e->getMessage() . "\n";
}

// Test 3: Check if global app exists
if (isset($GLOBALS['chat_bot_app'])) {
    echo "✓ Global app instance exists\n";
} else {
    echo "✗ Global app instance missing\n";
    
    // Try to create it
    try {
        $config = include __DIR__ . '/config.json';
        if ($config) {
            echo "✓ Config loaded from JSON\n";
        } else {
            echo "✗ Failed to load config JSON\n";
        }
    } catch (Exception $e) {
        echo "✗ Config JSON error: " . $e->getMessage() . "\n";
    }
}

echo "Debug test complete.\n";
?>
