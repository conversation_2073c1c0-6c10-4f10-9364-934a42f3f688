<?php
/**
 * Chat Bot - Main Entry Point
 *
 * This is the main entry point for the Chat Bot app, providing
 * AI-powered Swiss Army Knife capabilities with mini-app ecosystem.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../auth.php';
require_once __DIR__ . '/../../includes/theme.php';

// Require authentication
requireAuth();

// Include app-specific files
require_once __DIR__ . '/includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$config = $app->getConfig();

// Handle route - default to chat interface
$route = $_GET['route'] ?? '/';
$templateFile = $app->handleRoute($route);

// If route returns a different template, include it instead
if ($templateFile !== $app->getAppPath() . '/index.php') {
    include $templateFile;
    exit;
}

// If we're on the default route, redirect to chat
if ($route === '/') {
    header('Location: ?route=/chat');
    exit;
}

// Set page title
$pageTitle = $config['app']['name'];

// Include dashboard header
include __DIR__ . '/../../templates/header.php';
?>

<div class="space-y-6">
    <!-- App Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('ai-robot', 'text-3xl'); ?> <?php echo htmlspecialchars($config['app']['name']); ?>
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                <?php echo htmlspecialchars($config['app']['description']); ?>
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="../../dashboard.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to Dashboard
            </a>
            <a href="?route=/settings" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Settings
            </a>
        </div>
    </div>

    <!-- App Status -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">App Status</h2>
        </div>
        
        <div class="p-6">
            <?php
            $status = $app->getStatus();
            $health = $status['health'];
            $healthColor = $health['status'] === 'healthy' ? 'green' : ($health['status'] === 'unhealthy' ? 'red' : 'yellow');
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-<?php echo $healthColor; ?>-600 dark:text-<?php echo $healthColor; ?>-400">
                        <?php echo ucfirst($health['status']); ?>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Health Status</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo $status['version']; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Version</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        <?php echo count($status['features']); ?>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Features</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        <?php echo date('H:i', strtotime($status['last_activity'])); ?>
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Last Activity</div>
                </div>
            </div>
            
            <!-- Health Checks -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?php foreach ($health['checks'] as $check => $result): ?>
                    <div class="flex items-center space-x-3">
                        <div class="text-lg">
                            <?php
                            if ($result === 'ok') {
                                echo getIcon('success-check', 'text-lg');
                            } elseif ($result === 'error') {
                                echo getIcon('error-x', 'text-lg');
                            } else {
                                echo getIcon('warning-alert', 'text-lg');
                            }
                            ?>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white"><?php echo ucfirst($check); ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-300"><?php echo ucfirst($result); ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- App Features -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <!-- AI Chat -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 flex flex-col h-full">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="text-2xl"><?php echo getIcon('ai-robot', 'text-2xl'); ?></div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">AI Chat</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                    Interactive AI chat interface with conversation history and context awareness.
                </p>
                <a href="?route=/chat" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full text-center block mt-auto">
                    Open Chat
                </a>
            </div>
        </div>

        <!-- Data Management -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 flex flex-col h-full">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="text-2xl"><?php echo getIcon('file-storage', 'text-2xl'); ?></div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Flat File Storage</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                    Store and manage app-specific data with full CRUD operations and search.
                </p>
                <a href="?route=/data" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full text-center block mt-auto">
                    Manage Data
                </a>
            </div>
        </div>

        <!-- Analytics -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 flex flex-col h-full">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="text-2xl"><?php echo getIcon('activity-chart', 'text-2xl'); ?></div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Analytics</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                    View app usage statistics, AI interaction metrics, and performance data.
                </p>
                <a href="?route=/analytics" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full text-center block mt-auto">
                    View Analytics
                </a>
            </div>
        </div>
    </div>

    <!-- Integration Examples -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Integration Examples</h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Dashboard Integration -->
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-3">Dashboard Integration</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                        <li>✅ Authentication system integration</li>
                        <li>✅ Theme and styling consistency</li>
                        <li>✅ Update system registration</li>
                        <li>✅ Backup and restore functionality</li>
                        <li>✅ Activity logging integration</li>
                        <li>✅ Menu item registration</li>
                    </ul>
                </div>

                <!-- AI Integration -->
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-3">AI Integration</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                        <li>✅ AI API configuration sharing</li>
                        <li>✅ Multiple AI provider support</li>
                        <li>✅ Context-aware conversations</li>
                        <li>✅ Token usage tracking</li>
                        <li>✅ Model selection and switching</li>
                        <li>✅ Error handling and fallbacks</li>
                    </ul>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2"><?php echo getIcon('rocket-deploy', 'text-base'); ?> Development Notes</h4>
                <p class="text-sm text-blue-800 dark:text-blue-200">
                    This Chat Bot provides comprehensive AI-powered capabilities with an extensible mini-app ecosystem.
                    It demonstrates advanced AI integration patterns and serves as a production-ready assistant for various tasks.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- App-specific JavaScript -->
<script>
// Sample app initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sample App initialized');
    
    // Log app activity
    fetch('api/activity.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            action: 'app.viewed',
            data: { route: '<?php echo htmlspecialchars($route); ?>' }
        })
    });
});
</script>

<?php include __DIR__ . '/../../templates/footer.php'; ?>
