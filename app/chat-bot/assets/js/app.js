/**
 * Chat Bot - Main JavaScript
 *
 * Core JavaScript functionality for the Chat Bot app
 */

// App namespace
window.ChatBot = {
    config: {},
    api: {},
    ui: {},
    utils: {}
};

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    ChatBot.init();
});

/**
 * Initialize the Chat Bot app
 */
ChatBot.init = function() {
    // Load configuration
    this.loadConfig();

    // Initialize API client
    this.api.init();

    // Initialize UI components
    this.ui.init();

    // Log app start
    this.logActivity('chatbot.initialized', 'Chat Bot app initialized');
};

/**
 * Load app configuration
 */
ChatBot.loadConfig = function() {
    // Default configuration
    this.config = {
        apiBaseUrl: 'api/',
        debug: false,
        autoSave: true,
        theme: 'auto'
    };

    // Load from localStorage if available
    const savedConfig = localStorage.getItem('chatBot.config');
    if (savedConfig) {
        try {
            Object.assign(this.config, JSON.parse(savedConfig));
        } catch (e) {
            console.warn('Failed to load saved config:', e);
        }
    }
};

/**
 * Save configuration
 */
ChatBot.saveConfig = function() {
    localStorage.setItem('chatBot.config', JSON.stringify(this.config));
};

/**
 * API Client
 */
ChatBot.api.init = function() {
    this.baseUrl = ChatBot.config.apiBaseUrl;
};

/**
 * Make API request
 */
ChatBot.api.request = async function(endpoint, options = {}) {
    const url = this.baseUrl + endpoint;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const requestOptions = Object.assign(defaultOptions, options);

    try {
        const response = await fetch(url, requestOptions);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}`);
        }

        return data;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
};

/**
 * Chat API methods
 */
ChatBot.api.chat = {
    send: function(message, conversationId = null, context = {}) {
        return ChatBot.api.request('chat.php', {
            method: 'POST',
            body: JSON.stringify({
                message: message,
                conversation_id: conversationId,
                context: context
            })
        });
    },

    getConversations: function(limit = 20) {
        return ChatBot.api.request(`chat.php?limit=${limit}`);
    },

    getConversation: function(conversationId, limit = 50) {
        return ChatBot.api.request(`chat.php?conversation_id=${conversationId}&limit=${limit}`);
    }
};

/**
 * Knowledge Base API methods
 */
ChatBot.api.knowledge = {
    get: function(type, key = null) {
        const params = new URLSearchParams({ type });
        if (key) params.append('key', key);
        return ChatBot.api.request(`data.php?${params}`);
    },

    create: function(type, key, value, metadata = {}) {
        return ChatBot.api.request('data.php', {
            method: 'POST',
            body: JSON.stringify({
                type: type,
                key: key,
                value: value,
                metadata: metadata
            })
        });
    },

    update: function(type, key, value, metadata = {}) {
        return ChatBot.api.request('data.php', {
            method: 'PUT',
            body: JSON.stringify({
                type: type,
                key: key,
                value: value,
                metadata: metadata
            })
        });
    },

    delete: function(type, key) {
        const params = new URLSearchParams({ type, key });
        return ChatBot.api.request(`data.php?${params}`, {
            method: 'DELETE'
        });
    }
};

/**
 * UI Components
 */
ChatBot.ui.init = function() {
    this.initTooltips();
    this.initModals();
    this.initNotifications();
};

/**
 * Initialize tooltips
 */
ChatBot.ui.initTooltips = function() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            ChatBot.ui.showTooltip(this, this.dataset.tooltip);
        });

        element.addEventListener('mouseleave', function() {
            ChatBot.ui.hideTooltip();
        });
    });
};

/**
 * Show tooltip
 */
ChatBot.ui.showTooltip = function(element, text) {
    const tooltip = document.createElement('div');
    tooltip.id = 'chat-bot-tooltip';
    tooltip.className = 'absolute z-50 px-2 py-1 text-sm bg-gray-900 text-white rounded shadow-lg';
    tooltip.textContent = text;

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
};

/**
 * Hide tooltip
 */
ChatBot.ui.hideTooltip = function() {
    const tooltip = document.getElementById('chat-bot-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
};

/**
 * Initialize modals
 */
ChatBot.ui.initModals = function() {
    // Modal functionality would go here
};

/**
 * Show notification
 */
ChatBot.ui.showNotification = function(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-black' :
        'bg-blue-500 text-white'
    }`;

    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }
};

/**
 * Initialize notifications
 */
ChatBot.ui.initNotifications = function() {
    // Notification system initialization
};

/**
 * Utility functions
 */
ChatBot.utils.formatDate = function(date) {
    return new Date(date).toLocaleDateString();
};

ChatBot.utils.formatTime = function(date) {
    return new Date(date).toLocaleTimeString();
};

ChatBot.utils.formatDateTime = function(date) {
    return new Date(date).toLocaleString();
};

ChatBot.utils.debounce = function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

ChatBot.utils.throttle = function(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

/**
 * Log activity
 */
ChatBot.logActivity = function(action, description, data = {}) {
    // Send to server if needed
    // This could integrate with the dashboard's activity logging
};

/**
 * Error handling
 */
ChatBot.handleError = function(error, context = '') {
    console.error('Chat Bot Error:', context, error);

    // Show user-friendly error message
    this.ui.showNotification(
        'An error occurred. Please try again.',
        'error'
    );

    // Log error activity
    this.logActivity('chatbot.error', 'Error occurred', {
        error: error.message,
        context: context,
        timestamp: new Date().toISOString()
    });
};

// Global error handler
window.addEventListener('error', function(event) {
    ChatBot.handleError(event.error, 'Global error handler');
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    ChatBot.handleError(event.reason, 'Unhandled promise rejection');
});

// Export for use in other scripts
window.ChatBot = ChatBot;
