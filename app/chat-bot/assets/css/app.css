/**
 * Sample App - Custom Styles
 * 
 * App-specific styles that extend the dashboard theme
 */

/* Chat Interface Styles */
.chat-message {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.chat-input:focus {
    outline: none;
    ring: 2px;
    ring-color: rgb(59 130 246);
}

/* Data Management Styles */
.data-card {
    transition: all 0.2s ease;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Status Indicators */
.status-healthy {
    color: #10b981;
}

.status-warning {
    color: #f59e0b;
}

.status-error {
    color: #ef4444;
}

/* Loading Animations */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .chat-container {
        height: 300px;
    }
    
    .data-grid {
        grid-template-columns: 1fr;
    }
}

/* Dark Mode Adjustments */
.dark .chat-message-user {
    background-color: rgb(37 99 235);
}

.dark .chat-message-assistant {
    background-color: rgb(55 65 81);
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.8);
}

/* App-specific Components */
.app-feature-card {
    border: 1px solid rgba(229, 231, 235, 1);
    transition: all 0.2s ease;
}

.app-feature-card:hover {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.dark .app-feature-card {
    border-color: rgba(75, 85, 99, 1);
}

.dark .app-feature-card:hover {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* Integration Status */
.integration-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.integration-status.active {
    background-color: rgb(220 252 231);
    color: rgb(22 101 52);
}

.dark .integration-status.active {
    background-color: rgb(6 78 59);
    color: rgb(167 243 208);
}

.integration-status.inactive {
    background-color: rgb(254 242 242);
    color: rgb(153 27 27);
}

.dark .integration-status.inactive {
    background-color: rgb(127 29 29);
    color: rgb(252 165 165);
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
