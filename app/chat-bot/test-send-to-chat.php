<?php
/**
 * Test Send to Chat Functionality
 */

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../auth.php';
require_once __DIR__ . '/../../config.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        // Test sending a calculation to chat
        $dataStorage = $app->getDataStorage();
        $userId = $_SESSION['user_id'];
        
        // Create a conversation ID
        $conversationId = 'conv_test_' . uniqid();
        
        // Format the message
        $formattedMessage = "📊 **Calculator Result**\n\n" . 
                           "Expression: `2 + 2`\n" .
                           "Result: **4**";
        
        // Save the message
        $success = $dataStorage->saveChatMessage(
            $userId,
            $conversationId,
            $formattedMessage,
            'Great calculation! 2 + 2 = 4 is correct. Need help with more math?',
            'test',
            0,
            'test_integration',
            ['test' => true]
        );
        
        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => 'Test message sent to chat successfully',
                'conversation_id' => $conversationId,
                'redirect_url' => "?route=/chat&conversation_id=" . $conversationId
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Failed to save message to chat'
            ]);
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Send to Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-bold mb-4">Test Send to Chat</h2>
        
        <button onclick="testSendToChat()" id="testBtn" 
                class="w-full py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
            Test Send to Chat
        </button>
        
        <div id="result" class="mt-4 p-3 rounded-lg hidden"></div>
    </div>

    <script>
        async function testSendToChat() {
            const btn = document.getElementById('testBtn');
            const result = document.getElementById('result');
            
            btn.textContent = 'Testing...';
            btn.disabled = true;
            
            try {
                const response = await fetch('test-send-to-chat.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test: true
                    })
                });
                
                const data = await response.json();
                
                result.className = 'mt-4 p-3 rounded-lg';
                
                if (data.success) {
                    result.className += ' bg-green-100 text-green-800';
                    result.innerHTML = `
                        <strong>Success!</strong><br>
                        ${data.message}<br>
                        <a href="${data.redirect_url}" class="text-blue-600 underline">View in Chat</a>
                    `;
                } else {
                    result.className += ' bg-red-100 text-red-800';
                    result.innerHTML = `
                        <strong>Error:</strong><br>
                        ${data.error}
                    `;
                }
                
                result.classList.remove('hidden');
                
            } catch (error) {
                result.className = 'mt-4 p-3 rounded-lg bg-red-100 text-red-800';
                result.innerHTML = `
                    <strong>Request Error:</strong><br>
                    ${error.message}
                `;
                result.classList.remove('hidden');
            } finally {
                btn.textContent = 'Test Send to Chat';
                btn.disabled = false;
            }
        }
    </script>
</body>
</html>
