<?php
/**
 * Chat Bot - Main Chat Interface
 *
 * This is the primary interface for the Chat Bot app, providing
 * AI-powered Swiss Army Knife capabilities with conversation management.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$config = $app->getConfig();
$dataStorage = $app->getDataStorage();

// Get user personalization settings
$userId = $_SESSION['user_id'];
$botName = $dataStorage->getSetting('bot_name', $userId) ?? 'Chat Bot';
$userName = $dataStorage->getSetting('user_name', $userId) ?? '';

// Set page title
$pageTitle = $config['app']['name'];

// Include dashboard header
include __DIR__ . '/../../../templates/header.php';
?>

<div class="space-y-6">
    <!-- Chat Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('ai-robot', 'text-3xl'); ?> <?php echo htmlspecialchars($botName); ?>
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                Your AI-powered assistant - ready to help with anything
            </p>
        </div>
        <div class="flex space-x-3">
            <button onclick="newConversation()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                New Chat
            </button>
            <a href="?route=/settings" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Settings
            </a>
        </div>
    </div>

    <!-- Chat Interface -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <!-- Chat Messages -->
        <div id="chatMessages" class="h-96 overflow-y-auto p-6 border-b <?php echo getThemeComponentClasses('divider'); ?>">
            <div id="welcomeMessage" class="text-center py-8">
                <div class="mb-4"><?php echo getIcon('ai-robot', 'text-4xl'); ?></div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    <?php if (!empty($userName)): ?>
                        Hello <?php echo htmlspecialchars($userName); ?>! I'm <?php echo htmlspecialchars($botName); ?>
                    <?php else: ?>
                        Hello! I'm <?php echo htmlspecialchars($botName); ?>
                    <?php endif; ?>
                </h3>
                <p class="text-gray-600 dark:text-gray-300">
                    I'm your AI-powered assistant! I can help with analysis, writing, coding, problem-solving, and much more.
                    Start a conversation and let's see what we can accomplish together.
                </p>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="p-6">
            <div class="flex space-x-4">
                <div class="flex-1">
                    <textarea 
                        id="messageInput" 
                        placeholder="Type your message here..." 
                        class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none <?php echo getThemeComponentClasses('input'); ?>"
                        rows="3"
                        onkeydown="handleKeyDown(event)"
                    ></textarea>
                </div>
                <div class="flex flex-col space-y-2">
                    <button 
                        id="sendButton"
                        onclick="sendMessage()" 
                        class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-3 rounded-lg font-medium transition duration-200"
                    >
                        Send
                    </button>
                    <button 
                        onclick="clearChat()" 
                        class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200 text-sm"
                    >
                        Clear
                    </button>
                </div>
            </div>
            
            <!-- Chat Status -->
            <div id="chatStatus" class="mt-4 text-sm text-gray-600 dark:text-gray-300 hidden">
                <div class="flex items-center space-x-2">
                    <div class="animate-spin">⏳</div>
                    <span>AI is thinking...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversation History -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mt-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?> flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Conversation History</h2>
            <div class="flex space-x-2">
                <button onclick="clearAllConversations()" class="<?php echo getThemeComponentClasses('button-danger'); ?> px-3 py-1 rounded text-sm">
                    Clear All
                </button>
                <button onclick="loadConversations()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-1 rounded text-sm">
                    Refresh
                </button>
            </div>
        </div>
        
        <div class="p-6">
            <div id="conversationList" class="space-y-3">
                <div class="text-center py-4 text-gray-600 dark:text-gray-300">
                    Loading conversations...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chat JavaScript -->
<script>
let currentConversationId = null;
let isLoading = false;

// Icon HTML for JavaScript use with consistent sizing
const copyIcon = '<span class="inline-flex items-center justify-center w-3 h-3 text-xs align-middle mr-1"><?php echo addslashes(getIcon("clipboard-copy", "w-3 h-3")); ?></span>';
const successIcon = '<span class="inline-flex items-center justify-center w-3 h-3 text-xs align-middle mr-1"><?php echo addslashes(getIcon("success-check", "w-3 h-3")); ?></span>';

// Initialize chat
document.addEventListener('DOMContentLoaded', function() {
    loadConversations();

    // Check if there's a conversation_id in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const conversationId = urlParams.get('conversation_id');

    if (conversationId) {
        // Load the specific conversation
        setTimeout(() => {
            loadConversation(conversationId);
        }, 500); // Small delay to ensure conversations list loads first
    }

    // Auto-refresh conversations every 5 seconds to catch new messages from mini-apps
    setInterval(() => {
        loadConversations();
    }, 5000);
});

// Handle keyboard shortcuts
function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Send message to AI
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message || isLoading) return;
    
    isLoading = true;
    updateUI();
    
    // Add user message to chat
    addMessageToChat('user', message);
    messageInput.value = '';
    
    try {
        const response = await fetch('api/chat.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                message: message,
                conversation_id: currentConversationId,
                context: {
                    timestamp: new Date().toISOString(),
                    interface: 'chat_template'
                }
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
            // Add AI response to chat
            addMessageToChat('assistant', data.response);
            currentConversationId = data.conversation_id;

            // Update conversation list
            loadConversations();
        } else {
            addMessageToChat('error', 'Error: ' + data.error);
        }
        
    } catch (error) {
        if (error.message.includes('504')) {
            addMessageToChat('error', 'Server timeout. Please try again with a shorter request.');
        } else if (error.message.includes('Unexpected token')) {
            addMessageToChat('error', 'Server error. Please check the server logs and try again.');
        } else {
            addMessageToChat('error', 'Network error: ' + error.message);
        }
    }

    isLoading = false;
    updateUI();
}

// Add message to chat display
function addMessageToChat(type, content) {
    const chatMessages = document.getElementById('chatMessages');
    const welcomeMessage = document.getElementById('welcomeMessage');

    // Hide welcome message
    if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `mb-4 ${type === 'user' ? 'text-right' : 'text-left'}`;

    const messageContent = document.createElement('div');
    messageContent.className = `inline-block max-w-xs lg:max-w-2xl px-4 py-2 rounded-lg ${
        type === 'user'
            ? 'bg-blue-600 text-white'
            : type === 'error'
            ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            : type === 'system'
            ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
            : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
    }`;

    // Format content with proper code block handling
    messageContent.innerHTML = formatMessageContent(content);
    messageDiv.appendChild(messageContent);

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Format message content with code blocks and other formatting
function formatMessageContent(content) {
    // First, preserve code blocks by replacing them with placeholders
    const codeBlocks = [];
    content = content.replace(/```(\w+)?\n?([\s\S]*?)```/g, function(match, language, code) {
        const lang = language || 'text';
        const codeId = 'code-' + Math.random().toString(36).substr(2, 9);

        // Clean and format the code properly
        let formattedCode = code.trim();

        // For CSS, HTML, JavaScript - apply comprehensive formatting
        if (lang === 'css' || lang === 'html' || lang === 'javascript' || lang === 'js') {
            formattedCode = formatCodeBlock(formattedCode, lang);
        }

        const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`;
        codeBlocks.push(`<div class="my-3">
            <div class="bg-gray-800 text-gray-200 px-2 py-1 text-xs font-mono rounded-t border-l-4 border-blue-500 flex justify-between items-center">
                <span class="pl-1">${lang}</span>
                <button onclick="copyCode('${codeId}')" class="text-gray-400 hover:text-white text-xs px-2 py-1 rounded hover:bg-gray-700 transition-colors">
                    ${copyIcon} Copy
                </button>
            </div>
            <pre class="bg-gray-900 text-gray-100 p-3 rounded-b overflow-x-auto border-l-4 border-blue-500"><code id="${codeId}" class="language-${lang}">${escapeHtml(formattedCode)}</code></pre>
        </div>`);
        return placeholder;
    });

    // Handle inline code (`code`)
    content = content.replace(/`([^`]+)`/g, function(match, code) {
        return `<code class="bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm font-mono">${escapeHtml(code)}</code>`;
    });

    // Handle bold text (**text**)
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Handle italic text (*text*)
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Split content into paragraphs by double line breaks
    const paragraphs = content.split(/\n\s*\n/);

    // Process each paragraph
    const processedParagraphs = paragraphs.map(paragraph => {
        paragraph = paragraph.trim();
        if (!paragraph) return '';

        // If paragraph contains a code block placeholder, return as-is
        if (paragraph.includes('__CODE_BLOCK_')) {
            return paragraph;
        }

        // For regular text paragraphs, replace single line breaks with spaces
        // This preserves the text flow without adding BR tags
        paragraph = paragraph.replace(/\n/g, ' ');

        return `<p class="mb-2">${paragraph}</p>`;
    });

    // Join paragraphs and remove empty ones
    let result = processedParagraphs.filter(p => p.trim()).join('');

    // Restore code blocks
    codeBlocks.forEach((block, index) => {
        result = result.replace(`__CODE_BLOCK_${index}__`, block);
    });

    return result;
}

// Format code blocks with proper indentation
function formatCodeBlock(code, language) {
    // Normalize line breaks and clean up the code
    let cleanCode = code
        .replace(/\r\n/g, '\n')  // Normalize line endings
        .replace(/\r/g, '\n')    // Handle old Mac line endings
        .trim();

    // Split into lines and remove empty lines, but preserve structure
    let lines = cleanCode.split('\n').map(line => line.trim());

    // Remove completely empty lines but keep lines with just whitespace as structure markers
    lines = lines.filter((line, index) => {
        // Keep non-empty lines
        if (line.length > 0) return true;

        // Keep empty lines that are between content (not at start/end)
        if (index > 0 && index < lines.length - 1) {
            const prevLine = lines[index - 1];
            const nextLine = lines[index + 1];
            return prevLine.length > 0 && nextLine.length > 0;
        }

        return false;
    });

    if (language === 'css') {
        return formatCSS(lines);
    } else if (language === 'html') {
        return formatHTML(lines);
    } else if (language === 'javascript' || language === 'js') {
        return formatJavaScript(lines);
    }

    // Default formatting for other languages
    return lines.join('\n');
}

// Format CSS with proper indentation
function formatCSS(lines) {
    let formatted = [];
    let indentLevel = 0;
    const indent = '    '; // 4 spaces

    for (let line of lines) {
        // Handle closing braces
        if (line.includes('}')) {
            indentLevel = Math.max(0, indentLevel - 1);
            formatted.push(indent.repeat(indentLevel) + line);
            if (!line.includes('{')) {
                formatted.push(''); // Add blank line after CSS rule
            }
            continue;
        }

        // Add current line with proper indentation
        formatted.push(indent.repeat(indentLevel) + line);

        // Handle opening braces
        if (line.includes('{')) {
            indentLevel++;
        }
    }

    return formatted.filter(line => line !== undefined).join('\n');
}

// Format HTML with proper indentation using a more robust approach
function formatHTML(lines) {
    // Join all lines and re-parse the HTML structure
    const htmlContent = lines.join(' ').replace(/\s+/g, ' ').trim();

    // Use a simple HTML parser approach
    return parseAndFormatHTML(htmlContent);
}

// Parse and format HTML content properly
function parseAndFormatHTML(html) {
    const indent = '    '; // 4 spaces
    let formatted = [];
    let indentLevel = 0;

    // Self-closing tags
    const selfClosingTags = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];

    // Split HTML into tokens (tags and content)
    const tokens = html.match(/<[^>]+>|[^<]+/g) || [];

    for (let token of tokens) {
        token = token.trim();
        if (!token) continue;

        if (token.startsWith('<')) {
            // This is a tag
            if (token.startsWith('</')) {
                // Closing tag
                indentLevel = Math.max(0, indentLevel - 1);
                formatted.push(indent.repeat(indentLevel) + token);
            } else if (token.startsWith('<!')) {
                // DOCTYPE, comment, etc.
                formatted.push(indent.repeat(indentLevel) + token);
            } else {
                // Opening tag
                const tagMatch = token.match(/<(\w+)/);
                if (tagMatch) {
                    const tagName = tagMatch[1].toLowerCase();

                    formatted.push(indent.repeat(indentLevel) + token);

                    // Check if this should increase indentation
                    const isSelfClosing = token.endsWith('/>') || selfClosingTags.includes(tagName);

                    if (!isSelfClosing) {
                        indentLevel++;
                    }
                }
            }
        } else {
            // This is content
            if (token.length > 0) {
                formatted.push(indent.repeat(indentLevel) + token);
            }
        }
    }

    return formatted.join('\n');
}

// Format JavaScript with proper indentation
function formatJavaScript(lines) {
    let formatted = [];
    let indentLevel = 0;
    const indent = '    '; // 4 spaces

    for (let line of lines) {
        // Handle closing braces
        if (line.includes('}')) {
            indentLevel = Math.max(0, indentLevel - 1);
        }

        // Add current line with proper indentation
        formatted.push(indent.repeat(indentLevel) + line);

        // Handle opening braces
        if (line.includes('{')) {
            indentLevel++;
        }
    }

    return formatted.join('\n');
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Copy code to clipboard
async function copyCode(codeId) {
    const codeElement = document.getElementById(codeId);
    if (!codeElement) return;

    try {
        await navigator.clipboard.writeText(codeElement.textContent);

        // Show feedback
        const button = codeElement.closest('.my-3').querySelector('button');
        const originalText = button.innerHTML;
        button.innerHTML = successIcon + ' Copied!';
        button.classList.add('text-green-400');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-green-400');
        }, 2000);
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = codeElement.textContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show feedback
        const button = codeElement.closest('.my-3').querySelector('button');
        const originalText = button.innerHTML;
        button.innerHTML = successIcon + ' Copied!';
        button.classList.add('text-green-400');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('text-green-400');
        }, 2000);
    }
}

// Clear all conversations
async function clearAllConversations() {
    if (!confirm('Are you sure you want to delete all conversation history? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch('api/chat.php', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'clear_all' })
        });

        const data = await response.json();

        if (data.success) {
            // Clear the conversation list
            document.getElementById('conversationList').innerHTML = '<p class="text-gray-500 dark:text-gray-400 text-center py-4">No conversations yet</p>';

            // Reset current conversation
            currentConversationId = null;

            // Clear chat messages
            document.getElementById('chatMessages').innerHTML = '';

            // Show success message
            alert('All conversation history has been cleared.');
        } else {
            alert('Error clearing conversations: ' + (data.error || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error clearing conversations:', error);
        alert('Error clearing conversations: ' + error.message);
    }
}

// Load conversation history
async function loadConversations() {
    try {
        const response = await fetch('api/chat.php');
        const data = await response.json();
        
        if (data.success) {
            displayConversations(data.conversations);
        }
    } catch (error) {
        console.error('Failed to load conversations:', error);
    }
}

// Display conversations
function displayConversations(conversations) {
    const conversationList = document.getElementById('conversationList');
    
    if (conversations.length === 0) {
        conversationList.innerHTML = `
            <div class="text-center py-4 text-gray-600 dark:text-gray-300">
                No conversations yet. Start chatting to create your first conversation!
            </div>
        `;
        return;
    }
    
    conversationList.innerHTML = conversations.map(conv => `
        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
            <div class="flex-1 cursor-pointer" onclick="loadConversation('${conv.conversation_id}')">
                <div class="font-medium text-gray-900 dark:text-white">
                    ${conv.preview}...
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300">
                    ${conv.message_count} messages • ${new Date(conv.last_activity).toLocaleDateString()}
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <div class="text-sm text-gray-500">
                    ${new Date(conv.last_activity).toLocaleTimeString()}
                </div>
                <button onclick="deleteConversation('${conv.conversation_id}', event)"
                        class="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900 transition-colors"
                        title="Delete conversation">
                    🗑️
                </button>
            </div>
        </div>
    `).join('');
}

// Load specific conversation
async function loadConversation(conversationId) {
    try {
        const response = await fetch(`api/chat.php?conversation_id=${conversationId}`);
        const data = await response.json();

        if (data.success) {
            currentConversationId = conversationId;
            displayConversationMessages(data.messages);
        }
    } catch (error) {
        console.error('Failed to load conversation:', error);
    }
}

// Delete conversation
async function deleteConversation(conversationId, event) {
    // Prevent event bubbling to avoid triggering loadConversation
    event.stopPropagation();

    if (!confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(`api/chat.php?conversation_id=${conversationId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            // If this was the current conversation, clear the chat
            if (currentConversationId === conversationId) {
                newConversation(); // Use the same logic as new conversation
            }

            // Reload conversation list
            loadConversations();

            // Show success message
            addMessageToChat('system', 'Conversation deleted successfully.');
        } else {
            addMessageToChat('error', 'Failed to delete conversation: ' + data.error);
        }
    } catch (error) {
        addMessageToChat('error', 'Network error while deleting conversation: ' + error.message);
    }
}

// Display conversation messages
function displayConversationMessages(messages) {
    const chatMessages = document.getElementById('chatMessages');
    const welcomeMessage = document.getElementById('welcomeMessage');
    
    // Clear current messages
    chatMessages.innerHTML = '';
    
    if (messages.length === 0) {
        chatMessages.appendChild(welcomeMessage);
        return;
    }
    
    messages.forEach(msg => {
        addMessageToChat('user', msg.message);
        if (msg.response) {
            addMessageToChat('assistant', msg.response);
        }
    });
}

// Start new conversation
function newConversation() {
    currentConversationId = null;
    const chatMessages = document.getElementById('chatMessages');

    // Clear all messages and recreate welcome message
    chatMessages.innerHTML = `
        <div id="welcomeMessage" class="text-center py-8">
            <div class="mb-4"><?php echo getIcon('ai-robot', 'text-4xl'); ?></div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                <?php if (!empty($userName)): ?>
                    Hello <?php echo htmlspecialchars($userName); ?>! I'm <?php echo htmlspecialchars($botName); ?>
                <?php else: ?>
                    Hello! I'm <?php echo htmlspecialchars($botName); ?>
                <?php endif; ?>
            </h3>
            <p class="text-gray-600 dark:text-gray-300">I'm your AI-powered assistant! I can help with analysis, writing, coding, problem-solving, and much more.</p>
        </div>
    `;
}

// Clear current chat
function clearChat() {
    if (confirm('Are you sure you want to clear the current chat?')) {
        newConversation();
    }
}

// Update UI based on loading state
function updateUI() {
    const sendButton = document.getElementById('sendButton');
    const chatStatus = document.getElementById('chatStatus');
    const messageInput = document.getElementById('messageInput');

    if (isLoading) {
        sendButton.disabled = true;
        sendButton.textContent = 'Sending...';
        chatStatus.classList.remove('hidden');
        messageInput.disabled = true;
    } else {
        sendButton.disabled = false;
        sendButton.textContent = 'Send';
        chatStatus.classList.add('hidden');
        messageInput.disabled = false;
        messageInput.focus();
    }
}

// Show Applets
function showApplets() {
    // Detect current theme
    const isDarkMode = document.documentElement.classList.contains('dark') ||
                      document.body.classList.contains('dark-mode') ||
                      localStorage.getItem('theme') === 'dark';

    const theme = isDarkMode ? 'dark' : 'light';
    const popup = window.open(`?route=/applets&popup=true&theme=${theme}`, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');

    // Store reference to this window in the popup for communication
    if (popup) {
        popup.chatBotWindow = window;
    }
}

// Function to receive messages from mini-apps
async function addMessageFromMiniApp(message) {
    // Add the message to the current chat
    addMessageToChat('user', message);

    // Focus the chat window to bring it to the front
    window.focus();

    // Scroll to the bottom to show the new message
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Trigger AI response without adding user message again
    await sendAIResponse(message);
}

// Send message to AI without adding user message (for mini-app integration)
async function sendAIResponse(message) {
    if (isLoading) return;

    isLoading = true;
    updateUI();

    try {
        const response = await fetch('api/chat.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                message: message,
                conversation_id: currentConversationId,
                context: {
                    timestamp: new Date().toISOString(),
                    interface: 'mini_app_integration'
                }
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
            // Add AI response to chat
            addMessageToChat('assistant', data.response);
            currentConversationId = data.conversation_id;

            // Update conversation list
            loadConversations();
        } else {
            addMessageToChat('error', 'Error: ' + data.error);
        }

    } catch (error) {
        if (error.message.includes('504')) {
            addMessageToChat('error', 'Server timeout. Please try again with a shorter request.');
        } else if (error.message.includes('Unexpected token')) {
            addMessageToChat('error', 'Server error. Please check the server logs and try again.');
        } else {
            addMessageToChat('error', 'Network error: ' + error.message);
        }
    }

    isLoading = false;
    updateUI();
}
</script>

<?php include __DIR__ . '/../../../templates/footer.php'; ?>
