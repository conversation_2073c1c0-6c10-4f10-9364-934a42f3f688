<?php
/**
 * Chat <PERSON>t - Knowledge Base Management
 *
 * This template provides knowledge base and data management
 * capabilities for the Chat Bot, allowing users to store and
 * organize information that can be accessed during conversations.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$config = $app->getConfig();

// Set page title
$pageTitle = $config['app']['name'] . ' - Knowledge Base';

// Include dashboard header
include __DIR__ . '/../../../templates/header.php';
?>

<div class="space-y-6">
    <!-- Knowledge Base Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('file-storage', 'text-3xl'); ?> Knowledge Base
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                Store and organize information that your AI assistant can access during conversations
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="?route=/chat" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to Chat
            </a>
            <button onclick="showAddDataModal()" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Add Knowledge
            </button>
        </div>
    </div>

    <!-- Knowledge Categories -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Knowledge Categories</h2>
        </div>

        <div class="p-6">
            <div class="flex flex-wrap gap-3">
                <button onclick="loadDataType('facts', this)" class="data-type-btn active px-4 py-2 rounded-lg font-medium transition duration-200 bg-blue-600 text-white">
                    Facts & Info
                </button>
                <button onclick="loadDataType('notes', this)" class="data-type-btn px-4 py-2 rounded-lg font-medium transition duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    Notes
                </button>
                <button onclick="loadDataType('references', this)" class="data-type-btn px-4 py-2 rounded-lg font-medium transition duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    References
                </button>
                <button onclick="loadDataType('templates', this)" class="data-type-btn px-4 py-2 rounded-lg font-medium transition duration-200 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    Templates
                </button>
            </div>
        </div>
    </div>

    <!-- Data Display -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?> flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Knowledge Items</h2>
            <div class="flex space-x-3">
                <input type="text" id="searchInput" placeholder="Search knowledge..."
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>"
                       onkeyup="filterData()">
                <button onclick="refreshData()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-2 rounded-lg">
                    Refresh
                </button>
            </div>
        </div>
        
        <div class="p-6">
            <div id="dataContainer">
                <div class="text-center py-8">
                    <div class="mb-4"><?php echo getIcon('file-storage', 'text-4xl'); ?></div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Loading Knowledge...</h3>
                    <p class="text-gray-600 dark:text-gray-300">Please wait while we load your knowledge base.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Data Modal -->
<div id="dataModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?> flex justify-between items-center">
            <h3 id="modalTitle" class="text-lg font-semibold text-gray-900 dark:text-white">Add Knowledge</h3>
            <button onclick="hideDataModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div class="p-6">
            <form id="dataForm" onsubmit="saveData(event)">
                <input type="hidden" id="editKey" value="">
                
                <div class="mb-4">
                    <label for="dataKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Data Key</label>
                    <input type="text" id="dataKey" required 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                </div>
                
                <div class="mb-4">
                    <label for="dataValue" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Data Value (JSON)</label>
                    <textarea id="dataValue" rows="4" required 
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>"
                              placeholder='{"example": "value"}'></textarea>
                </div>
                
                <div class="mb-6">
                    <label for="dataMetadata" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Metadata (Optional)</label>
                    <textarea id="dataMetadata" rows="2" 
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>"
                              placeholder='{"description": "Optional metadata"}'></textarea>
                </div>
                
                <div class="flex space-x-3">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 flex-1">
                        Save Data
                    </button>
                    <button type="button" onclick="hideDataModal()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Data Management JavaScript -->
<script>
let currentDataType = 'general';
let allData = [];

// Initialize knowledge base
document.addEventListener('DOMContentLoaded', function() {
    loadDataType('facts');
});

// Load data by type
async function loadDataType(type, clickedButton = null) {
    currentDataType = type;

    // Update button states
    document.querySelectorAll('.data-type-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-blue-600', 'text-white');
        btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
    });

    // If called from a button click, update that button's state
    if (clickedButton) {
        clickedButton.classList.add('active', 'bg-blue-600', 'text-white');
        clickedButton.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
    } else {
        // If called programmatically, find the button for this type
        const typeButton = document.querySelector(`button[onclick="loadDataType('${type}')"]`);
        if (typeButton) {
            typeButton.classList.add('active', 'bg-blue-600', 'text-white');
            typeButton.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
        }
    }

    await refreshData();
}

// Refresh data
async function refreshData() {
    try {
        const response = await fetch(`api/data.php?type=${currentDataType}`);
        const result = await response.json();
        
        if (result.success) {
            allData = result.data || [];
            displayData(allData);
        } else {
            showError('Failed to load data: ' + result.error);
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
}

// Display data
function displayData(data) {
    const container = document.getElementById('dataContainer');
    
    if (data.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="mb-4"><?php echo getIcon('file-storage', 'text-4xl'); ?></div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Data Found</h3>
                <p class="text-gray-600 dark:text-gray-300">No ${currentDataType} data records found. Click "Add Data" to create your first record.</p>
            </div>
        `;
        return;
    }
    
    const html = data.map(item => `
        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">${item.data_key}</h4>
                    <pre class="text-sm text-gray-600 dark:text-gray-300 mt-2 bg-gray-50 dark:bg-gray-800 p-2 rounded overflow-x-auto">${JSON.stringify(item.data_value, null, 2)}</pre>
                    ${item.metadata && Object.keys(item.metadata).length > 0 ? `
                        <div class="mt-2">
                            <span class="text-xs text-gray-500">Metadata:</span>
                            <pre class="text-xs text-gray-500 bg-gray-50 dark:bg-gray-800 p-1 rounded">${JSON.stringify(item.metadata, null, 2)}</pre>
                        </div>
                    ` : ''}
                    <div class="text-xs text-gray-500 mt-2">
                        Created: ${new Date(item.created_at).toLocaleString()} | 
                        Updated: ${new Date(item.updated_at).toLocaleString()}
                    </div>
                </div>
                <div class="flex space-x-2 ml-4">
                    <button onclick="editData('${item.data_key}')" class="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                    <button onclick="deleteData('${item.data_key}')" class="text-red-600 hover:text-red-800 text-sm">Delete</button>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

// Filter data
function filterData() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const filteredData = allData.filter(item => 
        item.data_key.toLowerCase().includes(searchTerm) ||
        JSON.stringify(item.data_value).toLowerCase().includes(searchTerm)
    );
    displayData(filteredData);
}

// Show add data modal
function showAddDataModal() {
    document.getElementById('modalTitle').textContent = 'Add Data';
    document.getElementById('editKey').value = '';
    document.getElementById('dataKey').value = '';
    document.getElementById('dataValue').value = '';
    document.getElementById('dataMetadata').value = '';
    document.getElementById('dataKey').disabled = false;
    document.getElementById('dataModal').classList.remove('hidden');
}

// Hide data modal
function hideDataModal() {
    document.getElementById('dataModal').classList.add('hidden');
}

// Edit data
function editData(key) {
    const item = allData.find(d => d.data_key === key);
    if (!item) return;
    
    document.getElementById('modalTitle').textContent = 'Edit Data';
    document.getElementById('editKey').value = key;
    document.getElementById('dataKey').value = key;
    document.getElementById('dataValue').value = JSON.stringify(item.data_value, null, 2);
    document.getElementById('dataMetadata').value = JSON.stringify(item.metadata || {}, null, 2);
    document.getElementById('dataKey').disabled = true;
    document.getElementById('dataModal').classList.remove('hidden');
}

// Save data
async function saveData(event) {
    event.preventDefault();
    
    const editKey = document.getElementById('editKey').value;
    const dataKey = document.getElementById('dataKey').value;
    const dataValue = document.getElementById('dataValue').value;
    const dataMetadata = document.getElementById('dataMetadata').value;
    
    try {
        const value = JSON.parse(dataValue);
        const metadata = dataMetadata ? JSON.parse(dataMetadata) : {};
        
        const method = editKey ? 'PUT' : 'POST';
        const response = await fetch('api/data.php', {
            method: method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                type: currentDataType,
                key: dataKey,
                value: value,
                metadata: metadata
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            hideDataModal();
            await refreshData();
            showSuccess(editKey ? 'Data updated successfully' : 'Data created successfully');
        } else {
            showError('Failed to save data: ' + result.error);
        }
    } catch (error) {
        showError('Invalid JSON format: ' + error.message);
    }
}

// Delete data
async function deleteData(key) {
    if (!confirm(`Are you sure you want to delete "${key}"?`)) return;
    
    try {
        const response = await fetch(`api/data.php?type=${currentDataType}&key=${key}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            await refreshData();
            showSuccess('Data deleted successfully');
        } else {
            showError('Failed to delete data: ' + result.error);
        }
    } catch (error) {
        showError('Network error: ' + error.message);
    }
}

// Show success message
function showSuccess(message) {
    // You can integrate with the dashboard's toast system here
    alert(message);
}

// Show error message
function showError(message) {
    // You can integrate with the dashboard's toast system here
    alert('Error: ' + message);
}
</script>

<?php include __DIR__ . '/../../../templates/footer.php'; ?>
