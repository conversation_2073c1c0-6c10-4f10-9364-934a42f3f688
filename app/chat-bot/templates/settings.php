<?php
/**
 * Chat <PERSON>t - Settings Interface
 *
 * This template provides comprehensive Chat Bot configuration
 * including bot personality, user preferences, and AI settings.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$config = $app->getConfig();
$dataStorage = $app->getDataStorage();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save_user_settings') {
        $settings = [
            'bot_name' => $_POST['bot_name'] ?? 'Assistant',
            'user_name' => $_POST['user_name'] ?? '',
            'bot_personality' => $_POST['bot_personality'] ?? 'helpful',
            'response_style' => $_POST['response_style'] ?? 'balanced',
            'response_length' => $_POST['response_length'] ?? 'medium',
            'auto_save_chats' => isset($_POST['auto_save_chats']),
            'show_typing_indicator' => isset($_POST['show_typing_indicator'])
        ];
        
        foreach ($settings as $key => $value) {
            $dataStorage->saveSetting($key, $value, $_SESSION['user_id'], false);
        }
        
        $app->logActivity('settings.user_updated', 'User settings updated', $settings);
        $successMessage = 'User settings saved successfully!';
    }
    
    if ($action === 'save_global_settings' && isAdmin()) {
        $settings = [
            'chat_bot_enabled' => isset($_POST['chat_bot_enabled']),
            'max_conversations' => (int)($_POST['max_conversations'] ?? 50),
            'allow_mini_apps' => isset($_POST['allow_mini_apps']),
            'enable_ai_training' => isset($_POST['enable_ai_training'])
        ];
        
        foreach ($settings as $key => $value) {
            $dataStorage->saveSetting($key, $value, null, true);
        }
        
        $app->logActivity('settings.global_updated', 'Global settings updated', $settings);
        $successMessage = 'Global settings saved successfully!';
    }
}

// Load current settings
$userSettings = [
    'bot_name' => $dataStorage->getSetting('bot_name', $_SESSION['user_id']) ?? 'Chat Bot',
    'user_name' => $dataStorage->getSetting('user_name', $_SESSION['user_id']) ?? '',
    'bot_personality' => $dataStorage->getSetting('bot_personality', $_SESSION['user_id']) ?? 'helpful',
    'response_style' => $dataStorage->getSetting('response_style', $_SESSION['user_id']) ?? 'balanced',
    'response_length' => $dataStorage->getSetting('response_length', $_SESSION['user_id']) ?? 'medium',
    'auto_save_chats' => $dataStorage->getSetting('auto_save_chats', $_SESSION['user_id']) ?? true,
    'show_typing_indicator' => $dataStorage->getSetting('show_typing_indicator', $_SESSION['user_id']) ?? true
];

$globalSettings = [
    'chat_bot_enabled' => $dataStorage->getSetting('chat_bot_enabled') ?? true,
    'max_conversations' => $dataStorage->getSetting('max_conversations') ?? 50,
    'allow_mini_apps' => $dataStorage->getSetting('allow_mini_apps') ?? true,
    'enable_ai_training' => $dataStorage->getSetting('enable_ai_training') ?? true
];

// Set page title
$pageTitle = $config['app']['name'] . ' - Settings';

// Include dashboard header
include __DIR__ . '/../../../templates/header.php';
?>

<div class="space-y-6">
    <!-- Settings Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('settings-gear', 'text-3xl'); ?> Chat Bot Settings
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                Personalize your Chat Bot experience and configure AI behavior
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="?route=/chat" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to Chat
            </a>
        </div>
    </div>

    <?php if (isset($successMessage)): ?>
        <div class="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-200 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($successMessage); ?>
        </div>
    <?php endif; ?>

    <!-- Bot Personalization -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Bot Personalization</h2>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Customize your Chat Bot's personality and behavior</p>
        </div>
        
        <div class="p-6">
            <form method="POST">
                <input type="hidden" name="action" value="save_user_settings">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Bot Name -->
                    <div>
                        <label for="bot_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Bot Name
                        </label>
                        <input type="text" name="bot_name" id="bot_name"
                               value="<?php echo htmlspecialchars($userSettings['bot_name']); ?>"
                               placeholder="Chat Bot"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                    </div>

                    <!-- User Name -->
                    <div>
                        <label for="user_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Your Name (for personalized responses)
                        </label>
                        <input type="text" name="user_name" id="user_name"
                               value="<?php echo htmlspecialchars($userSettings['user_name']); ?>"
                               placeholder="Optional"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                    </div>

                    <!-- Bot Personality -->
                    <div>
                        <label for="bot_personality" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Bot Personality
                        </label>
                        <select name="bot_personality" id="bot_personality"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                            <option value="helpful" <?php echo $userSettings['bot_personality'] === 'helpful' ? 'selected' : ''; ?>>Helpful & Professional</option>
                            <option value="friendly" <?php echo $userSettings['bot_personality'] === 'friendly' ? 'selected' : ''; ?>>Friendly & Casual</option>
                            <option value="technical" <?php echo $userSettings['bot_personality'] === 'technical' ? 'selected' : ''; ?>>Technical & Precise</option>
                            <option value="creative" <?php echo $userSettings['bot_personality'] === 'creative' ? 'selected' : ''; ?>>Creative & Imaginative</option>
                        </select>
                    </div>

                    <!-- Response Style -->
                    <div>
                        <label for="response_style" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Response Style
                        </label>
                        <select name="response_style" id="response_style"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                            <option value="concise" <?php echo $userSettings['response_style'] === 'concise' ? 'selected' : ''; ?>>Concise & Direct</option>
                            <option value="balanced" <?php echo $userSettings['response_style'] === 'balanced' ? 'selected' : ''; ?>>Balanced</option>
                            <option value="detailed" <?php echo $userSettings['response_style'] === 'detailed' ? 'selected' : ''; ?>>Detailed & Comprehensive</option>
                        </select>
                    </div>

                    <!-- Response Length -->
                    <div>
                        <label for="response_length" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Preferred Response Length
                        </label>
                        <select name="response_length" id="response_length"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                            <option value="short" <?php echo $userSettings['response_length'] === 'short' ? 'selected' : ''; ?>>Short (1-2 paragraphs)</option>
                            <option value="medium" <?php echo $userSettings['response_length'] === 'medium' ? 'selected' : ''; ?>>Medium (2-4 paragraphs)</option>
                            <option value="long" <?php echo $userSettings['response_length'] === 'long' ? 'selected' : ''; ?>>Long (4+ paragraphs)</option>
                        </select>
                    </div>
                </div>
                
                <!-- Chat Preferences -->
                <div class="mt-6 space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="auto_save_chats" id="auto_save_chats"
                               <?php echo $userSettings['auto_save_chats'] ? 'checked' : ''; ?>
                               class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="auto_save_chats" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Auto-save chat conversations
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="show_typing_indicator" id="show_typing_indicator"
                               <?php echo $userSettings['show_typing_indicator'] ? 'checked' : ''; ?>
                               class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="show_typing_indicator" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Show typing indicator when AI is responding
                        </label>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200">
                        Save Bot Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Global Chat Bot Settings (Admin Only) -->
    <?php if (isAdmin()): ?>
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Global Chat Bot Settings</h2>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">System-wide Chat Bot configuration (Admin only)</p>
            </div>
            
            <div class="p-6">
                <form method="POST">
                    <input type="hidden" name="action" value="save_global_settings">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Max Conversations -->
                        <div>
                            <label for="max_conversations" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Max Conversations Per User
                            </label>
                            <input type="number" name="max_conversations" id="max_conversations"
                                   value="<?php echo htmlspecialchars($globalSettings['max_conversations']); ?>"
                                   min="1" max="500"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                        </div>
                    </div>
                    
                    <!-- Global Chat Bot Options -->
                    <div class="mt-6 space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="chat_bot_enabled" id="chat_bot_enabled"
                                   <?php echo $globalSettings['chat_bot_enabled'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="chat_bot_enabled" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Enable Chat Bot for all users
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="allow_mini_apps" id="allow_mini_apps"
                                   <?php echo $globalSettings['allow_mini_apps'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="allow_mini_apps" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Allow users to access Mini Apps
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="enable_ai_training" id="enable_ai_training"
                                   <?php echo $globalSettings['enable_ai_training'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="enable_ai_training" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Enable AI training from conversations
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200">
                            Save Global Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- Chat Bot Information -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mt-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Chat Bot Information</h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-900 dark:text-white mb-2">Chat Bot Details</h3>
                    <dl class="space-y-1 text-sm">
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Name:</dt>
                            <dd class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($config['app']['name']); ?></dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Version:</dt>
                            <dd class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($app->getVersion()); ?></dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Type:</dt>
                            <dd class="text-gray-900 dark:text-white">AI-Powered Swiss Army Knife</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Storage:</dt>
                            <dd class="text-gray-900 dark:text-white">Flat File JSON</dd>
                        </div>
                    </dl>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-900 dark:text-white mb-2">Features</h3>
                    <ul class="space-y-1 text-sm">
                        <?php foreach ($config['features'] as $feature => $enabled): ?>
                            <li class="flex items-center">
                                <span class="<?php echo $enabled ? 'text-green-600' : 'text-gray-400'; ?>">
                                    <?php echo $enabled ? '✅' : '❌'; ?>
                                </span>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">
                                    <?php echo ucfirst(str_replace('_', ' ', $feature)); ?>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../../templates/footer.php'; ?>
