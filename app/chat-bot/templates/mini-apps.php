<?php
/**
 * Chat Bot - Mini Apps Framework
 * 
 * This template demonstrates the extensible mini-app ecosystem
 * that can be integrated with the Chat Bot for enhanced functionality.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Include app files with error handling
try {
    require_once __DIR__ . '/../includes/app.php';

    // Get the app instance
    $app = $GLOBALS['chat_bot_app'] ?? null;
    $config = $app ? $app->getConfig() : ['app' => ['name' => 'Chat Bot']];
} catch (Exception $e) {
    // Fallback configuration if app fails to load
    error_log("Chat Bot app failed to load: " . $e->getMessage());
    $app = null;
    $config = ['app' => ['name' => 'Chat Bot']];
}

// Check if this is being loaded in popup mode
$isPopup = isset($_GET['popup']) && $_GET['popup'] === 'true';

// Get theme from URL parameter or fallback to current theme
$urlTheme = $_GET['theme'] ?? null;
if ($urlTheme && in_array($urlTheme, ['light', 'dark'])) {
    $currentTheme = $urlTheme;
} else {
    $currentTheme = function_exists('getCurrentTheme') ? getCurrentTheme() : 'dark';
}

// Set page title
$pageTitle = $config['app']['name'] . ' - Mini Apps';

// Include dashboard header only if not in popup mode
if (!$isPopup) {
    include __DIR__ . '/../../../templates/header.php';
} else {
    // Minimal header for popup mode with error handling
    try {
        $bodyClasses = function_exists('getThemeComponentClasses') ? getThemeComponentClasses('body') : 'bg-gray-100 dark:bg-gray-900';
    } catch (Exception $e) {
        $bodyClasses = 'bg-gray-100 dark:bg-gray-900';
    }
    ?>
    <!DOCTYPE html>
    <html lang="en" class="<?php echo $currentTheme; ?>">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($pageTitle); ?></title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                darkMode: 'class',
                theme: {
                    extend: {}
                }
            }
        </script>
        <style>
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            }
        </style>
    </head>
    <body class="<?php echo $bodyClasses; ?>">
    <?php
}
?>

<div class="space-y-6">
    <!-- Mini Apps Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                📱 Mini Apps
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">
                Extensible mini-applications that enhance your AI assistant capabilities
            </p>
        </div>
        <div class="flex space-x-3">
            <button onclick="showCreateMiniApp()" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Create Mini App
            </button>
        </div>
    </div>

    <!-- Available Mini Apps -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Calculator Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🧮</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Calculator</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Basic calculator with scientific functions
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('calculator')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('calculator')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Note Taker Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">📝</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Notes</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Fast note-taking with auto-save functionality
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('notes')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('notes')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Unit Converter Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🔄</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Unit Converter</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Convert between different units of measurement
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('converter')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('converter')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Color Palette Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🎨</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Color Palette</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Generate and manage color palettes for design
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('colors')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('colors')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Password Generator Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🔐</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Password Generator</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Generate secure passwords with custom criteria
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('password')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('password')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Create New Mini App Card -->
        <div class="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-500 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6 text-center">
                <div class="text-4xl mb-4">➕</div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Create New</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                    Build your own mini-app with our framework
                </p>
                <button onclick="showCreateMiniApp()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    Get Started
                </button>
            </div>
        </div>
    </div>

    <!-- Mini App Framework Info -->
    <div class="mini-app-card rounded-lg shadow">
        <div class="px-6 py-4" style="background-color: #4b5563 !important;">
            <h2 class="text-xl font-semibold text-white">Mini App Framework</h2>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-white mb-2">Features</h3>
                    <ul class="mini-app-text space-y-1 text-sm">
                        <li>• Popup-based interface for non-intrusive operation</li>
                        <li>• Chat Bot integration for seamless workflow</li>
                        <li>• Extensible architecture for custom development</li>
                        <li>• Shared data access with knowledge base</li>
                        <li>• Theme-aware styling and responsive design</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-white mb-2">Development</h3>
                    <ul class="mini-app-text space-y-1 text-sm">
                        <li>• Simple HTML/CSS/JavaScript structure</li>
                        <li>• Access to Chat Bot API for AI integration</li>
                        <li>• Built-in data persistence and user settings</li>
                        <li>• Documentation and examples provided</li>
                        <li>• Hot-reload development environment</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mini App Container with Tabbed Interface -->
<div id="miniAppContainer" class="<?php echo $isPopup ? '' : 'hidden'; ?> fixed inset-0 bg-black bg-opacity-50 z-50">
    <div class="bg-gray-100 dark:bg-gray-900 w-full h-full flex flex-col">

        <!-- Mini App Header with Navigation -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div class="flex flex-wrap items-center gap-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Mini Apps</h3>

                <!-- View Toggle -->
                <div class="flex bg-blue-100 rounded-lg p-1">
                    <button id="homeViewBtn" onclick="showHomeView()" class="px-3 py-1 rounded text-sm font-medium bg-blue-500 text-white shadow-sm">
                        Home
                    </button>
                    <button id="appsViewBtn" onclick="showAppsView()" class="px-3 py-1 rounded text-sm font-medium text-blue-600 hover:text-blue-800">
                        Apps
                    </button>
                </div>

                <!-- App Tabs (shown when apps are open) -->
                <div id="tabList" class="flex flex-wrap gap-1">
                    <!-- Tabs will be dynamically added here -->
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="flex-1 overflow-hidden" style="min-height: 600px; position: relative;">
            <!-- Home View (Mini Apps Grid) -->
            <div id="homeView" class="h-full overflow-y-auto p-6">
                <div class="max-w-6xl mx-auto">
                    <!-- This will contain the same mini-apps grid as the main page -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="miniAppsGrid">
                        <!-- Mini apps will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Apps View (Tabbed Interface) -->
            <div id="appsView" class="h-full bg-gray-100 dark:bg-gray-900" style="display: none;">
                <div id="appTabs" class="h-full">
                    <!-- Individual app content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Mini App Modal -->
<div id="createMiniAppModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Create New Mini App</h2>
                <button onclick="closeCreateMiniApp()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="p-6">
            <form id="createMiniAppForm">
                <!-- Basic Information -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="appName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">App Name</label>
                            <input type="text" id="appName" name="appName" required
                                   class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                   placeholder="My Custom App">
                        </div>
                        <div>
                            <label for="appIcon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Icon (Emoji)</label>
                            <input type="text" id="appIcon" name="appIcon" required
                                   class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                   placeholder="🚀" maxlength="2">
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="appDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description and Functionality</label>
                        <textarea id="appDescription" name="appDescription" rows="2" required
                                  class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                  placeholder="Describe what your app does and what functionality you want it to have"></textarea>
                    </div>
                </div>

                <!-- App Type -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">App Type</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <label class="cursor-pointer">
                            <input type="radio" name="appType" value="form" class="sr-only" checked>
                            <div class="p-4 border-2 border-blue-500 bg-blue-50 dark:bg-blue-900 rounded-lg">
                                <div class="text-2xl mb-2">📝</div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Form App</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Create forms with inputs and outputs</p>
                            </div>
                        </label>
                        <label class="cursor-pointer">
                            <input type="radio" name="appType" value="tool" class="sr-only">
                            <div class="p-4 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 rounded-lg">
                                <div class="text-2xl mb-2">🔧</div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Tool App</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Utility tools and calculators</p>
                            </div>
                        </label>
                        <label class="cursor-pointer">
                            <input type="radio" name="appType" value="display" class="sr-only">
                            <div class="p-4 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 rounded-lg">
                                <div class="text-2xl mb-2">📊</div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Display App</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Data visualization and dashboards</p>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Form Fields (shown for form type) -->
                <div id="formFieldsSection" class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Form Fields</h3>
                    <div id="formFieldsList" class="space-y-3">
                        <!-- Form fields will be added here -->
                    </div>
                    <button type="button" onclick="addFormField()" class="mt-3 px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm">
                        Add Field
                    </button>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button type="button" onclick="closeCreateMiniApp()" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg">
                        Cancel
                    </button>
                    <button type="button" onclick="previewMiniApp()" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg">
                        Preview
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg">
                        Create App
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Code Editor Modal -->
<div id="codeEditorModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full h-full mx-4 my-4 flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Code Editor</h2>
                <p class="text-sm text-gray-600 dark:text-gray-300" id="editorAppName">Editing mini-app.php</p>
            </div>
            <div class="flex space-x-2">
                <button onclick="saveCode()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium">
                    Save
                </button>
                <button onclick="closeCodeEditor()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="flex-1 flex">
            <!-- Code Editor -->
            <div class="flex-1 flex flex-col">
                <div class="px-4 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">mini-app.php</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">Custom app logic</span>
                        </div>
                        <button onclick="copyCode()" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                            Copy
                        </button>
                    </div>
                </div>
                <div class="flex-1 relative">
                    <textarea id="codeEditor" class="w-full h-full p-4 font-mono text-sm border-0 resize-none bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
                              placeholder="Loading code..."></textarea>
                </div>
            </div>

            <!-- AI Prompt Panel -->
            <div class="w-1/3 border-l border-gray-200 dark:border-gray-700 flex flex-col">
                <div class="px-4 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Prompt</span>
                        <button onclick="copyAIPrompt()" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                            Copy
                        </button>
                    </div>
                </div>
                <div class="flex-1 overflow-y-auto p-4">
                    <div id="aiPromptContent" class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        Loading AI prompt...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Mini Apps Tabbed Interface JavaScript
let openApps = new Map(); // Store open apps: appId -> {title, content, active}
let currentView = 'home'; // 'home' or 'apps'
let activeAppId = null;

// Initialize mini apps grid in popup
async function initializeMiniAppsGrid() {
    const grid = document.getElementById('miniAppsGrid');
    if (!grid) return;

    // Load custom apps from registry
    let customAppsHTML = '';
    try {
        const response = await fetch('mini-apps/get-custom-apps.php');

        if (!response.ok) {
            console.warn('Failed to load custom apps:', response.status, response.statusText);
        } else {
            const responseText = await response.text();

            if (!responseText.trim()) {
                console.warn('Empty response from get-custom-apps.php');
            } else {
                try {
                    const customApps = JSON.parse(responseText);

                    if (customApps.success && customApps.apps) {
                        // Store custom apps data for title lookup
                        customAppsData = customApps.apps;

                        customAppsHTML = Object.entries(customApps.apps).map(([appId, app]) => {
                            const isEditable = app.editable && app.structure === 'folder-based';
                            const editButton = isEditable ? `
                                <button onclick="editMiniApp('${appId}')" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm" title="Edit Code">
                                    ✏️
                                </button>` : '';

                            return `
                            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
                                <div class="p-6">
                                    <div class="flex items-center mb-4">
                                        <div class="text-2xl mr-3">${app.icon}</div>
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${app.name}</h3>
                                        ${app.structure === 'folder-based' ? '<span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">New</span>' : '<span class="ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">Legacy</span>'}
                                    </div>
                                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                                        ${app.description}
                                    </p>
                                    <div class="flex space-x-2 flex-wrap gap-2">
                                        <button onclick="openMiniApp('${appId}')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                                            Open
                                        </button>
                                        ${editButton}
                                        <button onclick="integrateMiniApp('${appId}')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                                            Integrate
                                        </button>
                                        <button onclick="deleteCustomApp('${appId}')" class="bg-red-500 hover:bg-red-600 text-white px-2 py-2 rounded-lg text-sm" title="Delete App">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            </div>`;
                        }).join('');
                    }
                } catch (jsonError) {
                    console.error('JSON parsing error:', jsonError);
                    console.log('Response text:', responseText);
                }
            }
        }
    } catch (error) {
        console.error('Error loading custom apps:', error);
    }

    // Create mini apps grid content
    grid.innerHTML = `
        <!-- Calculator Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🧮</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Calculator</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Basic calculator with scientific functions
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('calculator')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('calculator')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Notes Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">📝</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Notes</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Fast note-taking with auto-save functionality
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('notes')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('notes')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Unit Converter Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🔄</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Unit Converter</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Convert between different units of measurement
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('converter')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('converter')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Color Palette Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🎨</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Color Palette</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Generate and manage color palettes for design
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('colors')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('colors')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Password Generator Mini App -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🔐</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Password Generator</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Generate secure passwords with custom criteria
                </p>
                <div class="flex space-x-2">
                    <button onclick="openMiniApp('password')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateMiniApp('password')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        ${customAppsHTML}

        <!-- Create New Mini App Card -->
        <div class="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-500 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6 text-center">
                <div class="text-4xl mb-4">➕</div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Create New</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                    Build your own mini-app with our framework
                </p>
                <button onclick="showCreateMiniApp()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    Get Started
                </button>
            </div>
        </div>
    `;
}

// Show mini apps container
async function showMiniApps() {
    console.log('showMiniApps called');
    const container = document.getElementById('miniAppContainer');
    if (container) {
        container.classList.remove('hidden');
        await initializeMiniAppsGrid();
        showHomeView();
        console.log('Mini apps container shown');
    } else {
        console.error('Mini app container not found');
    }
}



// Close mini app container
function closeMiniAppContainer() {
    const urlParams = new URLSearchParams(window.location.search);
    const isPopup = urlParams.get('popup') === 'true';

    if (isPopup) {
        // In popup mode, close the window
        window.close();
    } else {
        // In embedded mode, hide the container
        document.getElementById('miniAppContainer').classList.add('hidden');
        // Keep apps open for when user reopens
    }
}

// Show home view (mini apps grid)
function showHomeView() {
    currentView = 'home';
    const homeView = document.getElementById('homeView');
    const appsView = document.getElementById('appsView');

    if (homeView) homeView.style.display = 'block';
    if (appsView) appsView.style.display = 'none';

    // Update view toggle buttons
    const homeBtn = document.getElementById('homeViewBtn');
    const appsBtn = document.getElementById('appsViewBtn');

    if (homeBtn) homeBtn.className = 'px-3 py-1 rounded text-sm font-medium bg-blue-500 text-white shadow-sm';
    if (appsBtn) appsBtn.className = 'px-3 py-1 rounded text-sm font-medium text-blue-600 hover:text-blue-800';
}

// Show apps view (tabbed interface)
function showAppsView() {
    console.log('Switching to apps view');
    currentView = 'apps';
    const homeView = document.getElementById('homeView');
    const appsView = document.getElementById('appsView');

    console.log('Elements found:', { homeView: !!homeView, appsView: !!appsView });

    if (homeView) {
        homeView.style.display = 'none';
    }
    if (appsView) {
        appsView.style.display = 'block';
    }

    // Update view toggle buttons
    const homeBtn = document.getElementById('homeViewBtn');
    const appsBtn = document.getElementById('appsViewBtn');

    if (homeBtn) homeBtn.className = 'px-3 py-1 rounded text-sm font-medium text-blue-600 hover:text-blue-800';
    if (appsBtn) appsBtn.className = 'px-3 py-1 rounded text-sm font-medium bg-blue-500 text-white shadow-sm';

    // Refresh the tab bar and content to reflect current state
    updateTabBar();
    updateAppContent();
}

// Open a mini app in a new tab
async function openMiniApp(appName) {
    try {
        console.log('Opening mini app:', appName);

        // Check if we're in popup mode
        const container = document.getElementById('miniAppContainer');
        if (!container) {
            console.log('No container found, opening in new window');
            // If no container, open in new window (for non-popup mode)
            window.open(`mini-apps/${appName}.php`, '_blank', 'width=1200,height=800');
            return;
        }

        const appId = appName + '_' + Date.now(); // Unique ID for this instance
        const title = await getMiniAppTitle(appName);
        const content = getMiniAppContent(appName);

        console.log('Creating app tab:', { appId, title });

        // Add to open apps
        openApps.set(appId, {
            name: appName,
            title: title,
            content: content,
            active: true
        });

        // Deactivate other apps
        openApps.forEach((app, id) => {
            if (id !== appId) app.active = false;
        });

        activeAppId = appId;

        console.log('Open apps:', openApps.size);

        // Update UI
        updateTabBar();
        updateAppContent();

        // Switch to apps view
        showAppsView();

        console.log('Mini app opened successfully');
    } catch (error) {
        console.error('Error opening mini app:', error);
        alert('Error opening mini app: ' + error.message);
    }
}

// Update tab bar
function updateTabBar() {
    try {
        const tabList = document.getElementById('tabList');
        if (!tabList) {
            return;
        }

        tabList.innerHTML = '';

        openApps.forEach((app, appId) => {
            const tab = document.createElement('div');
            tab.className = `flex items-center px-3 py-1 rounded text-sm font-medium cursor-pointer transition-colors ${
                app.active
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`;

            const closeButtonClass = app.active
                ? 'ml-1 text-gray-300 hover:text-white transition-colors'
                : 'ml-1 text-gray-400 hover:text-gray-600 transition-colors';

            tab.innerHTML = `
                <span onclick="switchToApp('${appId}')" class="mr-2">${app.title}</span>
                <button onclick="event.stopPropagation(); closeApp('${appId}')" class="${closeButtonClass}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            `;

            tabList.appendChild(tab);
        });
    } catch (error) {
        console.error('Error updating tab bar:', error);
    }
}

// Update app content area - persistent tab content with show/hide logic
function updateAppContent() {
    try {
        console.log('updateAppContent called');
        const appTabs = document.getElementById('appTabs');
        if (!appTabs) {
            console.log('appTabs element not found');
            return;
        }

        console.log('Open apps count:', openApps.size);

        // Create missing app containers and update visibility
        openApps.forEach((app, appId) => {
            let appDiv = document.getElementById(`app_${appId}`);

            // Create app container if it doesn't exist
            if (!appDiv) {
                console.log('Creating new app container for:', appId);
                appDiv = document.createElement('div');
                appDiv.id = `app_${appId}`;
                appDiv.className = 'h-full';
                appDiv.innerHTML = app.content;
                appTabs.appendChild(appDiv);
                console.log('App container created and added:', appDiv);
            }

            // Update visibility based on active state
            if (app.active) {
                appDiv.style.display = 'block';
                console.log('Showing app:', appId);
            } else {
                appDiv.style.display = 'none';
                console.log('Hiding app:', appId);
            }
        });

        // Remove containers for closed apps
        const existingContainers = appTabs.querySelectorAll('[id^="app_"]');
        existingContainers.forEach(container => {
            const appId = container.id.replace('app_', '');
            if (!openApps.has(appId)) {
                console.log('Removing container for closed app:', appId);
                container.remove();
            }
        });

        console.log('App content updated with persistent containers');
    } catch (error) {
        console.error('Error updating app content:', error);
    }
}

// Switch to a specific app tab
function switchToApp(appId) {
    try {
        console.log('Switching to app:', appId);

        // Check if app exists
        if (!openApps.has(appId)) {
            console.warn('App not found:', appId);
            return;
        }

        // Deactivate all apps and activate the selected one
        openApps.forEach((app, id) => {
            app.active = (id === appId);
        });

        activeAppId = appId;

        // Update UI - tab bar for visual feedback, content for show/hide
        updateTabBar();
        updateAppContent();

        // Automatically switch to apps view to show the selected app
        showAppsView();

        console.log('Successfully switched to app:', appId);
    } catch (error) {
        console.error('Error switching to app:', error);
    }
}

// Close an app tab
function closeApp(appId) {
    try {
        console.log('Closing app:', appId);

        // Remove from openApps map
        openApps.delete(appId);

        // Remove the DOM container immediately to free memory
        const appContainer = document.getElementById(`app_${appId}`);
        if (appContainer) {
            console.log('Removing DOM container for:', appId);
            appContainer.remove();
        }

        // Always update the tab bar first to remove the tab
        updateTabBar();

        // If this was the active app, activate another one or go home
        if (activeAppId === appId) {
            if (openApps.size > 0) {
                const firstAppId = openApps.keys().next().value;
                switchToApp(firstAppId);
            } else {
                activeAppId = null;
                showHomeView();
            }
        } else {
            // Just update content visibility (no need to recreate)
            updateAppContent();
        }

        console.log('App closed successfully:', appId);
    } catch (error) {
        console.error('Error closing app:', error);
    }
}

// Store custom app data for quick access
let customAppsData = {};

// Get mini app title
async function getMiniAppTitle(appName) {
    const titles = {
        'calculator': 'Calculator',
        'notes': 'Quick Notes',
        'converter': 'Unit Converter',
        'colors': 'Color Palette',
        'password': 'Password Generator'
    };

    // Check if it's a built-in app
    if (titles[appName]) {
        return titles[appName];
    }

    // Check if it's a custom app
    if (customAppsData[appName]) {
        return customAppsData[appName].name;
    }

    // Try to fetch custom app data if not already loaded
    try {
        const response = await fetch('mini-apps/get-custom-apps.php');
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.apps) {
                customAppsData = result.apps;
                if (customAppsData[appName]) {
                    return customAppsData[appName].name;
                }
            }
        }
    } catch (error) {
        console.error('Error fetching custom app data:', error);
    }

    // Fallback to app name with proper formatting
    return appName.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

// Setup communication between iframe and chat bot window
function setupIframeCommunication(iframe) {
    try {
        // Pass the chat bot window reference to the iframe
        if (window.chatBotWindow && iframe.contentWindow) {
            iframe.contentWindow.chatBotWindow = window.chatBotWindow;
            console.log('Chat bot window reference passed to iframe');
        }
    } catch (error) {
        console.error('Error setting up iframe communication:', error);
    }
}

// Get mini app content (full screen for tabbed interface)
function getMiniAppContent(appName) {
    // Get current theme from URL or detect it
    const urlParams = new URLSearchParams(window.location.search);
    const theme = urlParams.get('theme') || 'dark';

    switch(appName) {
        case 'calculator':
            return `<iframe src="mini-apps/calculator.php?theme=${theme}&padding=true" class="w-full h-full border-0" onload="setupIframeCommunication(this)"></iframe>`;
        case 'notes':
            return `<iframe src="mini-apps/notes.php?theme=${theme}&padding=true" class="w-full h-full border-0" onload="setupIframeCommunication(this)"></iframe>`;
        case 'converter':
            return '<div class="pt-4">' + getConverterContent() + '</div>';
        case 'colors':
            return '<div class="pt-4">' + getColorsContent() + '</div>';
        case 'password':
            return '<div class="pt-4">' + getPasswordContent() + '</div>';
        default:
            // Check if it's a custom app - try folder structure first, then file structure
            if (customAppsData[appName] && customAppsData[appName].structure === 'folder-based') {
                return `<iframe src="mini-apps/${appName}/init.php?theme=${theme}&padding=true" class="w-full h-full border-0" onload="setupIframeCommunication(this)"></iframe>`;
            } else {
                // Fallback to file-based structure
                return `<iframe src="mini-apps/${appName}.php?theme=${theme}&padding=true" class="w-full h-full border-0" onload="setupIframeCommunication(this)"></iframe>`;
            }
    }
}

// Delete custom app
async function deleteCustomApp(appId) {
    if (!confirm('Are you sure you want to delete this mini app? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch('mini-apps/delete-app.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ appId: appId })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Mini app deleted successfully!', 'success');

            // Close all instances of this app if currently open
            const appsToClose = [];
            openApps.forEach((app, id) => {
                if (app.name === appId) {
                    appsToClose.push(id);
                }
            });

            appsToClose.forEach(id => {
                closeApp(id);
            });

            // Refresh the grid
            await initializeMiniAppsGrid();
        } else {
            showNotification('Error deleting mini app: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('Error deleting mini app:', error);
        showNotification('Error deleting mini app: ' + error.message, 'error');
    }
}

// Calculator mini app content
function getCalculatorContent() {
    return `
        <div class="space-y-4">
            <input type="text" id="calcDisplay" readonly class="w-full p-3 text-right text-xl border rounded-lg" value="0">
            <div class="grid grid-cols-4 gap-2">
                <button onclick="clearCalc()" class="p-3 bg-red-500 text-white rounded-lg">C</button>
                <button onclick="calcInput('/')" class="p-3 bg-gray-500 text-white rounded-lg">÷</button>
                <button onclick="calcInput('*')" class="p-3 bg-gray-500 text-white rounded-lg">×</button>
                <button onclick="calcBackspace()" class="p-3 bg-gray-500 text-white rounded-lg">⌫</button>
                
                <button onclick="calcInput('7')" class="p-3 bg-gray-200 rounded-lg">7</button>
                <button onclick="calcInput('8')" class="p-3 bg-gray-200 rounded-lg">8</button>
                <button onclick="calcInput('9')" class="p-3 bg-gray-200 rounded-lg">9</button>
                <button onclick="calcInput('-')" class="p-3 bg-gray-500 text-white rounded-lg">-</button>
                
                <button onclick="calcInput('4')" class="p-3 bg-gray-200 rounded-lg">4</button>
                <button onclick="calcInput('5')" class="p-3 bg-gray-200 rounded-lg">5</button>
                <button onclick="calcInput('6')" class="p-3 bg-gray-200 rounded-lg">6</button>
                <button onclick="calcInput('+')" class="p-3 bg-gray-500 text-white rounded-lg">+</button>
                
                <button onclick="calcInput('1')" class="p-3 bg-gray-200 rounded-lg">1</button>
                <button onclick="calcInput('2')" class="p-3 bg-gray-200 rounded-lg">2</button>
                <button onclick="calcInput('3')" class="p-3 bg-gray-200 rounded-lg">3</button>
                <button onclick="calcEquals()" class="p-3 bg-blue-500 text-white rounded-lg row-span-2">=</button>
                
                <button onclick="calcInput('0')" class="p-3 bg-gray-200 rounded-lg col-span-2">0</button>
                <button onclick="calcInput('.')" class="p-3 bg-gray-200 rounded-lg">.</button>
            </div>
        </div>
    `;
}

// Notes mini app content
function getNotesContent() {
    return `
        <div class="space-y-4">
            <textarea id="notesText" placeholder="Start typing your notes..." 
                      class="w-full h-64 p-3 border rounded-lg resize-none"></textarea>
            <div class="flex space-x-2">
                <button onclick="saveNotes()" class="px-4 py-2 bg-blue-500 text-white rounded-lg">Save</button>
                <button onclick="clearNotes()" class="px-4 py-2 bg-gray-500 text-white rounded-lg">Clear</button>
                <button onclick="sendNotesToChat()" class="px-4 py-2 bg-green-500 text-white rounded-lg">Send to Chat</button>
            </div>
        </div>
    `;
}

// Other mini app content functions (simplified for demo)
function getConverterContent() {
    return '<div class="text-center py-8"><p>Unit Converter coming soon!</p></div>';
}

function getColorsContent() {
    return '<div class="text-center py-8"><p>Color Palette generator coming soon!</p></div>';
}

function getPasswordContent() {
    return '<div class="text-center py-8"><p>Password Generator coming soon!</p></div>';
}

// Calculator functions
let calcExpression = '';

function calcInput(value) {
    const display = document.getElementById('calcDisplay');
    if (display.value === '0') {
        display.value = value;
    } else {
        display.value += value;
    }
    calcExpression = display.value;
}

function clearCalc() {
    document.getElementById('calcDisplay').value = '0';
    calcExpression = '';
}

function calcBackspace() {
    const display = document.getElementById('calcDisplay');
    if (display.value.length > 1) {
        display.value = display.value.slice(0, -1);
    } else {
        display.value = '0';
    }
    calcExpression = display.value;
}

function calcEquals() {
    try {
        const result = eval(calcExpression.replace('×', '*').replace('÷', '/'));
        document.getElementById('calcDisplay').value = result;
        calcExpression = result.toString();
    } catch (error) {
        document.getElementById('calcDisplay').value = 'Error';
        calcExpression = '';
    }
}

// Notes functions
function saveNotes() {
    const notes = document.getElementById('notesText').value;
    localStorage.setItem('chatbot_notes', notes);
    alert('Notes saved!');
}

function clearNotes() {
    if (confirm('Clear all notes?')) {
        document.getElementById('notesText').value = '';
    }
}

function sendNotesToChat() {
    const notes = document.getElementById('notesText').value;
    if (notes.trim()) {
        // This would integrate with the chat interface
        alert('Notes would be sent to chat: ' + notes.substring(0, 50) + '...');
    }
}

// Integration state storage
const integrationStates = {};

// Integration functions
async function integrateMiniApp(appName) {
    const button = document.querySelector(`button[onclick="integrateMiniApp('${appName}')"]`);
    const currentState = integrationStates[appName] || false;

    try {
        button.textContent = 'Integrating...';
        button.disabled = true;

        // Toggle the integration state
        const newState = !currentState;
        integrationStates[appName] = newState;

        // Update button appearance
        updateIntegrationButton(appName, newState);

        // Show notification
        const action = newState ? 'integrated with' : 'integration removed from';
        showNotification(`${getMiniAppTitle(appName)} ${action} chat bot`, 'success');

    } catch (error) {
        console.error('Integration error:', error);
        showNotification(`Failed to integrate ${getMiniAppTitle(appName)}: ${error.message}`, 'error');
        // Revert state on error
        integrationStates[appName] = currentState;
        updateIntegrationButton(appName, currentState);
    } finally {
        button.disabled = false;
    }
}

// Update integration button appearance
function updateIntegrationButton(appName, isIntegrated) {
    const button = document.querySelector(`button[onclick="integrateMiniApp('${appName}')"]`);
    if (!button) return;

    if (isIntegrated) {
        button.textContent = 'Integrated';
        button.className = button.className
            .replace('bg-green-500', 'bg-red-500')
            .replace('hover:bg-green-600', 'hover:bg-red-600')
            .replace('bg-blue-500', 'bg-red-500')
            .replace('hover:bg-blue-600', 'hover:bg-red-600');
    } else {
        button.textContent = 'Integrate';
        button.className = button.className
            .replace('bg-red-500', 'bg-green-500')
            .replace('hover:bg-red-600', 'hover:bg-green-600');
    }
}

// Initialize integration states on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all integration states as false
    const apps = ['calculator', 'notes', 'converter', 'colors', 'password'];
    apps.forEach(app => {
        integrationStates[app] = false;
        updateIntegrationButton(app, false);
    });

    // Listen for messages from mini-app iframes
    window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'openChat') {
            // Open chat in a new tab when requested by mini-app
            window.open(event.data.url, '_blank');
        }
    });
});

// Simplified integration status check - removed for now to avoid API errors

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Code Editor functionality
let currentEditingApp = null;

async function editMiniApp(appId) {
    try {
        currentEditingApp = appId;
        const appData = customAppsData[appId];

        if (!appData || appData.structure !== 'folder-based') {
            showNotification('This app is not editable', 'error');
            return;
        }

        // Show the modal
        document.getElementById('codeEditorModal').classList.remove('hidden');
        document.getElementById('editorAppName').textContent = `Editing ${appData.name} - mini-app.php`;

        // Load the code and AI prompt
        await Promise.all([
            loadMiniAppCode(appId),
            loadAIPrompt(appId)
        ]);

    } catch (error) {
        console.error('Error opening code editor:', error);
        showNotification('Error opening code editor: ' + error.message, 'error');
    }
}

async function loadMiniAppCode(appId) {
    try {
        const response = await fetch(`mini-apps/${appId}/mini-app.php`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const code = await response.text();
        document.getElementById('codeEditor').value = code;

    } catch (error) {
        console.error('Error loading mini-app code:', error);
        document.getElementById('codeEditor').value = '// Error loading code: ' + error.message;
    }
}

async function loadAIPrompt(appId) {
    try {
        const response = await fetch(`mini-apps/${appId}/AI_PROMPT.md`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const prompt = await response.text();
        document.getElementById('aiPromptContent').textContent = prompt;

    } catch (error) {
        console.error('Error loading AI prompt:', error);
        document.getElementById('aiPromptContent').textContent = 'Error loading AI prompt: ' + error.message;
    }
}

async function saveCode() {
    if (!currentEditingApp) {
        showNotification('No app selected for editing', 'error');
        return;
    }

    try {
        const code = document.getElementById('codeEditor').value;

        const response = await fetch('mini-apps/save-code.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                appId: currentEditingApp,
                code: code
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Code saved successfully!', 'success');
        } else {
            showNotification('Error saving code: ' + result.error, 'error');
        }

    } catch (error) {
        console.error('Error saving code:', error);
        showNotification('Error saving code: ' + error.message, 'error');
    }
}

function closeCodeEditor() {
    document.getElementById('codeEditorModal').classList.add('hidden');
    currentEditingApp = null;
}

function copyCode() {
    const codeContent = document.getElementById('codeEditor').value;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(codeContent).then(() => {
            showNotification('Code copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Error copying to clipboard:', err);
            fallbackCopyTextToClipboard(codeContent);
        });
    } else {
        fallbackCopyTextToClipboard(codeContent);
    }
}

function copyAIPrompt() {
    const promptContent = document.getElementById('aiPromptContent').textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(promptContent).then(() => {
            showNotification('AI prompt copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Error copying to clipboard:', err);
            fallbackCopyTextToClipboard(promptContent);
        });
    } else {
        fallbackCopyTextToClipboard(promptContent);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('AI prompt copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy AI prompt', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Failed to copy AI prompt', 'error');
    }

    document.body.removeChild(textArea);
}

// Create Mini App functionality
let formFieldCounter = 0;

function showCreateMiniApp() {
    document.getElementById('createMiniAppModal').classList.remove('hidden');
    setupAppTypeSelection();

    // Only add default field if form type is selected
    const formType = document.querySelector('input[name="appType"]:checked');
    if (formType && formType.value === 'form') {
        addFormField(); // Add one default field
    }
}

function closeCreateMiniApp() {
    document.getElementById('createMiniAppModal').classList.add('hidden');
    document.getElementById('createMiniAppForm').reset();
    document.getElementById('formFieldsList').innerHTML = '';
    formFieldCounter = 0;
}

function setupAppTypeSelection() {
    const appTypeRadios = document.querySelectorAll('input[name="appType"]');
    appTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Update visual selection
            document.querySelectorAll('input[name="appType"]').forEach(r => {
                const container = r.closest('label').querySelector('div');
                if (r.checked) {
                    container.className = 'p-4 border-2 border-blue-500 bg-blue-50 dark:bg-blue-900 rounded-lg';
                } else {
                    container.className = 'p-4 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 rounded-lg';
                }
            });

            // Show/hide relevant sections
            const formFieldsSection = document.getElementById('formFieldsSection');
            if (this.value === 'form') {
                formFieldsSection.style.display = 'block';
                // Add a default field if none exist
                const fieldsList = document.getElementById('formFieldsList');
                if (fieldsList.children.length === 0) {
                    addFormField();
                }
            } else {
                formFieldsSection.style.display = 'none';
                // Clear form fields for non-form apps
                document.getElementById('formFieldsList').innerHTML = '';
                formFieldCounter = 0;
            }
        });
    });
}

function addFormField() {
    formFieldCounter++;
    const fieldsList = document.getElementById('formFieldsList');

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700';
    fieldDiv.innerHTML = `
        <div class="flex items-center justify-between mb-3">
            <h4 class="font-medium text-gray-900 dark:text-white">Field ${formFieldCounter}</h4>
            <button type="button" onclick="removeFormField(this)" class="text-red-500 hover:text-red-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Label</label>
                <input type="text" name="fieldLabel[]"
                       class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                       placeholder="Field Label">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                <select name="fieldType[]"
                        class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm">
                    <option value="text">Text Input</option>
                    <option value="textarea">Text Area</option>
                    <option value="number">Number</option>
                    <option value="email">Email</option>
                    <option value="select">Dropdown</option>
                    <option value="checkbox">Checkbox</option>
                    <option value="radio">Radio Buttons</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Placeholder</label>
                <input type="text" name="fieldPlaceholder[]"
                       class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                       placeholder="Enter placeholder text">
            </div>
        </div>
    `;

    fieldsList.appendChild(fieldDiv);
}

function removeFormField(button) {
    button.closest('.p-4').remove();
}

function previewMiniApp() {
    const formData = collectFormData();
    if (!formData) return;

    const previewHTML = generateMiniAppHTML(formData);

    // Open preview in a new window
    const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    previewWindow.document.write(previewHTML);
    previewWindow.document.close();
}

function generateMiniAppHTML(appData) {
    const theme = new URLSearchParams(window.location.search).get('theme') || 'dark';

    let fieldsHTML = '';
    if (appData.type === 'form' && appData.fields.length > 0) {
        fieldsHTML = appData.fields.map(field => {
            switch (field.type) {
                case 'textarea':
                    return '<div class="mb-4">' +
                        '<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">' + field.label + '</label>' +
                        '<textarea rows="3" placeholder="' + field.placeholder + '" ' +
                        'class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>' +
                        '</div>';
                case 'select':
                    return '<div class="mb-4">' +
                        '<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">' + field.label + '</label>' +
                        '<select class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">' +
                        '<option value="">Select an option</option>' +
                        '<option value="option1">Option 1</option>' +
                        '<option value="option2">Option 2</option>' +
                        '</select>' +
                        '</div>';
                case 'checkbox':
                    return '<div class="mb-4">' +
                        '<label class="flex items-center">' +
                        '<input type="checkbox" class="mr-2">' +
                        '<span class="text-sm font-medium text-gray-700 dark:text-gray-300">' + field.label + '</span>' +
                        '</label>' +
                        '</div>';
                default:
                    return '<div class="mb-4">' +
                        '<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">' + field.label + '</label>' +
                        '<input type="' + field.type + '" placeholder="' + field.placeholder + '" ' +
                        'class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">' +
                        '</div>';
            }
        }).join('');
    } else {
        fieldsHTML = '<div class="text-center py-8">' +
            '<div class="text-4xl mb-4">' + appData.icon + '</div>' +
            '<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">' + appData.name + '</h3>' +
            '<p class="text-gray-600 dark:text-gray-300">' + appData.description + '</p>' +
            '<div class="mt-6">' +
            '<p class="text-sm text-gray-500 dark:text-gray-400">This is a preview of your ' + appData.type + ' app.</p>' +
            '<p class="text-sm text-gray-500 dark:text-gray-400">Functionality will be added when the app is created.</p>' +
            '</div>' +
            '</div>';
    }

    return '<!DOCTYPE html>' +
        '<html lang="en" class="' + theme + '">' +
        '<head>' +
            '<meta charset="UTF-8">' +
            '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
            '<title>' + appData.name + ' - Preview</title>' +
            '<script src="https://cdn.tailwindcss.com"><\/script>' +
            '<script>' +
                'tailwind.config = { darkMode: "class" }' +
            '<\/script>' +
        '</head>' +
        '<body class="bg-gray-100 dark:bg-gray-900 pt-4">' +
            '<div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">' +
                '<div class="flex items-center justify-between mb-6">' +
                    '<div class="flex items-center">' +
                        '<div class="text-2xl mr-3">' + appData.icon + '</div>' +
                        '<h2 class="text-xl font-bold text-gray-900 dark:text-white">' + appData.name + '</h2>' +
                    '</div>' +
                    '<div class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">' +
                        'PREVIEW' +
                    '</div>' +
                '</div>' +
                '<p class="text-gray-600 dark:text-gray-300 mb-6">' + appData.description + '</p>' +
                fieldsHTML +
                (appData.type === 'form' && appData.fields.length > 0 ?
                    '<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">' +
                        '<button class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">' +
                            'Submit' +
                        '</button>' +
                        '<button class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">' +
                            'Clear' +
                        '</button>' +
                        '<button class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600">' +
                            'Send to Chat' +
                        '</button>' +
                    '</div>' : '') +
            '</div>' +
        '</body>' +
        '</html>';
}

function collectFormData() {
    const form = document.getElementById('createMiniAppForm');
    const formData = new FormData(form);

    const appData = {
        name: formData.get('appName'),
        icon: formData.get('appIcon'),
        description: formData.get('appDescription'),
        type: formData.get('appType'),
        fields: []
    };

    // Validate required fields
    if (!appData.name || !appData.icon || !appData.description) {
        alert('Please fill in all required fields');
        return null;
    }

    // Collect form fields if it's a form app
    if (appData.type === 'form') {
        const labels = formData.getAll('fieldLabel[]');
        const types = formData.getAll('fieldType[]');
        const placeholders = formData.getAll('fieldPlaceholder[]');

        // Only add fields that have labels
        for (let i = 0; i < labels.length; i++) {
            if (labels[i] && labels[i].trim()) {
                appData.fields.push({
                    label: labels[i].trim(),
                    type: types[i] || 'text',
                    placeholder: placeholders[i] || ''
                });
            }
        }

        // If it's a form app but no fields were added, add a default field
        if (appData.fields.length === 0) {
            appData.fields.push({
                label: 'Input',
                type: 'text',
                placeholder: 'Enter text here'
            });
        }
    }

    return appData;
}



// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    // Handle form submission
    const createForm = document.getElementById('createMiniAppForm');
    if (createForm) {
        createForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const appData = collectFormData();
            if (!appData) return;

            // Get submit button and store original text
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            try {
                // Show loading state
                submitBtn.textContent = 'Creating...';
                submitBtn.disabled = true;

                // Create the mini-app file
                const response = await fetch('mini-apps/create-app.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(appData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseText = await response.text();

                if (!responseText.trim()) {
                    throw new Error('Empty response from server');
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (jsonError) {
                    console.error('JSON parsing error:', jsonError);
                    console.log('Response text:', responseText);
                    throw new Error('Invalid JSON response from server');
                }

                if (result.success) {
                    showNotification('Mini app created successfully!', 'success');
                    closeCreateMiniApp();

                    // Refresh the mini-apps grid to show the new app
                    await initializeMiniAppsGrid();

                    // Optionally open the new app
                    if (confirm('Mini app created! Would you like to open it now?')) {
                        openMiniApp(result.appId);
                    }
                } else {
                    showNotification('Error creating mini app: ' + (result.error || 'Unknown error'), 'error');
                }

            } catch (error) {
                console.error('Error creating mini app:', error);
                showNotification('Error creating mini app: ' + error.message, 'error');
            } finally {
                // Restore button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // Initialize popup if in popup mode
    const urlParams = new URLSearchParams(window.location.search);
    const isPopup = urlParams.get('popup') === 'true';

    if (isPopup) {
        await initializeMiniAppsGrid();
        showHomeView();
    }
});
</script>

<?php
if (!$isPopup) {
    include __DIR__ . '/../../../templates/footer.php';
} else {
    echo '</body></html>';
}
?>
