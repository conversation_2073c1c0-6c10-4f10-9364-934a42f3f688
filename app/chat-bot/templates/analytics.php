<?php
/**
 * Chat Bot - Analytics Interface
 *
 * This template provides comprehensive analytics for Chat Bot usage,
 * conversation patterns, AI performance, and user engagement metrics.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$config = $app->getConfig();
$dataStorage = $app->getDataStorage();

// Get statistics
$stats = $dataStorage->getStatistics();

// Set page title
$pageTitle = $config['app']['name'] . ' - Analytics';

// Include dashboard header
include __DIR__ . '/../../../templates/header.php';
?>

<div class="space-y-6">
    <!-- Analytics Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('activity-chart', 'text-3xl'); ?> Chat Bot Analytics
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                Monitor Chat Bot performance, conversation patterns, and user engagement
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="?route=/chat" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to Chat
            </a>
            <button onclick="refreshStats()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Chat Statistics -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="text-2xl"><?php echo getIcon('ai-robot', 'text-2xl'); ?></div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Chats</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['total_chats']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversations -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="text-2xl">💬</div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Conversations</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['conversations']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Knowledge Items -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="text-2xl"><?php echo getIcon('file-storage', 'text-2xl'); ?></div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Knowledge Items</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo number_format($stats['data']['total_records']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Tokens -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="text-2xl">🎯</div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-300">AI Tokens</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['total_tokens']); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Chat Analytics -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Chat Analytics</h2>
            </div>
            
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Total Messages</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['total_chats']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Active Conversations</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['conversations']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Unique Users</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['unique_users']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Total Tokens Used</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['total_tokens']); ?></span>
                    </div>
                    <?php if ($stats['chats']['total_chats'] > 0): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-300">Avg. Tokens per Message</span>
                            <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['chats']['total_tokens'] / $stats['chats']['total_chats'], 1); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Knowledge Base Analytics -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Knowledge Base Analytics</h2>
            </div>
            
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Knowledge Items</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['data']['total_records']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Knowledge Categories</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['data']['data_types']); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600 dark:text-gray-300">Active Users</span>
                        <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['data']['unique_users']); ?></span>
                    </div>
                    <?php if ($stats['data']['unique_users'] > 0): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-300">Avg. Items per User</span>
                            <span class="font-semibold text-gray-900 dark:text-white"><?php echo number_format($stats['data']['total_records'] / $stats['data']['unique_users'], 1); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Analytics -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Settings Analytics</h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo number_format($stats['settings']['total_settings']); ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Total Settings</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo number_format($stats['settings']['global_settings']); ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Global Settings</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?php echo number_format($stats['settings']['user_settings']); ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">User Settings</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Bot Health Status -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mt-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Chat Bot Health</h2>
        </div>
        
        <div class="p-6">
            <?php
            $status = $app->getStatus();
            $health = $status['health'];
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <?php foreach ($health['checks'] as $check => $result): ?>
                    <div class="flex items-center space-x-3 p-3 rounded-lg <?php echo $result === 'ok' ? 'bg-green-50 dark:bg-green-900' : ($result === 'error' ? 'bg-red-50 dark:bg-red-900' : 'bg-yellow-50 dark:bg-yellow-900'); ?>">
                        <div class="text-lg">
                            <?php 
                            echo $result === 'ok' ? '✅' : ($result === 'error' ? '❌' : '⚠️');
                            ?>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white"><?php echo ucfirst(str_replace('_', ' ', $check)); ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-300"><?php echo ucfirst($result); ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Analytics JavaScript -->
<script>
// Refresh statistics
function refreshStats() {
    location.reload();
}

// Initialize analytics
document.addEventListener('DOMContentLoaded', function() {
    console.log('Analytics page loaded');
    
    // Log analytics view
    fetch('api/activity.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            action: 'analytics.viewed',
            description: 'Analytics page viewed',
            data: { timestamp: new Date().toISOString() }
        })
    });
});
</script>

<?php include __DIR__ . '/../../../templates/footer.php'; ?>
