<?php
/**
 * Applets Simple - Basic working version for debugging
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Set page title
$pageTitle = 'Chat Bot - Applets Simple';

// Check if this is being loaded in popup mode
$isPopup = isset($_GET['popup']) && $_GET['popup'] === 'true';

// Include dashboard header only if not in popup mode
if (!$isPopup) {
    include __DIR__ . '/../../../templates/header.php';
} else {
    ?>
    <!DOCTYPE html>
    <html lang="en" class="dark">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo htmlspecialchars($pageTitle); ?></title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                darkMode: 'class',
                theme: {
                    extend: {}
                }
            }
        </script>
    </head>
    <body class="bg-gray-100 dark:bg-gray-900">
    <?php
}
?>

<div class="space-y-6 p-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Applets Simple</h1>
    
    <!-- Simple Applets Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Calculator -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Calculator</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Basic calculator</p>
            <button onclick="openApp('calculator')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Open
            </button>
        </div>
        
        <!-- Notes -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Notes</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Quick notes</p>
            <button onclick="openApp('notes')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                Open
            </button>
        </div>
    </div>
</div>

<!-- Simple Popup -->
<div id="appPopup" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
    <div class="bg-white dark:bg-gray-800 w-full h-full flex flex-col">
        <div class="p-4 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center">
            <h3 id="appTitle" class="text-lg font-semibold text-gray-900 dark:text-white">App</h3>
            <button onclick="closeApp()" class="text-gray-500 hover:text-gray-700">✕</button>
        </div>
        <div id="appContent" class="flex-1">
            <!-- App content here -->
        </div>
    </div>
</div>

<script>
function openApp(appName) {
    const popup = document.getElementById('appPopup');
    const title = document.getElementById('appTitle');
    const content = document.getElementById('appContent');
    
    title.textContent = appName === 'calculator' ? 'Calculator' : 'Notes';
    
    if (appName === 'calculator') {
        content.innerHTML = '<iframe src="applets/calculator.php" class="w-full h-full border-0"></iframe>';
    } else if (appName === 'notes') {
        content.innerHTML = '<iframe src="applets/notes.php" class="w-full h-full border-0"></iframe>';
    }
    
    popup.classList.remove('hidden');
}

function closeApp() {
    document.getElementById('appPopup').classList.add('hidden');
}
</script>

<?php 
if (!$isPopup) {
    include __DIR__ . '/../../../templates/footer.php'; 
} else {
    echo '</body></html>';
}
?>
