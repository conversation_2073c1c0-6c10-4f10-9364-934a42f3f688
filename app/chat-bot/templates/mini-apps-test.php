<?php
/**
 * Applets Test - Simplified version to debug issues
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Set page title
$pageTitle = 'Chat Bot - Applets Test';

// Include dashboard header
include __DIR__ . '/../../../templates/header.php';
?>

<div class="space-y-6">
    <!-- Apple<PERSON> Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                📱 Applets Test
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">
                Testing applet functionality
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="?route=/chat" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to Chat
            </a>
        </div>
    </div>

    <!-- Test Applets -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Calculator Applet -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">🧮</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Calculator</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Basic calculator with scientific functions
                </p>
                <div class="flex space-x-2">
                    <button onclick="openApplet('calculator')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateApplet('calculator')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>

        <!-- Notes Applet -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="text-2xl mr-3">📝</div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Notes</h3>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    Fast note-taking with auto-save functionality
                </p>
                <div class="flex space-x-2">
                    <button onclick="openApplet('notes')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-1">
                        Open
                    </button>
                    <button onclick="integrateApplet('notes')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        Integrate
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Applet Container (Hidden by default) -->
<div id="miniAppContainer" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
            <h3 id="miniAppTitle" class="text-lg font-semibold text-gray-900 dark:text-white">Applet</h3>
            <button onclick="closeMiniApp()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div id="miniAppContent" class="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
            <!-- Mini app content will be loaded here -->
        </div>
    </div>
</div>

<script>
// Applets JavaScript
let currentMiniApp = null;

// Open a mini app
function openApplet(appName) {
    currentMiniApp = appName;
    document.getElementById('miniAppTitle').textContent = getAppletTitle(appName);
    document.getElementById('miniAppContent').innerHTML = getAppletContent(appName);
    document.getElementById('miniAppContainer').classList.remove('hidden');
}

// Close mini app
function closeMiniApp() {
    document.getElementById('miniAppContainer').classList.add('hidden');
    currentMiniApp = null;
}

// Get mini app title
function getAppletTitle(appName) {
    const titles = {
        'calculator': 'Calculator',
        'notes': 'Quick Notes'
    };
    return titles[appName] || 'Applet';
}

// Get mini app content
function getAppletContent(appName) {
    switch(appName) {
        case 'calculator':
            return '<iframe src="applets/calculator.php" class="w-full h-[500px] border-0 rounded-lg"></iframe>';
        case 'notes':
            return '<iframe src="applets/notes.php" class="w-full h-[600px] border-0 rounded-lg"></iframe>';
        default:
            return '<p>Mini app content coming soon!</p>';
    }
}

// Integration functions
function integrateApplet(appName) {
    alert('Integration test for ' + appName);
}
</script>

<?php include __DIR__ . '/../../../templates/footer.php'; ?>
