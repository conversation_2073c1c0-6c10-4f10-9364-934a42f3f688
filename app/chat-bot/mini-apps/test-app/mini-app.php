<?php
// Server-side function to handle mini-app requests
function handleMiniAppRequest($data) {
    switch ($data['action']) {
        case 'generate_proverb':
            $subject = isset($data['subject']) ? trim($data['subject']) : '';
            if (empty($subject)) {
                return ['success' => false, 'error' => 'Subject is required'];
            }
            
            // Generate a Chinese proverb based on the subject
            $proverb = generateChineseProverb($subject);
            return [
                'success' => true, 
                'proverb' => $proverb,
                'subject' => $subject,
                'message' => 'Proverb generated successfully'
            ];
            
        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}

// Function to generate contextual Chinese proverbs
function generateChineseProverb($subject) {
    $subject_lower = strtolower($subject);
    
    // Base proverbs that can be adapted
    $proverbs = [
        // Gaming/Fortnite related
        'fortnite' => [
            "在游戏中，智者先建堡垒，愚者只知冲锋。(In games, the wise build fortresses first, fools only charge ahead.)",
            "胜负如云烟，技艺方永恒。(Victory and defeat are like clouds, but skill is eternal.)",
            "千里之行，始于单步；游戏之胜，始于谋略。(A journey of a thousand miles begins with a single step; gaming victory begins with strategy.)"
        ],
        
        // Hollywood/Entertainment
        'hollywood' => [
            "戏如人生，人生如戏，真假难辨。(Drama is like life, life is like drama, truth and falsehood are hard to distinguish.)",
            "台上一分钟，台下十年功。(One minute on stage requires ten years of practice off stage.)",
            "明星如月，有圆有缺；名利如水，有涨有落。(Stars are like the moon, sometimes full, sometimes waning; fame and fortune are like water, sometimes rising, sometimes falling.)"
        ],
        
        // Beach/Nature
        'beach' => [
            "海纳百川，有容乃大。(The ocean accepts all rivers; tolerance makes one great.)",
            "潮起潮落，自然之道；人生起伏，顺其自然。(Tides rise and fall, this is nature's way; life has ups and downs, go with the flow.)",
            "沙滩上的脚印会被浪花抹去，但回忆却永远留在心中。(Footprints on the beach will be erased by waves, but memories remain forever in the heart.)"
        ],
        
        // Default/General proverbs
        'default' => [
            "学而时习之，不亦说乎？(Is it not a pleasure to study and practice what you learn?)",
            "知己知彼，百战不殆。(Know yourself and know your enemy, and you will never be defeated.)",
            "水滴石穿，绳锯木断。(Constant dripping wears away the stone; persistent sawing cuts through wood.)",
            "山重水复疑无路，柳暗花明又一村。(When mountains and rivers block the way, suddenly there's a bright village ahead.)",
            "富贵不能淫，贫贱不能移，威武不能屈。(Wealth cannot corrupt, poverty cannot sway, and force cannot bend a noble spirit.)"
        ]
    ];
    
    // Find matching category or use default
    $category = 'default';
    foreach (array_keys($proverbs) as $key) {
        if ($key !== 'default' && strpos($subject_lower, $key) !== false) {
            $category = $key;
            break;
        }
    }
    
    // Select random proverb from category
    $selected_proverbs = $proverbs[$category];
    $random_proverb = $selected_proverbs[array_rand($selected_proverbs)];
    
    return $random_proverb;
}
?>

<!-- HTML Content -->
<div class="space-y-4">
    <!-- Header -->
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Chinese Proverb Generator</h2>
        <p class="text-gray-600 dark:text-gray-400">Enter a subject and generate a relevant Chinese proverb</p>
    </div>
    
    <!-- Form Field -->
    <div class="space-y-2">
        <label for="proverb-subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Proverb Subject
        </label>
        <input 
            type="text" 
            id="proverb-subject" 
            name="proverb-subject"
            placeholder="e.g., Fortnite, Hollywood, The beach..."
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                   focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            autocomplete="off"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400">
            Popular subjects: Gaming, Movies, Nature, Technology, Love, Success
        </p>
    </div>
    
    <!-- Generated Proverb Display -->
    <div id="proverb-display" class="hidden p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h3 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">Generated Proverb:</h3>
        <p id="proverb-text" class="text-gray-800 dark:text-gray-200 font-medium italic"></p>
        <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">
            <span class="font-medium">Subject:</span> <span id="proverb-subject-display"></span>
        </p>
    </div>
    
    <!-- Console Log Notice -->
    <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
        <p class="text-sm text-yellow-800 dark:text-yellow-200">
            <span class="font-medium">📝 Note:</span> Proverbs will also be logged to the browser console. 
            Press F12 and check the Console tab to see the output.
        </p>
    </div>
</div>

<!-- Action Buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button 
        onclick="generateProverb()" 
        class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 
               focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
               disabled:opacity-50 disabled:cursor-not-allowed"
        id="generate-btn"
    >
        Generate Proverb
    </button>
    
    <button 
        onclick="clearForm()" 
        class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600
               focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
    >
        Clear
    </button>
    
    <button 
        onclick="sendDataToChat()" 
        class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600
               focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        id="send-chat-btn"
        disabled
    >
        Send to Chat
    </button>
</div>

<script>
// Global variables to store current proverb data
let currentProverbData = null;

// Main function to generate proverb
async function generateProverb() {
    const subjectInput = document.getElementById('proverb-subject');
    const subject = subjectInput.value.trim();
    
    if (!subject) {
        showNotification('Please enter a subject for the proverb', 'error');
        subjectInput.focus();
        return;
    }
    
    // Disable button during request
    const generateBtn = document.getElementById('generate-btn');
    generateBtn.disabled = true;
    generateBtn.textContent = 'Generating...';
    
    try {
        // Simulate API call to generate proverb
        const response = await fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'generate_proverb',
                subject: subject
            })
        });
        
        // For demo purposes, we'll generate the proverb client-side
        // In a real implementation, this would be handled by the PHP function
        const proverbData = generateClientSideProverb(subject);
        
        if (proverbData.success) {
            // Store current proverb data
            currentProverbData = proverbData;
            
            // Display proverb on screen
            displayProverb(proverbData);
            
            // Log to console
            console.log('🏮 Chinese Proverb Generated 🏮');
            console.log(`Subject: ${proverbData.subject}`);
            console.log(`Proverb: ${proverbData.proverb}`);
            console.log('-------------------');
            
            // Enable send to chat button
            document.getElementById('send-chat-btn').disabled = false;
            
            showNotification('Proverb generated successfully! Check console for details.', 'success');
        } else {
            throw new Error(proverbData.error || 'Failed to generate proverb');
        }
        
    } catch (error) {
        console.error('Error generating proverb:', error);
        showNotification('Error generating proverb: ' + error.message, 'error');
    } finally {
        // Re-enable button
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate Proverb';
    }
}

// Client-side proverb generation (fallback)
function generateClientSideProverb(subject) {
    const proverbs = {
        'fortnite': [
            "在游戏中，智者先建堡垒，愚者只知冲锋。(In games, the wise build fortresses first, fools only charge ahead.)",
            "胜负如云烟，技艺方永恒。(Victory and defeat are like clouds, but skill is eternal.)",
            "千里之行，始于单步；游戏之胜，始于谋略。(A journey of a thousand miles begins with a single step; gaming victory begins with strategy.)"
        ],
        'hollywood': [
            "戏如人生，人生如戏，真假难辨。(Drama is like life, life is like drama, truth and falsehood are hard to distinguish.)",
            "台上一分钟，台下十年功。(One minute on stage requires ten years of practice off stage.)",
            "明星如月，有圆有缺；名利如水，有涨有落。(Stars are like the moon, sometimes full, sometimes waning; fame and fortune are like water, sometimes rising, sometimes falling.)"
        ],
        'beach': [
            "海纳百川，有容乃大。(The ocean accepts all rivers; tolerance makes one great.)",
            "潮起潮落，自然之道；人生起伏，顺其自然。(Tides rise and fall, this is nature's way; life has ups and downs, go with the flow.)",
            "沙滩上的脚印会被浪花抹去，但回忆却永远留在心中。(Footprints on the beach will be erased by waves, but memories remain forever in the heart.)"
        ],
        'default': [
            "学而时习之，不亦说乎？(Is it not a pleasure to study and practice what you learn?)",
            "知己知彼，百战不殆。(Know yourself and know your enemy, and you will never be defeated.)",
            "水滴石穿，绳锯木断。(Constant dripping wears away the stone; persistent sawing cuts through wood.)",
            "山重水复疑无路，柳暗花明又一村。(When mountains and rivers block the way, suddenly there's a bright village ahead.)",
            "富贵不能淫，贫贱不能移，威武不能屈。(Wealth cannot corrupt, poverty cannot sway, and force cannot bend a noble spirit.)"
        ]
    };
    
    // Find matching category
    const subjectLower = subject.toLowerCase();
    let category = 'default';
    
    for (const key of Object.keys(proverbs)) {
        if (key !== 'default' && subjectLower.includes(key)) {
            category = key;
            break;
        }
    }
    
    // Select random proverb
    const categoryProverbs = proverbs[category];
    const randomProverb = categoryProverbs[Math.floor(Math.random() * categoryProverbs.length)];
    
    return {
        success: true,
        proverb: randomProverb,
        subject: subject,
        category: category
    };
}

// Display proverb in the UI
function displayProverb(proverbData) {
    const display = document.getElementById('proverb-display');
    const text = document.getElementById('proverb-text');
    const subjectDisplay = document.getElementById('proverb-subject-display');
    
    text.textContent = proverbData.proverb;
    subjectDisplay.textContent = proverbData.subject;
    
    display.classList.remove('hidden');
    display.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Clear form and reset state
function clearForm() {
    document.getElementById('proverb-subject').value = '';
    document.getElementById('proverb-display').classList.add('hidden');
    document.getElementById('send-chat-btn').disabled = true;
    currentProverbData = null;
    
    // Clear console
    console.clear();
    console.log('🧹 Form cleared and console reset');
    
    showNotification('Form cleared successfully', 'success');
}

// Collect form data
function collectFormData() {
    return {
        subject: document.getElementById('proverb-subject').value.trim(),
        currentProverb: currentProverbData
    };
}

// Send data to chat
function sendDataToChat() {
    if (!currentProverbData) {
        showNotification('Please generate a proverb first', 'error');
        return;
    }
    
    const chatMessage = `I generated a Chinese proverb about "${currentProverbData.subject}": ${currentProverbData.proverb}`;
    
    // Use the built-in integration function
    sendToChat(currentProverbData, chatMessage);
    
    showNotification('Proverb sent to chat successfully!', 'success');
}

// Add enter key support for the input field
document.getElementById('proverb-subject').addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        generateProverb();
    }
});

// Initial console log
console.log('🏮 Chinese Proverb Generator Mini-App Loaded 🏮');
console.log('Enter a subject and click "Generate Proverb" to see Chinese wisdom!');
</script>