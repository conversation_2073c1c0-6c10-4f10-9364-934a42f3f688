<?php
// Folder To Markdown Mini-App
// No server-side processing needed for this app
?>

<!-- Folder To Markdown Mini-App -->
<div class="space-y-4">
    <!-- Upload Section -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border-2 border-dashed border-gray-300 dark:border-gray-600">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a2 2 0 00-2 2v20m0 0v9a2 2 0 002 2h24a2 2 0 002-2v-9m-28 0l4.586-4.586a2 2 0 012.828 0L20 32m-10 0h28m-10-4l4.586-4.586a2 2 0 012.828 0L38 26" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <label for="folderInput" class="mt-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Upload Folder
            </label>
            <input type="file" id="folderInput" webkitdirectory directory multiple class="mt-2 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-300 cursor-pointer">
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select a folder to convert to markdown</p>
        </div>
    </div>

    <!-- Progress Section -->
    <div id="progressSection" class="hidden">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <span class="ml-3 text-sm text-blue-700 dark:text-blue-300">Processing files...</span>
            </div>
            <div class="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div id="progressBar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            <p id="progressText" class="mt-1 text-xs text-gray-600 dark:text-gray-400">0 / 0 files processed</p>
        </div>
    </div>

    <!-- Stats Section -->
    <div id="statsSection" class="hidden grid grid-cols-3 gap-4">
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white" id="totalFiles">0</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Files</div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white" id="totalFolders">0</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Folders</div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white" id="totalSize">0 KB</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Size</div>
        </div>
    </div>

    <!-- Output Section -->
    <div id="outputSection" class="hidden">
        <div class="flex justify-between items-center mb-2">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Markdown Output</h3>
            <button onclick="copyToClipboard()" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                Copy to Clipboard
            </button>
        </div>
        <div class="relative">
            <pre id="markdownOutput" class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto max-h-96 overflow-y-auto text-sm font-mono whitespace-pre-wrap"></pre>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div id="actionButtons" class="hidden flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="processFolder()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors">
        Convert to Markdown
    </button>
    <button onclick="clearAll()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 transition-colors">
        Clear
    </button>
    <button onclick="sendMarkdownToChat()" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 transition-colors">
        Send to Chat
    </button>
</div>

<script>
let uploadedFiles = [];
let markdownContent = '';
let fileStructure = {};

// Handle folder selection
document.getElementById('folderInput').addEventListener('change', function(e) {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
        uploadedFiles = files;
        document.getElementById('actionButtons').classList.remove('hidden');
        showNotification(`${files.length} files selected`, 'success');
        
        // Auto-process if files are selected
        processFolder();
    }
});

// Build file structure from uploaded files
function buildFileStructure(files) {
    const structure = {};
    let totalSize = 0;
    
    files.forEach(file => {
        const pathParts = file.webkitRelativePath.split('/');
        let current = structure;
        
        totalSize += file.size;
        
        // Build nested structure
        for (let i = 0; i < pathParts.length - 1; i++) {
            const folderName = pathParts[i];
            if (!current[folderName]) {
                current[folderName] = {
                    type: 'folder',
                    children: {}
                };
            }
            current = current[folderName].children;
        }
        
        // Add file
        const fileName = pathParts[pathParts.length - 1];
        current[fileName] = {
            type: 'file',
            file: file,
            size: file.size,
            path: file.webkitRelativePath
        };
    });
    
    return { structure, totalSize };
}

// Generate tree structure in markdown
function generateTreeMarkdown(structure, prefix = '', isLast = true) {
    let markdown = '';
    const entries = Object.entries(structure);
    
    entries.forEach(([name, item], index) => {
        const isLastItem = index === entries.length - 1;
        const connector = isLastItem ? '└── ' : '├── ';
        const extension = isLastItem ? '    ' : '│   ';
        
        if (item.type === 'folder') {
            markdown += prefix + connector + '📁 ' + name + '/\n';
            markdown += generateTreeMarkdown(item.children, prefix + extension, isLastItem);
        } else {
            const icon = getFileIcon(name);
            markdown += prefix + connector + icon + ' ' + name + '\n';
        }
    });
    
    return markdown;
}

// Get appropriate file icon based on extension
function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'js': '📜', 'jsx': '📜', 'ts': '📜', 'tsx': '📜',
        'html': '🌐', 'htm': '🌐',
        'css': '🎨', 'scss': '🎨', 'sass': '🎨',
        'json': '📋', 'xml': '📋', 'yaml': '📋', 'yml': '📋',
        'md': '📝', 'txt': '📝', 'doc': '📝', 'docx': '📝',
        'png': '🖼️', 'jpg': '🖼️', 'jpeg': '🖼️', 'gif': '🖼️', 'svg': '🖼️',
        'pdf': '📄',
        'zip': '📦', 'rar': '📦', '7z': '📦',
        'mp3': '🎵', 'wav': '🎵', 'mp4': '🎬', 'avi': '🎬',
        'py': '🐍', 'php': '🐘', 'rb': '💎', 'go': '🐹',
        'sh': '🖥️', 'bat': '🖥️', 'exe': '🖥️'
    };
    
    return iconMap[ext] || '📄';
}

// Process files and convert to markdown
async function processFolder() {
    if (uploadedFiles.length === 0) {
        showNotification('Please select a folder first', 'error');
        return;
    }
    
    // Show progress
    document.getElementById('progressSection').classList.remove('hidden');
    document.getElementById('outputSection').classList.add('hidden');
    document.getElementById('statsSection').classList.add('hidden');
    
    // Build file structure
    const { structure, totalSize } = buildFileStructure(uploadedFiles);
    fileStructure = structure;
    
    // Start markdown generation
    markdownContent = '# Folder Structure\n\n';
    markdownContent += '```\n';
    markdownContent += generateTreeMarkdown(structure);
    markdownContent += '```\n\n';
    
    // Add file contents
    markdownContent += '# File Contents\n\n';
    
    let processedCount = 0;
    const totalFiles = uploadedFiles.length;
    
    // Sort files by path for consistent output
    const sortedFiles = uploadedFiles.sort((a, b) => 
        a.webkitRelativePath.localeCompare(b.webkitRelativePath)
    );
    
    for (const file of sortedFiles) {
        try {
            const content = await readFileContent(file);
            const fileExt = file.name.split('.').pop().toLowerCase();
            
            markdownContent += `## ${file.webkitRelativePath}\n\n`;
            
            if (isTextFile(file.name)) {
                markdownContent += '```' + getLanguageFromExtension(fileExt) + '\n';
                markdownContent += content;
                markdownContent += '\n```\n\n';
            } else if (isImageFile(file.name)) {
                markdownContent += `*[Binary image file: ${formatFileSize(file.size)}]*\n\n`;
            } else {
                markdownContent += `*[Binary file: ${formatFileSize(file.size)}]*\n\n`;
            }
            
            processedCount++;
            updateProgress(processedCount, totalFiles);
            
        } catch (error) {
            console.error(`Error reading file ${file.name}:`, error);
            markdownContent += `*[Error reading file: ${file.name}]*\n\n`;
        }
    }
    
    // Show results
    displayResults(totalFiles, countFolders(structure), totalSize);
}

// Count folders in structure
function countFolders(structure) {
    let count = 0;
    Object.values(structure).forEach(item => {
        if (item.type === 'folder') {
            count++;
            count += countFolders(item.children);
        }
    });
    return count;
}

// Read file content
function readFileContent(file) {
    return new Promise((resolve, reject) => {
        if (!isTextFile(file.name)) {
            resolve('');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = reject;
        reader.readAsText(file);
    });
}

// Check if file is text-based
function isTextFile(filename) {
    const textExtensions = [
        'txt', 'md', 'js', 'jsx', 'ts', 'tsx', 'json', 'html', 'htm', 
        'css', 'scss', 'sass', 'xml', 'yaml', 'yml', 'ini', 'conf',
        'sh', 'bat', 'py', 'rb', 'php', 'java', 'c', 'cpp', 'h', 'hpp',
        'cs', 'go', 'rs', 'swift', 'kt', 'r', 'sql', 'env', 'gitignore',
        'dockerfile', 'makefile', 'readme', 'license'
    ];
    
    const ext = filename.split('.').pop().toLowerCase();
    const name = filename.toLowerCase();
    
    return textExtensions.includes(ext) || 
           textExtensions.includes(name) ||
           name.startsWith('.');
}

// Check if file is image
function isImageFile(filename) {
    const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'webp', 'ico'];
    const ext = filename.split('.').pop().toLowerCase();
    return imageExtensions.includes(ext);
}

// Get language identifier for syntax highlighting
function getLanguageFromExtension(ext) {
    const langMap = {
        'js': 'javascript', 'jsx': 'javascript',
        'ts': 'typescript', 'tsx': 'typescript',
        'py': 'python', 'rb': 'ruby',
        'java': 'java', 'c': 'c', 'cpp': 'cpp',
        'cs': 'csharp', 'php': 'php',
        'go': 'go', 'rs': 'rust',
        'swift': 'swift', 'kt': 'kotlin',
        'r': 'r', 'sql': 'sql',
        'sh': 'bash', 'bat': 'batch',
        'yml': 'yaml', 'yaml': 'yaml',
        'json': 'json', 'xml': 'xml',
        'html': 'html', 'htm': 'html',
        'css': 'css', 'scss': 'scss',
        'md': 'markdown'
    };
    
    return langMap[ext] || ext;
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Update progress
function updateProgress(current, total) {
    const percentage = Math.round((current / total) * 100);
    document.getElementById('progressBar').style.width = percentage + '%';
    document.getElementById('progressText').textContent = `${current} / ${total} files processed`;
}

// Display results
function displayResults(files, folders, size) {
    document.getElementById('progressSection').classList.add('hidden');
    document.getElementById('statsSection').classList.remove('hidden');
    document.getElementById('outputSection').classList.remove('hidden');
    
    document.getElementById('totalFiles').textContent = files;
    document.getElementById('totalFolders').textContent = folders;
    document.getElementById('totalSize').textContent = formatFileSize(size);
    document.getElementById('markdownOutput').textContent = markdownContent;
}

// Copy to clipboard
function copyToClipboard() {
    navigator.clipboard.writeText(markdownContent).then(() => {
        showNotification('Markdown copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showNotification('Failed to copy to clipboard', 'error');
    });
}

// Clear all
function clearAll() {
    uploadedFiles = [];
    markdownContent = '';
    fileStructure = {};
    
    document.getElementById('folderInput').value = '';
    document.getElementById('progressSection').classList.add('hidden');
    document.getElementById('statsSection').classList.add('hidden');
    document.getElementById('outputSection').classList.add('hidden');
    document.getElementById('actionButtons').classList.add('hidden');
    
    showNotification('Cleared all data', 'success');
}

// Send to chat
function sendMarkdownToChat() {
    if (!markdownContent) {
        showNotification('No markdown content to send', 'error');
        return;
    }
    
    const data = {
        markdown: markdownContent,
        stats: {
            files: document.getElementById('totalFiles').textContent,
            folders: document.getElementById('totalFolders').textContent,
            size: document.getElementById('totalSize').textContent
        }
    };
    
    sendToChat(data, 'Here is the folder structure and contents in markdown format:');
    showNotification('Sent to chat!', 'success');
}
</script>

<style>
#markdownOutput {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    line-height: 1.5;
    tab-size: 4;
}

#markdownOutput::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

#markdownOutput::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

#markdownOutput::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

#markdownOutput::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}
</style>