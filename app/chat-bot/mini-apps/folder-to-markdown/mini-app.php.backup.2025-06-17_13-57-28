<?php
/**
 * mini-app.php
 * Folder To Markdown Mini-App
 */
?>

<div class="space-y-4">
  <div>
    <label class="block mb-1 font-medium text-gray-900 dark:text-white">Upload Folder</label>
    <input type="file" id="folderInput" webkitdirectory directory multiple class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-white dark:bg-gray-800 dark:border-gray-600" />
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Select a folder to convert its contents to Markdown.</p>
  </div>

  <div>
    <label class="block mb-1 font-medium text-gray-900 dark:text-white">Markdown Output</label>
    <textarea id="markdownOutput" rows="16" readonly class="w-full p-2 text-sm font-mono text-gray-900 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:text-white dark:border-gray-600"></textarea>
  </div>
</div>

<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
  <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
    Convert to Markdown
  </button>
  <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
    Clear
  </button>
  <button onclick="sendDataToChat()" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600">
    Send to Chat
  </button>
</div>

<script>
function collectFormData() {
  return {
    markdown: document.getElementById('markdownOutput').value
  };
}

function clearForm() {
  document.getElementById('folderInput').value = '';
  document.getElementById('markdownOutput').value = '';
}

function sendDataToChat() {
  const data = collectFormData();
  if (!data.markdown) {
    showNotification('No Markdown to send.', 'error');
    return;
  }
  sendToChat(data, 'Here is the folder converted to Markdown:');
}

function submitForm() {
  const input = document.getElementById('folderInput');
  const files = input.files;
  if (!files.length) {
    showNotification('Please select a folder first.', 'error');
    return;
  }

  const fileArray = Array.from(files);
  const structure = [];
  const contents = [];

  fileArray.sort((a, b) => a.webkitRelativePath.localeCompare(b.webkitRelativePath));

  fileArray.forEach(file => {
    structure.push(`- \\`${file.webkitRelativePath}\``);
  });

  let pendingReads = fileArray.length;

  fileArray.forEach(file => {
    const reader = new FileReader();
    reader.onload = () => {
      contents.push(`\n\n### ${file.webkitRelativePath}\n\n\`\`\`\n${reader.result}\n\`\`\``);
      pendingReads--;
      if (pendingReads === 0) {
        const output = `# Folder Structure\n\n${structure.join("\n")}\n\n# File Contents\n${contents.join("\n")}`;
        document.getElementById('markdownOutput').value = output;
        showNotification('Folder successfully converted.', 'success');
      }
    };
    reader.onerror = () => {
      showNotification(`Failed to read ${file.name}`, 'error');
      pendingReads--;
    };
    reader.readAsText(file);
  });
}
</script>
