<div class="space-y-4 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Folder To Markdown Converter</h2>

    <div class="space-y-2">
        <label for="folderInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Upload Folder:
        </label>
        <input
            type="file"
            id="folderInput"
            webkitdirectory
            directory
            multiple
            class="block w-full text-sm text-gray-900 dark:text-gray-100
                   file:mr-4 file:py-2 file:px-4
                   file:rounded-md file:border-0
                   file:text-sm file:font-semibold
                   file:bg-blue-50 file:text-blue-700
                   hover:file:bg-blue-100
                   dark:file:bg-blue-900 dark:file:text-blue-300
                   dark:hover:file:bg-blue-800"
            onchange="handleFolderUpload(event)"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Select a folder from your computer. The app will read all files within it (and subfolders).
        </p>
    </div>

    <div class="space-y-2">
        <label for="markdownOutput" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Generated Markdown:
        </label>
        <textarea
            id="markdownOutput"
            class="w-full p-3 border border-gray-300 dark:border-gray-700 rounded-md
                   bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100
                   font-mono text-sm resize-y"
            rows="15"
            readonly
            placeholder="Upload a folder to see its structure and contents here..."
        ></textarea>
        <div id="loadingIndicator" class="hidden text-center text-blue-600 dark:text-blue-400 mt-2">
            Processing files... Please wait.
        </div>
    </div>

    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button onclick="copyMarkdown()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 transition-colors duration-200">
            <i class="fas fa-copy mr-2"></i> Copy Markdown
        </button>
        <button onclick="clearOutput()" class="flex-1 py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-200">
            <i class="fas fa-trash-alt mr-2"></i> Clear
        </button>
        <button onclick="sendMarkdownToChat()" class="flex-1 py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 transition-colors duration-200">
            <i class="fas fa-paper-plane mr-2"></i> Send to Chat
        </button>
    </div>

    <!-- Notification Container (for custom alerts/messages instead of alert()) -->
    <div id="appNotification" class="fixed inset-x-0 bottom-4 flex justify-center z-50 pointer-events-none">
        <div id="notificationMessage" class="hidden px-6 py-3 rounded-lg shadow-lg text-sm font-semibold transition-opacity duration-300 opacity-0 bg-blue-500 text-white dark:bg-blue-700"></div>
    </div>
</div>

<script>
    // Global variables for elements
    const folderInput = document.getElementById('folderInput');
    const markdownOutput = document.getElementById('markdownOutput');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const notificationMessage = document.getElementById('notificationMessage');

    // Function to display custom notifications
    function showAppNotification(message, type = 'info') {
        notificationMessage.textContent = message;
        let bgColor = 'bg-blue-500'; // Default info
        if (type === 'success') {
            bgColor = 'bg-green-500';
        } else if (type === 'error') {
            bgColor = 'bg-red-500';
        } else if (type === 'warning') {
            bgColor = 'bg-yellow-500';
        }

        notificationMessage.className = `px-6 py-3 rounded-lg shadow-lg text-sm font-semibold transition-opacity duration-300 opacity-100 ${bgColor} text-white dark:${bgColor.replace('500', '700')}`;
        notificationMessage.classList.remove('hidden');

        setTimeout(() => {
            notificationMessage.classList.remove('opacity-100');
            notificationMessage.classList.add('opacity-0');
            // Hide after transition
            setTimeout(() => {
                notificationMessage.classList.add('hidden');
            }, 300);
        }, 3000); // Hide after 3 seconds
    }


    /**
     * Handles the folder upload event, reads files, and generates Markdown.
     * @param {Event} event The change event from the file input.
     */
    async function handleFolderUpload(event) {
        const files = event.target.files;
        if (files.length === 0) {
            markdownOutput.value = "No folder selected or folder is empty.";
            return;
        }

        loadingIndicator.classList.remove('hidden');
        markdownOutput.value = ""; // Clear previous content

        try {
            // Sort files by their relative path for consistent output
            const sortedFiles = Array.from(files).sort((a, b) => {
                return a.webkitRelativePath.localeCompare(b.webkitRelativePath);
            });

            const markdownSections = [];

            // 1. Generate Folder Structure Markdown
            markdownSections.push("# Folder Structure\n\n```");
            const structure = buildFileStructure(sortedFiles);
            markdownSections.push(renderFileStructure(structure));
            markdownSections.push("```\n");

            // 2. Generate File Contents Markdown
            markdownSections.push("---\n\n# File Contents\n");

            for (const file of sortedFiles) {
                // Skip directories (files with size 0 and no content, though webkitdirectory usually only lists actual files)
                if (file.size === 0 && file.type === "") {
                    continue;
                }

                try {
                    const fileContent = await readFileAsText(file);
                    markdownSections.push(`\n## File: ${file.webkitRelativePath}\n\n\`\`\`\n${fileContent}\n\`\`\`\n`);
                } catch (readError) {
                    markdownSections.push(`\n## File: ${file.webkitRelativePath}\n\n\`\`\`\nError reading file: ${readError.message}\n\`\`\`\n`);
                    showAppNotification(`Error reading file: ${file.name}`, 'error');
                }
            }

            markdownOutput.value = markdownSections.join('');
            showAppNotification('Folder successfully converted to Markdown!', 'success');

        } catch (error) {
            console.error("Error processing folder:", error);
            markdownOutput.value = `Error processing folder: ${error.message}`;
            showAppNotification('An error occurred during processing.', 'error');
        } finally {
            loadingIndicator.classList.add('hidden');
        }
    }

    /**
     * Reads a file as text.
     * @param {File} file The file object to read.
     * @returns {Promise<string>} A promise that resolves with the file content as text.
     */
    function readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error(`Failed to read file ${file.name}: ${reader.error}`));
            reader.readAsText(file);
        });
    }

    /**
     * Builds a nested object representing the folder structure.
     * @param {File[]} files An array of File objects from a webkitdirectory input.
     * @returns {Object} A nested object representing the file system tree.
     */
    function buildFileStructure(files) {
        const tree = {};

        files.forEach(file => {
            const pathParts = file.webkitRelativePath.split('/');
            let currentLevel = tree;

            for (let i = 0; i < pathParts.length; i++) {
                const part = pathParts[i];
                if (i === pathParts.length - 1) {
                    // This is the file itself
                    if (!currentLevel.files) {
                        currentLevel.files = [];
                    }
                    currentLevel.files.push(part);
                } else {
                    // This is a directory
                    if (!currentLevel.children) {
                        currentLevel.children = {};
                    }
                    if (!currentLevel.children[part]) {
                        currentLevel.children[part] = {};
                    }
                    currentLevel = currentLevel.children[part];
                }
            }
        });
        return tree;
    }

    /**
     * Renders the file structure object into a Markdown string with indentation.
     * @param {Object} node The current node in the file structure tree.
     * @param {string} prefix The prefix for indentation (e.g., '├── ', '└── ', '│   ').
     * @returns {string} The Markdown string for the structure.
     */
    function renderFileStructure(node, prefix = '') {
        let output = '';
        const children = Object.keys(node.children || {}).sort();
        const files = (node.files || []).sort();
        const allEntries = [...children.map(c => ({ type: 'dir', name: c })), ...files.map(f => ({ type: 'file', name: f }))];

        allEntries.forEach((entry, index) => {
            const isLast = index === allEntries.length - 1;
            const connector = isLast ? '└── ' : '├── ';
            const newPrefix = isLast ? prefix + '    ' : prefix + '│   ';

            output += `${prefix}${connector}${entry.name}${entry.type === 'dir' ? '/' : ''}\n`;

            if (entry.type === 'dir' && node.children[entry.name]) {
                output += renderFileStructure(node.children[entry.name], newPrefix);
            }
        });

        return output;
    }

    /**
     * Copies the content of the markdownOutput textarea to the clipboard.
     */
    function copyMarkdown() {
        const content = markdownOutput.value;
        if (!content) {
            showAppNotification('Nothing to copy!', 'warning');
            return;
        }

        // Use navigator.clipboard.writeText if available (modern browsers)
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(content)
                .then(() => {
                    showAppNotification('Markdown copied to clipboard!', 'success');
                })
                .catch(err => {
                    console.error('Failed to copy text using Clipboard API:', err);
                    // Fallback for older browsers or if Clipboard API fails (e.g., in some iframes)
                    fallbackCopyTextToClipboard(content);
                });
        } else {
            // Fallback for browsers that do not support navigator.clipboard
            fallbackCopyTextToClipboard(content);
        }
    }

    /**
     * Fallback function to copy text to clipboard using document.execCommand.
     * @param {string} text The text to copy.
     */
    function fallbackCopyTextToClipboard(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed'; // Prevent scrolling to bottom
        textarea.style.left = '-9999px'; // Move off-screen
        document.body.appendChild(textarea);
        textarea.select();
        try {
            document.execCommand('copy');
            showAppNotification('Markdown copied to clipboard (fallback)!', 'success');
        } catch (err) {
            console.error('Failed to copy text using execCommand:', err);
            showAppNotification('Failed to copy markdown. Please copy manually.', 'error');
        } finally {
            document.body.removeChild(textarea);
        }
    }

    /**
     * Clears the markdown output and resets the file input.
     */
    function clearOutput() {
        markdownOutput.value = '';
        folderInput.value = null; // Clears the selected files from the input
        showAppNotification('Output cleared!', 'info');
    }

    /**
     * Sends the generated Markdown content to the chat bot.
     */
    function sendMarkdownToChat() {
        const content = markdownOutput.value;
        if (!content) {
            showAppNotification('No markdown to send!', 'warning');
            return;
        }
        // Assuming sendToChat is a global function provided by the AI Dashboard system
        if (typeof sendToChat === 'function') {
            sendToChat(content, "Here are the converted folder contents:");
            showAppNotification('Markdown sent to chat!', 'success');
        } else {
            console.error("sendToChat function is not available.");
            showAppNotification('Could not send to chat: sendToChat function not found.', 'error');
        }
    }
</script>
```php
<?php
// PHP functions can be added here if server-side logic is needed for this mini-app.
// For this "Folder To Markdown" app, all core functionality (file reading and markdown generation)
// is handled client-side using JavaScript due to browser security models for file access.
// If there were a need to process files on the server (e.g., very large files, specific parsing),
// this section would be used in conjunction with a frontend fetch request.

function handleMiniAppRequest($data) {
    // This function would be called if you make an AJAX request from your JavaScript
    // to this mini-app's PHP backend (via the main init.php's API handler).
    // Example:
    // if ($data['action'] === 'process_text') {
    //     $text_to_process = $data['text'];
    //     // Perform server-side processing here
    //     return ['success' => true, 'processed_text' => strtoupper($text_to_process)];
    // }
    return ['success' => false, 'error' => 'No server-side action defined for Folder To Markdown.'];
}
?>
