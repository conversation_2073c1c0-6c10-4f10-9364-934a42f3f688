<?php
// Folder To Markdown Applet - Core Functionality
?>

<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Folder To Markdown</h2>
        <p class="text-gray-600 dark:text-gray-400">Upload a folder and convert it to markdown format for easy sharing</p>
    </div>

    <!-- Upload Section -->
    <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center bg-gray-50 dark:bg-gray-800/50">
        <div class="space-y-4">
            <div class="text-4xl text-gray-400 dark:text-gray-500">📁</div>
            <div>
                <label for="folderInput" class="cursor-pointer">
                    <span class="text-lg font-medium text-gray-700 dark:text-gray-300">Choose Folder to Upload</span>
                    <input type="file" id="folderInput" webkitdirectory directory multiple class="hidden" accept="*/*">
                </label>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400">Select a folder and all its contents will be processed</p>
        </div>
    </div>

    <!-- Processing Status -->
    <div id="processingStatus" class="hidden bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex items-center space-x-3">
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <span class="text-blue-800 dark:text-blue-200">Processing folder...</span>
        </div>
    </div>

    <!-- File Statistics -->
    <div id="fileStats" class="hidden bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">Folder Analysis Complete</h3>
        <div id="statsContent" class="text-sm text-green-700 dark:text-green-300 space-y-1"></div>
    </div>

    <!-- Options -->
    <div id="optionsSection" class="hidden space-y-4">
        <h3 class="font-semibold text-gray-900 dark:text-white">Output Options</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label class="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer">
                <input type="checkbox" id="includeHidden" class="rounded border-gray-300 dark:border-gray-600">
                <span class="text-gray-700 dark:text-gray-300">Include hidden files</span>
            </label>
            <label class="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer">
                <input type="checkbox" id="includeBinary" class="rounded border-gray-300 dark:border-gray-600">
                <span class="text-gray-700 dark:text-gray-300">Include binary files (content as info)</span>
            </label>
        </div>
        <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Max file size to read (KB)</label>
            <input type="number" id="maxFileSize" value="500" min="1" max="10000" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
        </div>
    </div>

    <!-- Output Section -->
    <div id="outputSection" class="hidden space-y-4">
        <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900 dark:text-white">Generated Markdown</h3>
            <button onclick="copyToClipboard()" class="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                📋 Copy Markdown
            </button>
        </div>
        <div class="relative">
            <textarea id="markdownOutput" readonly class="w-full h-96 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-vertical" placeholder="Generated markdown will appear here..."></textarea>
        </div>
    </div>
</div>

<!-- Action buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="generateMarkdown()" id="generateBtn" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
        Generate Markdown
    </button>
    <button onclick="clearAll()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <button onclick="sendDataToChat()" id="sendToChatBtn" class="py-2 px-4 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
        Send to Chat
    </button>
</div>

<script>
let uploadedFiles = [];
let folderStructure = {};
let generatedMarkdown = '';

// Initialize event listeners
document.getElementById('folderInput').addEventListener('change', handleFolderUpload);

function handleFolderUpload(event) {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    uploadedFiles = files;
    showProcessingStatus(true);
    
    setTimeout(() => {
        analyzeFolderStructure(files);
        showProcessingStatus(false);
        showFileStats(files);
        document.getElementById('optionsSection').classList.remove('hidden');
        document.getElementById('generateBtn').disabled = false;
    }, 500);
}

function analyzeFolderStructure(files) {
    folderStructure = {};
    
    files.forEach(file => {
        const pathParts = file.webkitRelativePath.split('/');
        let current = folderStructure;
        
        pathParts.forEach((part, index) => {
            if (index === pathParts.length - 1) {
                // It's a file
                if (!current.files) current.files = [];
                current.files.push({
                    name: part,
                    fullPath: file.webkitRelativePath,
                    file: file,
                    size: file.size,
                    type: file.type || 'unknown'
                });
            } else {
                // It's a directory
                if (!current.dirs) current.dirs = {};
                if (!current.dirs[part]) current.dirs[part] = {};
                current = current.dirs[part];
            }
        });
    });
}

function showProcessingStatus(show) {
    const statusEl = document.getElementById('processingStatus');
    if (show) {
        statusEl.classList.remove('hidden');
    } else {
        statusEl.classList.add('hidden');
    }
}

function showFileStats(files) {
    const statsEl = document.getElementById('fileStats');
    const contentEl = document.getElementById('statsContent');
    
    const totalFiles = files.length;
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    const fileTypes = {};
    
    files.forEach(file => {
        const ext = file.name.split('.').pop().toLowerCase();
        fileTypes[ext] = (fileTypes[ext] || 0) + 1;
    });
    
    const topTypes = Object.entries(fileTypes)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([ext, count]) => `${ext}: ${count}`)
        .join(', ');
    
    contentEl.innerHTML = `
        <div><strong>Total files:</strong> ${totalFiles}</div>
        <div><strong>Total size:</strong> ${formatFileSize(totalSize)}</div>
        <div><strong>File types:</strong> ${topTypes}</div>
    `;
    
    statsEl.classList.remove('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function generateMarkdown() {
    if (uploadedFiles.length === 0) {
        showNotification('Please upload a folder first', 'error');
        return;
    }
    
    showProcessingStatus(true);
    
    try {
        const includeHidden = document.getElementById('includeHidden').checked;
        const includeBinary = document.getElementById('includeBinary').checked;
        const maxFileSize = parseInt(document.getElementById('maxFileSize').value) * 1024; // Convert to bytes
        
        let markdown = '# Folder Contents\n\n';
        
        // Add folder structure
        markdown += '## 📁 Folder Structure\n\n';
        markdown += '```\n';
        markdown += generateTreeStructure(folderStructure, '', includeHidden);
        markdown += '```\n\n';
        
        // Add file contents
        markdown += '## 📄 File Contents\n\n';
        
        const sortedFiles = uploadedFiles
            .filter(file => {
                if (!includeHidden && file.name.startsWith('.')) return false;
                return true;
            })
            .sort((a, b) => a.webkitRelativePath.localeCompare(b.webkitRelativePath));
        
        for (const file of sortedFiles) {
            markdown += await generateFileSection(file, includeBinary, maxFileSize);
        }
        
        generatedMarkdown = markdown;
        document.getElementById('markdownOutput').value = markdown;
        document.getElementById('outputSection').classList.remove('hidden');
        document.getElementById('sendToChatBtn').disabled = false;
        
        showNotification('Markdown generated successfully!', 'success');
        
    } catch (error) {
        showNotification('Error generating markdown: ' + error.message, 'error');
    } finally {
        showProcessingStatus(false);
    }
}

function generateTreeStructure(structure, prefix = '', includeHidden = false) {
    let result = '';
    
    // Add directories first
    if (structure.dirs) {
        const dirs = Object.keys(structure.dirs)
            .filter(name => includeHidden || !name.startsWith('.'))
            .sort();
        
        dirs.forEach((dirName, index) => {
            const isLast = index === dirs.length - 1 && (!structure.files || structure.files.length === 0);
            const connector = isLast ? '└── ' : '├── ';
            const nextPrefix = prefix + (isLast ? '    ' : '│   ');
            
            result += prefix + connector + dirName + '/\n';
            result += generateTreeStructure(structure.dirs[dirName], nextPrefix, includeHidden);
        });
    }
    
    // Add files
    if (structure.files) {
        const files = structure.files
            .filter(file => includeHidden || !file.name.startsWith('.'))
            .sort((a, b) => a.name.localeCompare(b.name));
        
        files.forEach((file, index) => {
            const isLast = index === files.length - 1;
            const connector = isLast ? '└── ' : '├── ';
            result += prefix + connector + file.name + '\n';
        });
    }
    
    return result;
}

async function generateFileSection(file, includeBinary, maxFileSize) {
    let section = `### 📄 \`${file.webkitRelativePath}\`\n\n`;
    
    // Add file info
    section += `**Size:** ${formatFileSize(file.size)} | **Type:** ${file.type || 'unknown'}\n\n`;
    
    // Check if file is too large
    if (file.size > maxFileSize) {
        section += `*File too large (>${formatFileSize(maxFileSize)}). Content not included.*\n\n`;
        return section;
    }
    
    // Check if file is binary
    if (file.type && (
        file.type.startsWith('image/') ||
        file.type.startsWith('video/') ||
        file.type.startsWith('audio/') ||
        file.type === 'application/octet-stream' ||
        file.type === 'application/pdf' ||
        file.type.includes('zip') ||
        file.type.includes('binary')
    )) {
        if (includeBinary) {
            section += `*Binary file - content not displayed*\n\n`;
        } else {
            section += `*Binary file - skipped*\n\n`;
        }
        return section;
    }
    
    try {
        const content = await readFileAsText(file);
        const fileExtension = file.name.split('.').pop().toLowerCase();
        const language = getLanguageFromExtension(fileExtension);
        
        section += `\`\`\`${language}\n${content}\n\`\`\`\n\n`;
    } catch (error) {
        section += `*Error reading file: ${error.message}*\n\n`;
    }
    
    return section;
}

function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(new Error('Failed to read file'));
        reader.readAsText(file);
    });
}

function getLanguageFromExtension(ext) {
    const languageMap = {
        'js': 'javascript',
        'ts': 'typescript',
        'jsx': 'javascript',
        'tsx': 'typescript',
        'py': 'python',
        'java': 'java',
        'c': 'c',
        'cpp': 'cpp',
        'h': 'c',
        'hpp': 'cpp',
        'cs': 'csharp',
        'php': 'php',
        'rb': 'ruby',
        'go': 'go',
        'rs': 'rust',
        'swift': 'swift',
        'kt': 'kotlin',
        'scala': 'scala',
        'html': 'html',
        'htm': 'html',
        'css': 'css',
        'scss': 'scss',
        'sass': 'sass',
        'less': 'less',
        'json': 'json',
        'xml': 'xml',
        'yaml': 'yaml',
        'yml': 'yaml',
        'toml': 'toml',
        'ini': 'ini',
        'cfg': 'ini',
        'conf': 'ini',
        'sh': 'bash',
        'bash': 'bash',
        'zsh': 'bash',
        'fish': 'bash',
        'ps1': 'powershell',
        'bat': 'batch',
        'cmd': 'batch',
        'sql': 'sql',
        'md': 'markdown',
        'txt': 'text',
        'log': 'text',
        'gitignore': 'text',
        'env': 'text',
        'dockerfile': 'dockerfile',
        'makefile': 'makefile'
    };
    
    return languageMap[ext] || 'text';
}

function copyToClipboard() {
    const textarea = document.getElementById('markdownOutput');
    textarea.select();
    textarea.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        showNotification('Markdown copied to clipboard!', 'success');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(textarea.value).then(() => {
            showNotification('Markdown copied to clipboard!', 'success');
        }).catch(() => {
            showNotification('Failed to copy to clipboard', 'error');
        });
    }
}

function sendDataToChat() {
    if (!generatedMarkdown) {
        showNotification('Please generate markdown first', 'error');
        return;
    }
    
    const folderName = uploadedFiles[0]?.webkitRelativePath.split('/')[0] || 'uploaded-folder';
    const customMessage = `Here's the complete folder structure and contents for "${folderName}" converted to markdown format:`;
    
    sendToChat(generatedMarkdown, customMessage);
    showNotification('Folder contents sent to chat!', 'success');
}

function clearAll() {
    uploadedFiles = [];
    folderStructure = {};
    generatedMarkdown = '';
    
    document.getElementById('folderInput').value = '';
    document.getElementById('markdownOutput').value = '';
    document.getElementById('fileStats').classList.add('hidden');
    document.getElementById('optionsSection').classList.add('hidden');
    document.getElementById('outputSection').classList.add('hidden');
    document.getElementById('generateBtn').disabled = true;
    document.getElementById('sendToChatBtn').disabled = true;
    
    showNotification('All data cleared', 'success');
}
</script>