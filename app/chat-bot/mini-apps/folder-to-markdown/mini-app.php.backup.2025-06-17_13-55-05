<?php
// mini-app.php for Folder To Markdown

// Ensure JSON response
header('Content-Type: application/json');

// Server-side function to handle folder processing
function handleMiniAppRequest($data) {
    try {
        // Check if action is set
        if (!isset($data['action']) || $data['action'] !== 'process_folder') {
            return ['success' => false, 'error' => 'Invalid or missing action'];
        }

        // Check PHP upload limits
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && empty($_FILES)) {
            $uploadMaxFilesize = ini_get('upload_max_filesize');
            $postMaxSize = ini_get('post_max_size');
            return ['success' => false, 'error' => "No files uploaded. Check PHP limits: upload_max_filesize={$uploadMaxFilesize}, post_max_size={$postMaxSize}"];
        }

        // Check if files were uploaded
        if (!isset($_FILES['folder']) || empty($_FILES['folder']['name'][0])) {
            return ['success' => false, 'error' => 'No folder uploaded'];
        }

        $uploadDir = sys_get_temp_dir() . '/folder_to_markdown_' . uniqid();
        if (!mkdir($uploadDir, 0777, true)) {
            return ['success' => false, 'error' => 'Failed to create temporary directory'];
        }

        // Process uploaded files
        $files = $_FILES['folder'];
        $fileCount = count($files['name']);
        $structure = [];

        // Move files to temporary directory, preserving folder structure
        for ($i = 0; $i < $fileCount; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                $errors = [
                    UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                    UPLOAD_ERR_FORM_SIZE => 'File exceeds form size limit',
                    UPLOAD_ERR_PARTIAL => 'File only partially uploaded',
                    UPLOAD_ERR_NO_FILE => 'No file uploaded',
                    UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                    UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                    UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the upload'
                ];
                $errorMsg = $errors[$files['error'][$i]] ?? 'Unknown upload error';
                continue; // Skip files with errors
            }

            // Get the relative path (e.g., "subfolder/file.txt")
            $relativePath = $files['name'][$i];
            $fullPath = $uploadDir . DIRECTORY_SEPARATOR . $relativePath;

            // Create subdirectories if needed
            $dir = dirname($fullPath);
            if (!is_dir($dir) && !mkdir($dir, 0777, true)) {
                return ['success' => false, 'error' => "Failed to create directory: $dir"];
            }

            // Move the file
            if (!move_uploaded_file($files['tmp_name'][$i], $fullPath)) {
                return ['success' => false, 'error' => "Failed to move file: $relativePath"];
            }

            // Track structure
            $pathParts = explode(DIRECTORY_SEPARATOR, $relativePath);
            $current = &$structure;
            foreach ($pathParts as $part) {
                $current[$part] = $current[$part] ?? [];
                $current = &$current[$part];
            }
        }

        // Generate markdown
        $markdown = generateMarkdownFromFolder($uploadDir);

        // Clean up
        deleteDirectory($uploadDir);

        return ['success' => true, 'markdown' => $markdown];
    } catch (Exception $e) {
        deleteDirectory($uploadDir ?? '');
        return ['success' => false, 'error' => 'Server error: ' . $e->getMessage()];
    }
}

// Helper function to generate markdown from folder
function generateMarkdownFromFolder($dir, $prefix = '') {
    $markdown = '';
    $items = scandir($dir);
    $files = [];
    $dirs = [];

    // Separate files and directories
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        if (is_dir($path)) {
            $dirs[] = $item;
        } else {
            $files[] = $item;
        }
    }

    // Sort directories and files
    sort($dirs);
    sort($files);

    // Generate folder structure (only on first call)
    if ($prefix === '') {
        $markdown .= "## Folder Structure\n\n";
        $markdown .= "```\n";
        $markdown .= generateStructure($dir);
        $markdown .= "```\n\n";
        $markdown .= "## File Contents\n\n";
    }

    // Generate file contents
    foreach ($files as $file) {
        $filePath = $dir . DIRECTORY_SEPARATOR . $file;
        $relativePath = $prefix . $file;
        $markdown .= "### $relativePath\n\n";
        $markdown .= "```" . getFileExtension($file) . "\n";
        $content = @file_get_contents($filePath);
        if ($content !== false) {
            $markdown .= htmlspecialchars($content);
        } else {
            $markdown .= "[Binary or unreadable file]";
        }
        $markdown .= "\n```\n\n";
    }

    // Recursively process subdirectories
    foreach ($dirs as $subDir) {
        $subDirPath = $dir . DIRECTORY_SEPARATOR . $subDir;
        $markdown .= generateMarkdownFromFolder($subDirPath, $prefix . $subDir . '/');
    }

    return $markdown;
}

// Helper function to generate folder structure
function generateStructure($dir, $prefix = '') {
    $structure = '';
    $items = scandir($dir);
    sort($items);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        $structure .= $prefix . $item . (is_dir($path) ? '/' : '') . "\n";
        if (is_dir($path)) {
            $structure .= generateStructure($path, $prefix . '  ');
        }
    }
    return $structure;
}

// Helper function to get file extension for code block
function getFileExtension($filename) {
    $ext = pathinfo($filename, PATHINFO_EXTENSION);
    return $ext ? strtolower($ext) : 'text';
}

// Helper function to delete directory recursively
function deleteDirectory($dir) {
    if (!file_exists($dir)) return;
    $items = scandir($dir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    rmdir($dir);
}

// Handle AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SERVER['HTTP_X_MINIAPP_ACTION'])) {
    $response = handleMiniAppRequest(['action' => $_SERVER['HTTP_X_MINIAPP_ACTION']]);
    echo json_encode($response);
    exit;
}
?>

<div class="space-y-4">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Folder To Markdown</h2>
    <p class="text-gray-600 dark:text-gray-300">Upload a folder to convert its structure and contents to markdown format.</p>

    <!-- File upload form -->
    <form id="folderForm" enctype="multipart/form-data" class="space-y-4">
        <div>
            <label for="folder" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select Folder</label>
            <input type="file" id="folder" name="folder[]" webkitdirectory multiple class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-gray-700 dark:file:text-gray-200 dark:hover:file:bg-gray-600">
        </div>
    </form>

    <!-- Output area -->
    <div id="outputContainer" class="hidden">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Markdown Output</label>
        <textarea id="markdownOutput" readonly class="mt-1 block w-full h-64 p-2 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-white" placeholder="Markdown will appear here..."></textarea>
    </div>
</div>

<!-- Action buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">Convert to Markdown</button>
    <button onclick="copyToClipboard()" id="copyButton" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 hidden">Copy Markdown</button>
    <button onclick="send propostas ao chat()" id="chatButton" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 hidden">Send to Chat</button>
</div>

<script>
function submitForm() {
    const form = document.getElementById('folderForm');
    const folderInput = document.getElementById('folder');

    if (!folderInput.files.length) {
        showNotification('Please select a folder to upload', 'error');
        return;
    }

    // Validate file count and size client-side
    const maxFiles = 100; // Example limit
    const maxSizeMB = 10; // Example limit in MB
    let totalSize = 0;
    if (folderInput.files.length > maxFiles) {
        showNotification(`Too many files selected. Maximum is ${maxFiles}.`, 'error');
        return;
    }
    for (const file of folderInput.files) {
        totalSize += file.size;
        if (file.size > maxSizeMB * 1024 * 1024) {
            showNotification(`File ${file.name} exceeds ${maxSizeMB}MB limit.`, 'error');
            return;
        }
    }
    if (totalSize > maxSizeMB * 1024 * 1024) {
        showNotification(`Total folder size exceeds ${maxSizeMB}MB limit.`, 'error');
        return;
    }

    const formData = new FormData();
    for (const file of folderInput.files) {
        formData.append('folder[]', file, file.webkitRelativePath);
    }

    // Send AJAX request to process the folder
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-MiniApp-Action': 'process_folder'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const output = document.getElementById('markdownOutput');
            const outputContainer = document.getElementById('outputContainer');
            const copyButton = document.getElementById('copyButton');
            const chatButton = document.getElementById('chatButton');

            output.value = data.markdown;
            outputContainer.classList.remove('hidden');
            copyButton.classList.remove('hidden');
            chatButton.classList.remove('hidden');
            showNotification('Folder converted to markdown successfully!', 'success');
        } else {
            showNotification('Error: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('Error processing folder: ' + error.message, 'error');
    });
}

function copyToClipboard() {
    const markdown = document.getElementById('markdownOutput').value;
    navigator.clipboard.writeText(markdown)
        .then(() => {
            showNotification('Markdown copied to clipboard!', 'success');
        })
        .catch(() => {
            showNotification('Failed to copy markdown', 'error');
        });
}

function sendDataToChat() {
    const markdown = document.getElementById('markdownOutput').value;
    sendToChat({ markdown: markdown }, 'Converted folder to markdown');
    showNotification('Markdown sent to chat!', 'success');
}
</script>