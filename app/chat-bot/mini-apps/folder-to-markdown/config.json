{"name": "Folder To Markdown", "icon": "🗃", "description": "I want this app to allow the user to paste in a file structure that is represent by text like this:\n└── 📁 lib/\n    ├── 🐘 init.php\n    ├── 📁 functions/\n    │   ├── 🐘 admin-bar-menu.php\n    │   └── 🐘 general.php\n    ├── 📁 languages/\n    │   └── 📄 instant-ide-manager.pot\n    ├── 📁 admin/\n    │   ├── 🐘 settings.php\n    │   ├── 🐘 build-menu.php\n    │   ├── 📁 boxes/\n    │   │   ├── 🐘 settings.php\n    │   │   └── 🐘 admin-access.php\n    │   └── 📁 update/\n    │       ├── 🐘 edd-updater.php\n    │       ├── 🐘 update.php\n    │       └── 🐘 EDD_SL_Plugin_Updater.php\n    ├── 📁 js/\n    │   ├── 📜 scripts.js\n    │   └── 📜 admin-options.js\n    └── 📁 css/\n        ├── 🎨 admin.css\n        ├── 🎨 icons.css\n        └── 📁 fonts/\n            ├── 🖼️ freelancer.svg\n            ├── 📄 freelancer.ttf\n            ├── 📄 freelancer.woff2\n            ├── 📄 freelancer.woff\n            └── 📄 freelancer.eot\n\nBe sure that it accounts for a wide array of file structure text representations, not just the specific one above. Also make sure to include a basic example of what file structure text looks like to make it clear to the user how to use the app. And then I want that file structure text to result into a zipped file that includes the files and folders that accurately represent the structure text. I then want this zipped file downloaded to the local computer.", "type": "tool", "id": "folder-to-markdown", "version": "1.0.0", "created_at": "2025-06-17 15:29:43", "fields": [], "editable": true, "structure": "folder-based"}