{"name": "Folder To Markdown", "icon": "🗂", "description": "I want this app to allow the user to upload an entire folder to the app and then convert it to markdown can can easily be copied for pasting elsewhere. So I want the output to be in raw markdown format that fully and accurately represents the contents of the folder, starting out with showing the file/folder structure and then followed by the contents of each file, listed in the appropriate order and labeled with the file name and path.", "type": "tool", "id": "folder-to-markdown", "version": "1.0.0", "created_at": "2025-06-17 13:12:52", "fields": [], "editable": true, "structure": "folder-based"}