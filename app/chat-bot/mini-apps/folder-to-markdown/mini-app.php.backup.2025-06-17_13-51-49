<?php
// mini-app.php for Folder To Markdown

// Server-side function to handle folder processing
function handleMiniAppRequest($data) {
    if ($data['action'] === 'process_folder') {
        try {
            // Check if a file was uploaded
            if (!isset($_FILES['folder']) || $_FILES['folder']['error'] === UPLOAD_ERR_NO_FILE) {
                return ['success' => false, 'error' => 'No folder uploaded'];
            }

            $uploadDir = sys_get_temp_dir() . '/folder_to_markdown_' . uniqid();
            mkdir($uploadDir, 0777, true);

            // Handle uploaded folder (assuming a zip file for simplicity)
            $zipFile = $_FILES['folder']['tmp_name'];
            $zip = new ZipArchive();
            if ($zip->open($zipFile) !== true) {
                return ['success' => false, 'error' => 'Failed to open uploaded folder'];
            }

            $zip->extractTo($uploadDir);
            $zip->close();

            // Generate markdown
            $markdown = generateMarkdownFromFolder($uploadDir);

            // Clean up
            deleteDirectory($uploadDir);

            return ['success' => true, 'markdown' => $markdown];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    return ['success' => false, 'error' => 'Unknown action'];
}

// Helper function to generate markdown from folder
function generateMarkdownFromFolder($dir, $prefix = '') {
    $markdown = '';
    $items = scandir($dir);
    $files = [];
    $dirs = [];

    // Separate files and directories
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        if (is_dir($path)) {
            $dirs[] = $item;
        } else {
            $files[] = $item;
        }
    }

    // Sort directories and files
    sort($dirs);
    sort($files);

    // Generate folder structure
    $markdown .= "## Folder Structure\n\n";
    $markdown .= "```\n";
    $markdown .= generateStructure($dir, $prefix);
    $markdown .= "```\n\n";

    // Generate file contents
    $markdown .= "## File Contents\n\n";
    foreach ($files as $file) {
        $filePath = $dir . DIRECTORY_SEPARATOR . $file;
        $markdown .= "### $prefix$file\n\n";
        $markdown .= "```" . getFileExtension($file) . "\n";
        $content = file_get_contents($filePath);
        $markdown .= htmlspecialchars($content);
        $markdown .= "\n```\n\n";
    }

    // Recursively process subdirectories
    foreach ($dirs as $subDir) {
        $subDirPath = $dir . DIRECTORY_SEPARATOR . $subDir;
        $markdown .= generateMarkdownFromFolder($subDirPath, $prefix . $subDir . '/');
    }

    return $markdown;
}

// Helper function to generate folder structure
function generateStructure($dir, $prefix = '') {
    $structure = '';
    $items = scandir($dir);
    sort($items);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        $structure .= $prefix . $item . (is_dir($path) ? '/' : '') . "\n";
        if (is_dir($path)) {
            $structure .= generateStructure($path, $prefix . '  ');
        }
    }
    return $structure;
}

// Helper function to get file extension for code block
function getFileExtension($filename) {
    $ext = pathinfo($filename, PATHINFO_EXTENSION);
    return $ext ? strtolower($ext) : 'text';
}

// Helper function to delete directory recursively
function deleteDirectory($dir) {
    if (!file_exists($dir)) return;
    $items = scandir($dir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    rmdir($dir);
}
?>

<div class="space-y-4">
    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Folder To Markdown</h2>
    <p class="text-gray-600 dark:text-gray-300">Upload a folder to convert its structure and contents to markdown format.</p>

    <!-- File upload form -->
    <form id="folderForm" enctype="multipart/form-data" class="space-y-4">
        <div>
            <label for="folder" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select Folder (ZIP)</label>
            <input type="file" id="folder" name="folder" accept=".zip" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-gray-700 dark:file:text-gray-200 dark:hover:file:bg-gray-600">
        </div>
    </form>

    <!-- Output area -->
    <div id="outputContainer" class="hidden">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Markdown Output</label>
        <textarea id="markdownOutput" readonly class="mt-1 block w-full h-64 p-2 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-white" placeholder="Markdown will appear here..."></textarea>
    </div>
</div>

<!-- Action buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">Convert to Markdown</button>
    <button onclick="copyToClipboard()" id="copyButton" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 hidden">Copy Markdown</button>
    <button onclick="sendDataToChat()" id="chatButton" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 hidden">Send to Chat</button>
</div>

<script>
function submitForm() {
    const form = document.getElementById('folderForm');
    const formData = new FormData(form);
    const folderInput = document.getElementById('folder');

    if (!folderInput.files.length) {
        showNotification('Please select a folder (ZIP file) to upload', 'error');
        return;
    }

    // Send AJAX request to process the folder
    fetch('', {
        method: 'POST',
        body: formData,
        headers: {
            'X-MiniApp-Action': 'process_folder'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const output = document.getElementById('markdownOutput');
            const outputContainer = document.getElementById('outputContainer');
            const copyButton = document.getElementById('copyButton');
            const chatButton = document.getElementById('chatButton');

            output.value = data.markdown;
            outputContainer.classList.remove('hidden');
            copyButton.classList.remove('hidden');
            chatButton.classList.remove('hidden');
            showNotification('Folder converted to markdown successfully!', 'success');
        } else {
            showNotification('Error: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('Error processing folder: ' + error.message, 'error');
    });
}

function copyToClipboard() {
    const markdown = document.getElementById('markdownOutput').value;
    navigator.clipboard.writeText(markdown)
        .then(() => {
            showNotification('Markdown copied to clipboard!', 'success');
        })
        .catch(() => {
            showNotification('Failed to copy markdown', 'error');
        });
}

function sendDataToChat() {
    const markdown = document.getElementById('markdownOutput').value;
    sendToChat({ markdown: markdown }, 'Converted folder to markdown');
    showNotification('Markdown sent to chat!', 'success');
}
</script>