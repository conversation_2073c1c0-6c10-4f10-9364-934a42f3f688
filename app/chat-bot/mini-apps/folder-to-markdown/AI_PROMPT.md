# AI Prompt for Folder To Markdown Applet

## App Overview
- **Name:** Folder To Markdown
- **Description:** I want this app to allow the user to paste in a file structure that is represent by text like this:
└── 📁 lib/
    ├── 🐘 init.php
    ├── 📁 functions/
    │   ├── 🐘 admin-bar-menu.php
    │   └── 🐘 general.php
    ├── 📁 languages/
    │   └── 📄 instant-ide-manager.pot
    ├── 📁 admin/
    │   ├── 🐘 settings.php
    │   ├── 🐘 build-menu.php
    │   ├── 📁 boxes/
    │   │   ├── 🐘 settings.php
    │   │   └── 🐘 admin-access.php
    │   └── 📁 update/
    │       ├── 🐘 edd-updater.php
    │       ├── 🐘 update.php
    │       └── 🐘 EDD_SL_Plugin_Updater.php
    ├── 📁 js/
    │   ├── 📜 scripts.js
    │   └── 📜 admin-options.js
    └── 📁 css/
        ├── 🎨 admin.css
        ├── 🎨 icons.css
        └── 📁 fonts/
            ├── 🖼️ freelancer.svg
            ├── 📄 freelancer.ttf
            ├── 📄 freelancer.woff2
            ├── 📄 freelancer.woff
            └── 📄 freelancer.eot

Be sure that it accounts for a wide array of file structure text representations, not just the specific one above. Also make sure to include a basic example of what file structure text looks like to make it clear to the user how to use the app. And then I want that file structure text to result into a zipped file that includes the files and folders that accurately represent the structure text. I then want this zipped file downloaded to the local computer.
- **Type:** tool
- **App ID:** folder-to-markdown

## Instructions for AI

You are helping to create a custom applet for an AI Dashboard system. The app structure is already set up with proper integrations, and you need to create the core functionality.

### What's Already Provided:
- **init.php**: Handles all AI Dashboard integrations, theming, chat bot communication, and loads your custom code
- **config.json**: App metadata and configuration
- **This prompt file**: Instructions for AI development

### What You Need to Create:

Create the content for **mini-app.php** that will be included in the init.php file. This file MUST contain:

1. **PHP Opening Tag**: Always start with `<?php` even if you don't use PHP functions
2. **HTML Content**: The main interface for the applet
3. **PHP Functions** (optional): Any server-side logic needed
4. **JavaScript Functions** (optional): Client-side functionality

**IMPORTANT**: Even if your applet is purely HTML/CSS/JavaScript, you MUST include the PHP opening tag `<?php` at the beginning of the file for the AI Dashboard to save it properly.

### Available Integration Functions:
- `sendToChat(data, customMessage)` - Send data to the chat bot
- `showNotification(message, type)` - Show success/error notifications
- `closeApp()` - Close the applet
- `appConfig` - JavaScript object with app configuration

### Required File Structure:
```php
<?php
// ALWAYS start with PHP opening tag - this is REQUIRED even for HTML-only applets
?>

<!-- Your custom HTML content goes here -->
<div class="space-y-4">
    <!-- Form fields, buttons, content, etc. -->
</div>

<!-- Action buttons (optional) -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
        Submit
    </button>
    <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <!-- Only include "Send to Chat" button if the applet generates data that would be useful to share with the AI assistant -->
    <!-- Examples: calculation results, generated content, analysis outputs, formatted data -->
    <!-- Do NOT include for: simple tools, games, pure display apps, or apps that don't produce shareable results -->
</div>
```

### JavaScript Guidelines:
```javascript
<script>
// Your custom functions
function submitForm() {
    // Handle form submission
    const data = collectFormData();
    // Process data...
    showNotification('Success!', 'success');
}

function collectFormData() {
    // Collect form data
    return {};
}

function sendDataToChat() {
    const data = collectFormData();
    sendToChat(data); // Uses built-in integration
}

// Only implement sendDataToChat() and include the "Send to Chat" button if your applet:
// - Generates useful results (calculations, analysis, formatted data)
// - Produces content that would be valuable to share with the AI assistant
// - Creates outputs that could be used in further AI conversations
//
// Do NOT include "Send to Chat" functionality for:
// - Simple utility tools that don't produce shareable results
// - Games or entertainment apps
// - Pure display or visualization apps without data output
// - Apps that only manipulate local data without generating insights
</script>
```

### Styling:
- Use Tailwind CSS classes (already loaded)
- Follow dark/light theme patterns: `text-gray-900 dark:text-white`
- Use consistent spacing and styling with the container

### Server-Side Functions (if needed):
```php
<?php
function handleAppletRequest($data) {
    // Custom API request handling
    switch ($data['action']) {
        case 'custom_action':
            // Your logic here
            return ['success' => true, 'message' => 'Custom action completed'];
        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}
?>
```

## Critical Requirements:
1. **ALWAYS start your mini-app.php file with `<?php`** - This is mandatory for the AI Dashboard save system
2. **Use proper theme-aware styling** with Tailwind classes like `text-gray-900 dark:text-white`
3. **Only include "Send to Chat" button when appropriate** - Only if the applet generates useful data to share with AI
4. **Handle form validation** and provide user feedback
5. **Follow responsive design** principles for mobile compatibility

## Your Task:
Create a fully functional applet that implements the described functionality. Focus on:
1. **Starting with PHP tag** - Always begin with `<?php` even for HTML-only applets
2. **User interface** that matches the description and uses proper theming
3. **Form handling** and data collection with validation
4. **Selective chat bot integration** - Only include "Send to Chat" if the applet produces useful shareable results
5. **Error handling** and user feedback with notifications

**Important**: Evaluate whether your applet generates data that would be valuable to share with an AI assistant. Only include the "Send to Chat" button and sendDataToChat() function if it makes sense for the applet's purpose.

The code should be production-ready, follow modern web development best practices, and work seamlessly within the AI Dashboard applet system.