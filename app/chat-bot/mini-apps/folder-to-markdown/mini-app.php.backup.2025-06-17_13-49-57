<?php
function handleMiniAppRequest($data) {
    // Handle any server-side requests if needed
    switch ($data['action'] ?? '') {
        case 'process_folder':
            // Server-side processing could be added here if needed
            return ['success' => true, 'message' => 'Folder processed successfully'];
        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}
?>

<!-- Folder To Markdown Mini-App Interface -->
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Folder To Markdown Converter</h3>
        <p class="text-sm text-gray-600 dark:text-gray-400">
            Upload a folder to convert its structure and contents to markdown format
        </p>
    </div>

    <!-- Upload Section -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border-2 border-dashed border-gray-300 dark:border-gray-600">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <label for="folderInput" class="cursor-pointer">
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500">
                    Choose a folder to upload
                </span>
                <input type="file" id="folderInput" webkitdirectory directory multiple class="hidden" />
            </label>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Select a folder and all its contents will be processed
            </p>
        </div>
    </div>

    <!-- Progress Section -->
    <div id="progressSection" class="hidden">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-3"></div>
                <span class="text-sm text-blue-700 dark:text-blue-300" id="progressText">Processing files...</span>
            </div>
            <div class="mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div id="progressBar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <!-- Settings -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Options</h4>
        <div class="space-y-2">
            <label class="flex items-center">
                <input type="checkbox" id="includeHidden" class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Include hidden files (.files)</span>
            </label>
            <label class="flex items-center">
                <input type="checkbox" id="includeBinary" class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">List binary files (without content)</span>
            </label>
            <label class="flex items-center">
                <input type="checkbox" id="showFileSize" checked class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Show file sizes</span>
            </label>
        </div>
    </div>

    <!-- Output Section -->
    <div id="outputSection" class="hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white">Generated Markdown</h4>
                <div class="flex space-x-2">
                    <button onclick="copyMarkdown()" class="text-xs px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Copy
                    </button>
                    <button onclick="downloadMarkdown()" class="text-xs px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600">
                        Download
                    </button>
                </div>
            </div>
            <div class="p-4">
                <textarea id="markdownOutput" readonly 
                    class="w-full h-96 text-xs font-mono text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-600 rounded p-3 resize-none"
                    placeholder="Markdown output will appear here..."></textarea>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="processFolder()" id="processBtn" disabled 
        class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed">
        Generate Markdown
    </button>
    <button onclick="clearAll()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <button onclick="sendMarkdownToChat()" id="sendBtn" disabled 
        class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed">
        Send to Chat
    </button>
</div>

<script>
let selectedFiles = [];
let generatedMarkdown = '';

// File input handler
document.getElementById('folderInput').addEventListener('change', function(e) {
    selectedFiles = Array.from(e.target.files);
    document.getElementById('processBtn').disabled = selectedFiles.length === 0;
    
    if (selectedFiles.length > 0) {
        showNotification(`Selected ${selectedFiles.length} files from folder`, 'success');
    }
});

async function processFolder() {
    if (selectedFiles.length === 0) {
        showNotification('Please select a folder first', 'error');
        return;
    }

    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const outputSection = document.getElementById('outputSection');
    
    progressSection.classList.remove('hidden');
    outputSection.classList.add('hidden');

    try {
        // Sort files for better organization
        const sortedFiles = selectedFiles.sort((a, b) => a.webkitRelativePath.localeCompare(b.webkitRelativePath));
        
        // Build folder structure
        const folderStructure = buildFolderStructure(sortedFiles);
        
        // Generate markdown
        let markdown = '# Folder Structure\n\n';
        markdown += generateFolderTree(folderStructure);
        markdown += '\n\n# File Contents\n\n';
        
        // Process files
        for (let i = 0; i < sortedFiles.length; i++) {
            const file = sortedFiles[i];
            const progress = ((i + 1) / sortedFiles.length) * 100;
            
            progressBar.style.width = `${progress}%`;
            progressText.textContent = `Processing ${file.name}... (${i + 1}/${sortedFiles.length})`;
            
            // Check if we should include this file
            if (!shouldIncludeFile(file)) continue;
            
            markdown += await processFile(file);
            
            // Small delay to prevent UI blocking
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        generatedMarkdown = markdown;
        document.getElementById('markdownOutput').value = markdown;
        
        progressSection.classList.add('hidden');
        outputSection.classList.remove('hidden');
        document.getElementById('sendBtn').disabled = false;
        
        showNotification('Markdown generated successfully!', 'success');
        
    } catch (error) {
        progressSection.classList.add('hidden');
        showNotification(`Error processing folder: ${error.message}`, 'error');
    }
}

function buildFolderStructure(files) {
    const structure = {};
    
    files.forEach(file => {
        const parts = file.webkitRelativePath.split('/');
        let current = structure;
        
        for (let i = 0; i < parts.length - 1; i++) {
            if (!current[parts[i]]) {
                current[parts[i]] = {};
            }
            current = current[parts[i]];
        }
        
        if (!current._files) current._files = [];
        current._files.push(file);
    });
    
    return structure;
}

function generateFolderTree(structure, prefix = '', depth = 0) {
    let result = '';
    const entries = Object.entries(structure);
    
    entries.forEach(([key, value], index) => {
        if (key === '_files') return;
        
        const isLast = index === entries.length - 1;
        const currentPrefix = depth === 0 ? '' : prefix + (isLast ? '└── ' : '├── ');
        
        result += `${currentPrefix}📁 ${key}/\n`;
        
        // Add files in this directory
        if (value._files) {
            value._files.forEach((file, fileIndex) => {
                if (!shouldIncludeFile(file)) return;
                
                const filePrefix = depth === 0 ? '├── ' : prefix + (isLast ? '    ' : '│   ') + '├── ';
                const size = document.getElementById('showFileSize').checked ? ` (${formatFileSize(file.size)})` : '';
                result += `${filePrefix}📄 ${file.name}${size}\n`;
            });
        }
        
        // Recursively add subdirectories
        const nextPrefix = depth === 0 ? '' : prefix + (isLast ? '    ' : '│   ');
        result += generateFolderTree(value, nextPrefix, depth + 1);
    });
    
    return result;
}

async function processFile(file) {
    const filePath = file.webkitRelativePath;
    const fileName = file.name;
    const fileSize = document.getElementById('showFileSize').checked ? ` (${formatFileSize(file.size)})` : '';
    
    let markdown = `## ${filePath}${fileSize}\n\n`;
    
    if (isBinaryFile(file)) {
        markdown += `*Binary file - content not displayed*\n\n`;
        return markdown;
    }
    
    try {
        const content = await readFileContent(file);
        const extension = fileName.split('.').pop().toLowerCase();
        
        markdown += '```' + (getLanguageFromExtension(extension) || '') + '\n';
        markdown += content;
        markdown += '\n```\n\n';
        
    } catch (error) {
        markdown += `*Error reading file: ${error.message}*\n\n`;
    }
    
    return markdown;
}

function shouldIncludeFile(file) {
    const includeHidden = document.getElementById('includeHidden').checked;
    const includeBinary = document.getElementById('includeBinary').checked;
    
    // Check hidden files
    if (!includeHidden && file.name.startsWith('.')) {
        return false;
    }
    
    // Check binary files
    if (!includeBinary && isBinaryFile(file)) {
        return false;
    }
    
    return true;
}

function isBinaryFile(file) {
    const binaryExtensions = [
        'exe', 'dll', 'so', 'dylib', 'bin', 'dat',
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'ico', 'webp',
        'mp3', 'wav', 'flac', 'aac', 'ogg',
        'mp4', 'avi', 'mkv', 'mov', 'wmv',
        'zip', 'rar', '7z', 'tar', 'gz', 'bz2',
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        'ttf', 'otf', 'woff', 'woff2',
        'db', 'sqlite', 'mdb'
    ];
    
    const extension = file.name.split('.').pop().toLowerCase();
    return binaryExtensions.includes(extension);
}

function readFileContent(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target.result);
        reader.onerror = e => reject(new Error('Failed to read file'));
        reader.readAsText(file);
    });
}

function getLanguageFromExtension(ext) {
    const languages = {
        'js': 'javascript',
        'ts': 'typescript',
        'py': 'python',
        'java': 'java',
        'c': 'c',
        'cpp': 'cpp',
        'cs': 'csharp',
        'php': 'php',
        'rb': 'ruby',
        'go': 'go',
        'rs': 'rust',
        'kt': 'kotlin',
        'swift': 'swift',
        'html': 'html',
        'css': 'css',
        'scss': 'scss',
        'sass': 'sass',
        'xml': 'xml',
        'json': 'json',
        'yml': 'yaml',
        'yaml': 'yaml',
        'md': 'markdown',
        'sql': 'sql',
        'sh': 'bash',
        'ps1': 'powershell'
    };
    
    return languages[ext] || '';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function copyMarkdown() {
    const textarea = document.getElementById('markdownOutput');
    textarea.select();
    document.execCommand('copy');
    showNotification('Markdown copied to clipboard!', 'success');
}

function downloadMarkdown() {
    const blob = new Blob([generatedMarkdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'folder-structure.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showNotification('Markdown file downloaded!', 'success');
}

function sendMarkdownToChat() {
    if (!generatedMarkdown) {
        showNotification('No markdown to send. Generate markdown first.', 'error');
        return;
    }
    
    const data = {
        type: 'folder_markdown',
        content: generatedMarkdown,
        fileCount: selectedFiles.length
    };
    
    sendToChat(data, 'Here is the markdown representation of the uploaded folder structure and contents:');
    showNotification('Markdown sent to chat!', 'success');
}

function clearAll() {
    selectedFiles = [];
    generatedMarkdown = '';
    document.getElementById('folderInput').value = '';
    document.getElementById('markdownOutput').value = '';
    document.getElementById('progressSection').classList.add('hidden');
    document.getElementById('outputSection').classList.add('hidden');
    document.getElementById('processBtn').disabled = true;
    document.getElementById('sendBtn').disabled = true;
    showNotification('Cleared all data', 'success');
}
</script>