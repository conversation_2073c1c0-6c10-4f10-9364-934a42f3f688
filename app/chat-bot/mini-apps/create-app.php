<?php
/**
 * Mini App Creator
 * Handles creation of custom mini-apps
 */

// Set JSON response header first
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output, but log them

try {
    // Include necessary files with error handling
    $configPath = __DIR__ . '/../../../config.php';
    $authPath = __DIR__ . '/../../../auth.php';

    if (!file_exists($configPath)) {
        throw new Exception('Config file not found at: ' . $configPath);
    }

    if (!file_exists($authPath)) {
        throw new Exception('Auth file not found at: ' . $authPath);
    }

    require_once $configPath;
    require_once $authPath;

    // Check if functions exist before calling
    if (function_exists('requireAuth')) {
        requireAuth();
    }

    // Handle POST requests only
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        exit;
    }

    // Get JSON input
    $input = file_get_contents('php://input');
    if ($input === false) {
        throw new Exception('Failed to read input');
    }

    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('JSON decode error: ' . json_last_error_msg());
    }

    if (!$data) {
        echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
        exit;
    }

    // Define helper functions first
    function sanitizeAppName($name) {
        // Convert to lowercase, replace spaces with hyphens, remove special chars
        $sanitized = strtolower(trim($name));
        $sanitized = preg_replace('/[^a-z0-9\s-]/', '', $sanitized);
        $sanitized = preg_replace('/[\s-]+/', '-', $sanitized);
        $sanitized = trim($sanitized, '-');

        // Ensure it's not empty and add timestamp if needed
        if (empty($sanitized)) {
            $sanitized = 'custom-app-' . time();
        }

        return $sanitized;
    }

    function generateMiniAppFile($data, $appId) {
        $name = htmlspecialchars($data['name']);
        $type = $data['type'];
        $fields = $data['fields'] ?? [];

        // Generate form fields HTML
        $formFieldsHTML = '';
        $formFieldsJS = '';

        if ($type === 'form' && !empty($fields)) {
            foreach ($fields as $index => $field) {
                $fieldId = 'field_' . $index;
                $label = htmlspecialchars($field['label']);
                $placeholder = htmlspecialchars($field['placeholder']);

                switch ($field['type']) {
                    case 'textarea':
                        $formFieldsHTML .= "
    <div class=\"mb-4\">
        <label for=\"$fieldId\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">$label</label>
        <textarea id=\"$fieldId\" rows=\"3\" placeholder=\"$placeholder\"
                  class=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"></textarea>
    </div>";
                        break;

                    case 'select':
                        $formFieldsHTML .= "
    <div class=\"mb-4\">
        <label for=\"$fieldId\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">$label</label>
        <select id=\"$fieldId\" class=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">
            <option value=\"\">Select an option</option>
            <option value=\"option1\">Option 1</option>
            <option value=\"option2\">Option 2</option>
        </select>
    </div>";
                        break;

                    case 'checkbox':
                        $formFieldsHTML .= "
    <div class=\"mb-4\">
        <label class=\"flex items-center\">
            <input type=\"checkbox\" id=\"$fieldId\" class=\"mr-2\">
            <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\">$label</span>
        </label>
    </div>";
                        break;

                    default:
                        $formFieldsHTML .= "
    <div class=\"mb-4\">
        <label for=\"$fieldId\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">$label</label>
        <input type=\"{$field['type']}\" id=\"$fieldId\" placeholder=\"$placeholder\"
               class=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">
    </div>";
                        break;
                }
            }

            // Generate JavaScript for collecting form data
            $fieldMappings = [];
            foreach ($fields as $index => $field) {
                $fieldId = 'field_' . $index;
                $fieldType = $field['type'];
                if ($fieldType === 'checkbox') {
                    $fieldMappings[] = "        data['{$field['label']}'] = document.getElementById('$fieldId').checked;";
                } else {
                    $fieldMappings[] = "        data['{$field['label']}'] = document.getElementById('$fieldId').value;";
                }
            }

            $formFieldsJS = "
    function collectFormData() {
        const data = {};
" . implode("\n", $fieldMappings) . "
        return data;
    }";
        } else {
            $formFieldsJS = "
    function collectFormData() {
        return {};
    }";
        }

        // Generate the mini-app.php content
        return <<<PHP
<?php
/**
 * Custom Mini-App: $name
 *
 * This file contains the custom logic for your mini-app.
 * Edit this file to customize your app's functionality.
 *
 * Available functions from init.php:
 * - sendToChat(data, customMessage) - Send data to chat bot
 * - showNotification(message, type) - Show notifications
 * - closeApp() - Close the mini-app
 */

// Optional: Custom request handler
function handleMiniAppRequest(\$data) {
    switch (\$data['action']) {
        case 'submit_form':
            // Custom form submission logic here
            return ['success' => true, 'message' => 'Form submitted successfully'];

        case 'send_to_chat':
            // Custom chat integration logic here
            return ['success' => true, 'message' => 'Sent to chat successfully'];

        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}
?>

<!-- Custom Mini-App Content -->
<div class="space-y-4">
$formFieldsHTML
</div>

<!-- Action Buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
        Submit
    </button>
    <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <button onclick="sendDataToChat()" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600">
        Send to Chat
    </button>
</div>

<script>
    // Custom JavaScript functions
$formFieldsJS

    function submitForm() {
        const data = collectFormData();

        // You can customize this function to handle form submission
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'submit_form',
                data: data
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showNotification(result.message, 'success');
            } else {
                showNotification('Error: ' + result.error, 'error');
            }
        })
        .catch(error => {
            showNotification('Error submitting form', 'error');
        });
    }

    function clearForm() {
        document.querySelectorAll('input, textarea, select').forEach(element => {
            if (element.type === 'checkbox') {
                element.checked = false;
            } else {
                element.value = '';
            }
        });
    }

    function sendDataToChat() {
        const data = collectFormData();
        sendToChat(data); // Uses the built-in integration function
    }
</script>
PHP;
    }

    function generateInitFile($data, $appId) {
        $name = htmlspecialchars($data['name']);
        $icon = htmlspecialchars($data['icon']);
        $description = htmlspecialchars($data['description']);

        return <<<PHP
<?php
/**
 * Mini App: $name
 * Integration and Bootstrap File
 *
 * This file handles all AI Dashboard integrations and loads the custom mini-app logic.
 * DO NOT EDIT - This file is managed by AI Dashboard
 */

// Handle API requests
if (\$_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    \$input = file_get_contents('php://input');
    \$data = json_decode(\$input, true);

    if (\$data && isset(\$data['action'])) {
        // Include the custom mini-app logic
        include_once __DIR__ . '/mini-app.php';

        // Call the custom handler if it exists
        if (function_exists('handleMiniAppRequest')) {
            echo json_encode(handleMiniAppRequest(\$data));
        } else {
            // Default handlers
            switch (\$data['action']) {
                case 'submit_form':
                    echo json_encode(['success' => true, 'message' => 'Form submitted successfully']);
                    break;

                case 'send_to_chat':
                    echo json_encode(['success' => true, 'message' => 'Sent to chat successfully']);
                    break;

                default:
                    echo json_encode(['success' => false, 'error' => 'Unknown action']);
            }
        }
        exit;
    }
}

// Get theme and padding parameters
\$theme = \$_GET['theme'] ?? 'dark';
\$addPadding = isset(\$_GET['padding']) && \$_GET['padding'] === 'true';

// Set body classes based on parameters
\$bodyClasses = [];
if (\$theme === 'light') {
    \$bodyClasses[] = 'bg-gray-100';
} else {
    \$bodyClasses[] = 'bg-gray-100 dark:bg-gray-900';
}
if (\$addPadding) {
    \$bodyClasses[] = 'pt-4';
}
\$bodyClassString = implode(' ', \$bodyClasses);

// App metadata
\$appConfig = [
    'name' => '$name',
    'icon' => '$icon',
    'description' => '$description',
    'id' => '$appId',
    'theme' => \$theme
];
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo \$theme; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$name - Mini App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>
<body class="<?php echo \$bodyClassString; ?>">
    <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-6">
            <div class="text-2xl mr-3">$icon</div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">$name</h2>
        </div>

        <!-- Custom Mini-App Content -->
        <div id="miniAppContent">
            <?php
            // Include the custom mini-app content
            include_once __DIR__ . '/mini-app.php';
            ?>
        </div>
    </div>

    <!-- AI Dashboard Integration Scripts -->
    <script>
        // App configuration
        const appConfig = <?php echo json_encode(\$appConfig); ?>;

        // Close app function
        function closeApp() {
            if (window.parent && window.parent.closeMiniApp) {
                window.parent.closeMiniApp();
            } else {
                window.close();
            }
        }

        // Notification system
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 \${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Send to chat function
        async function sendToChat(data, customMessage = null) {
            // Check if we can send directly to the chat bot window
            if (window.chatBotWindow && !window.chatBotWindow.closed) {
                try {
                    // Format the message
                    const message = customMessage || `$icon **$name**\\n\\n` + Object.entries(data).map(([key, value]) => `**\${key}:** \${value}`).join('\\n');

                    // Call the function in the chat bot window
                    window.chatBotWindow.addMessageFromMiniApp(message);

                    // Focus the chat bot window
                    window.chatBotWindow.focus();

                    // Show success message
                    showNotification('Sent to chat successfully!', 'success');

                    return true;
                } catch (e) {
                    console.error('Error communicating with chat bot window:', e);
                }
            }

            // Fallback: show data in alert
            const message = Object.entries(data).map(([key, value]) => `\${key}: \${value}`).join('\\n');
            alert('Data to send to chat:\\n\\n' + message);
            return false;
        }
    </script>

    <!-- Include custom mini-app scripts -->
    <script>
        <?php
        // Include custom JavaScript if the mini-app provides it
        \$customJSFile = __DIR__ . '/mini-app.js';
        if (file_exists(\$customJSFile)) {
            echo file_get_contents(\$customJSFile);
        }
        ?>
    </script>
</body>
</html>
PHP;
    }



    function generateConfigFile($data, $appId) {
        $config = [
            'name' => $data['name'],
            'icon' => $data['icon'],
            'description' => $data['appDisplayDescription'], // Use display description for config
            'type' => $data['type'],
            'id' => $appId,
            'version' => '1.0.0',
            'created_at' => date('Y-m-d H:i:s'),
            'fields' => $data['fields'] ?? [],
            'editable' => true,
            'structure' => 'folder-based'
        ];

        return json_encode($config, JSON_PRETTY_PRINT);
    }

    function generateAIPrompt($data, $appId) {
        $name = $data['name'];
        $description = $data['description'];
        $type = $data['type'];
        $fields = $data['fields'] ?? [];

        $fieldsDescription = '';
        if (!empty($fields)) {
            $fieldsDescription = "\n\n**Form Fields:**\n";
            foreach ($fields as $field) {
                $fieldsDescription .= "- {$field['label']} ({$field['type']}): {$field['placeholder']}\n";
            }
        }

        return <<<MARKDOWN
# AI Prompt for $name Applet

## App Overview
- **Name:** $name
- **Description:** $description
- **Type:** $type
- **App ID:** $appId$fieldsDescription

## Instructions for AI

You are helping to create a custom applet for an AI Dashboard system. The app structure is already set up with proper integrations, and you need to create the core functionality.

### What's Already Provided:
- **init.php**: Handles all AI Dashboard integrations, theming, chat bot communication, and loads your custom code
- **config.json**: App metadata and configuration
- **This prompt file**: Instructions for AI development

### What You Need to Create:

Create the content for **mini-app.php** that will be included in the init.php file. This file MUST contain:

1. **PHP Opening Tag**: Always start with `<?php` even if you don't use PHP functions
2. **HTML Content**: The main interface for the applet
3. **PHP Functions** (optional): Any server-side logic needed
4. **JavaScript Functions** (optional): Client-side functionality

**IMPORTANT**: Even if your applet is purely HTML/CSS/JavaScript, you MUST include the PHP opening tag `<?php` at the beginning of the file for the AI Dashboard to save it properly.

### Available Integration Functions:
- `sendToChat(data, customMessage)` - Send data to the chat bot
- `showNotification(message, type)` - Show success/error notifications
- `closeApp()` - Close the applet
- `appConfig` - JavaScript object with app configuration

### Required File Structure:
```php
<?php
// ALWAYS start with PHP opening tag - this is REQUIRED even for HTML-only applets
?>

<!-- Your custom HTML content goes here -->
<div class="space-y-4">
    <!-- Form fields, buttons, content, etc. -->
</div>

<!-- Action buttons (optional) -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
        Submit
    </button>
    <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <!-- Only include "Send to Chat" button if the applet generates data that would be useful to share with the AI assistant -->
    <!-- Examples: calculation results, generated content, analysis outputs, formatted data -->
    <!-- Do NOT include for: simple tools, games, pure display apps, or apps that don't produce shareable results -->
</div>
```

### JavaScript Guidelines:
```javascript
<script>
// Your custom functions
function submitForm() {
    // Handle form submission
    const data = collectFormData();
    // Process data...
    showNotification('Success!', 'success');
}

function collectFormData() {
    // Collect form data
    return {};
}

function sendDataToChat() {
    const data = collectFormData();
    sendToChat(data); // Uses built-in integration
}

// Only implement sendDataToChat() and include the "Send to Chat" button if your applet:
// - Generates useful results (calculations, analysis, formatted data)
// - Produces content that would be valuable to share with the AI assistant
// - Creates outputs that could be used in further AI conversations
//
// Do NOT include "Send to Chat" functionality for:
// - Simple utility tools that don't produce shareable results
// - Games or entertainment apps
// - Pure display or visualization apps without data output
// - Apps that only manipulate local data without generating insights
</script>
```

### Styling:
- Use Tailwind CSS classes (already loaded)
- Follow dark/light theme patterns: `text-gray-900 dark:text-white`
- Use consistent spacing and styling with the container

### Server-Side Functions (if needed):
```php
<?php
function handleAppletRequest(\$data) {
    // Custom API request handling
    switch (\$data['action']) {
        case 'custom_action':
            // Your logic here
            return ['success' => true, 'message' => 'Custom action completed'];
        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}
?>
```

## Critical Requirements:
1. **ALWAYS start your mini-app.php file with `<?php`** - This is mandatory for the AI Dashboard save system
2. **Use proper theme-aware styling** with Tailwind classes like `text-gray-900 dark:text-white`
3. **Only include "Send to Chat" button when appropriate** - Only if the applet generates useful data to share with AI
4. **Handle form validation** and provide user feedback
5. **Follow responsive design** principles for mobile compatibility

## Your Task:
Create a fully functional applet that implements the described functionality. Focus on:
1. **Starting with PHP tag** - Always begin with `<?php` even for HTML-only applets
2. **User interface** that matches the description and uses proper theming
3. **Form handling** and data collection with validation
4. **Selective chat bot integration** - Only include "Send to Chat" if the applet produces useful shareable results
5. **Error handling** and user feedback with notifications

**Important**: Evaluate whether your applet generates data that would be valuable to share with an AI assistant. Only include the "Send to Chat" button and sendDataToChat() function if it makes sense for the applet's purpose.

The code should be production-ready, follow modern web development best practices, and work seamlessly within the AI Dashboard applet system.
MARKDOWN;
    }

    function updateMiniAppsRegistry($appId, $data) {
        // Create or update a registry file for custom apps
        $registryFile = __DIR__ . '/custom-apps-registry.json';

        $registry = [];
        if (file_exists($registryFile)) {
            $registry = json_decode(file_get_contents($registryFile), true) ?: [];
        }

        $registry[$appId] = [
            'name' => $data['name'],
            'icon' => $data['icon'],
            'description' => $data['appDisplayDescription'], // Use display description for card display
            'type' => $data['type'],
            'created_at' => date('Y-m-d H:i:s'),
            'folder' => $appId,
            'structure' => 'folder-based'
        ];

        file_put_contents($registryFile, json_encode($registry, JSON_PRETTY_PRINT));
    }

    // Validate required fields
    $requiredFields = ['name', 'icon', 'appDisplayDescription', 'description', 'type'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            echo json_encode(['success' => false, 'error' => "Missing required field: $field"]);
            exit;
        }
    }

    // Sanitize app name for file system
    $appId = sanitizeAppName($data['name']);
    $appFolderPath = __DIR__ . '/' . $appId;

    // Check if app already exists
    if (file_exists($appFolderPath)) {
        echo json_encode(['success' => false, 'error' => 'An app with this name already exists']);
        exit;
    }

    // Create app folder
    if (!mkdir($appFolderPath, 0755, true)) {
        throw new Exception('Failed to create app folder');
    }

    // Generate and write the files
    $initContent = generateInitFile($data, $appId);
    $miniAppContent = generateMiniAppFile($data, $appId);
    $configContent = generateConfigFile($data, $appId);
    $promptContent = generateAIPrompt($data, $appId);

    // Write all files
    $files = [
        'init.php' => $initContent,
        'mini-app.php' => $miniAppContent,
        'config.json' => $configContent,
        'AI_PROMPT.md' => $promptContent
    ];

    foreach ($files as $filename => $content) {
        $filePath = $appFolderPath . '/' . $filename;
        if (file_put_contents($filePath, $content) === false) {
            throw new Exception("Failed to write $filename");
        }
    }

    // Update the mini-apps registry
    updateMiniAppsRegistry($appId, $data);

    echo json_encode([
        'success' => true,
        'appId' => $appId,
        'folderPath' => $appId,
        'message' => 'Mini app created successfully'
    ]);

} catch (Exception $e) {
    error_log('Fatal error in create-app.php: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} catch (Error $e) {
    error_log('PHP Fatal error in create-app.php: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'PHP Fatal error: ' . $e->getMessage()]);
}
?>
