# AI Prompt for Simple Coder Applet

## App Overview
- **Name:** Simple Coder
- **Description:** I would like this app to provide a simple, yet fully functional code editor. So I would like it to have syntax highlighting, line numbering, proper tab/spacing, fine in file, etc... Ideally it would use Codemirror and have a solid light and dark mode that change based on which theme mode the AI Dashboard is currently set to use. I want it to support all popular coding languages and have a copy button to easy copy the code inside the editor. I would also like to include a dropdown menu with several options like "Fix this code" or "Refine this code" or "Explain this code", etc.. (you pick out the wording and common statements like this that would be useful. And whichever is selected you can then click the "Send to chat" button (which I want you to add for AI chat bot integration) and make it so that it sends the selected statement and then the code after it so that the chat bot knows how to respond.
- **Type:** tool
- **Width:** full
- **App ID:** simple-coder

## Instructions for AI

You are helping to create a custom applet for an AI Dashboard system. The app structure is already set up with proper integrations, and you need to create the core functionality.

### What's Already Provided:
- **init.php**: Handles all AI Dashboard integrations, theming, chat bot communication, and loads your custom code
- **config.json**: App metadata and configuration
- **This prompt file**: Instructions for AI development

### What You Need to Create:

Create the content for **mini-app.php** that will be included in the init.php file. This file MUST contain:

1. **PHP Opening Tag**: Always start with `<?php` even if you don't use PHP functions
2. **HTML Content**: The main interface for the applet
3. **PHP Functions** (optional): Any server-side logic needed
4. **JavaScript Functions** (optional): Client-side functionality

**IMPORTANT**: Even if your applet is purely HTML/CSS/JavaScript, you MUST include the PHP opening tag `<?php` at the beginning of the file for the AI Dashboard to save it properly.

### Available Integration Functions:
- `sendToChat(data, customMessage)` - Send data to the chat bot
- `showNotification(message, type)` - Show success/error notifications
- `closeApp()` - Close the applet
- `appConfig` - JavaScript object with app configuration

### Required File Structure:
```php
<?php
// ALWAYS start with PHP opening tag - this is REQUIRED even for HTML-only applets
?>

<!-- Your custom HTML content goes here -->
<div class="space-y-4">
    <!-- Form fields, buttons, content, etc. -->
</div>

<!-- Action buttons (optional) -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
        Submit
    </button>
    <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <!-- IMPORTANT: Only include "Send to Chat" button if the user's description EXPLICITLY requests chat integration -->
    <!-- Do NOT include "Send to Chat" functionality unless specifically requested in the requirements -->
</div>
```

### JavaScript Guidelines:
```javascript
<script>
// Your custom functions
function submitForm() {
    // Handle form submission
    const data = collectFormData();
    // Process data...
    showNotification('Success!', 'success');
}

function collectFormData() {
    // Collect form data
    return {};
}

function sendDataToChat() {
    const data = collectFormData();
    sendToChat(data); // Uses built-in integration
}

// CRITICAL: Only implement sendDataToChat() and include the "Send to Chat" button if:
// - The user's description EXPLICITLY requests chat integration or sharing functionality
// - The requirements specifically mention sending data to the AI assistant
//
// Do NOT include "Send to Chat" functionality unless explicitly requested:
// - Do not assume the user wants chat integration
// - Do not add it "just in case" or because it might be useful
// - Only include it when the user specifically asks for it in their requirements
</script>
```

### Styling:
- Use Tailwind CSS classes (already loaded)
- Follow dark/light theme patterns: `text-gray-900 dark:text-white`
- Use consistent spacing and styling with the container

### Width Requirements:
Based on the selected width (full), style your applet accordingly:

- **Small (448px)**: Use `max-w-md` class for compact layouts, ideal for simple tools and calculators
- **Medium (672px)**: Use `max-w-2xl` class for standard forms and moderate content
- **Large (896px)**: Use `max-w-4xl` class for content-rich apps like editors or dashboards
- **Full (100%)**: Use `w-full` class to fill the entire container width for data tables or complex layouts

Apply the appropriate max-width class to your main container div to match the selected width.

### Server-Side Functions (if needed):
```php
<?php
function handleAppletRequest($data) {
    // Custom API request handling
    switch ($data['action']) {
        case 'custom_action':
            // Your logic here
            return ['success' => true, 'message' => 'Custom action completed'];
        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}
?>
```

## Critical Requirements:
1. **ALWAYS start your mini-app.php file with `<?php`** - This is mandatory for the AI Dashboard save system
2. **Use proper theme-aware styling** with Tailwind classes like `text-gray-900 dark:text-white`
3. **Only include "Send to Chat" if EXPLICITLY requested** - Do not add chat integration unless the user specifically asks for it
4. **Handle form validation** and provide user feedback
5. **Follow responsive design** principles for mobile compatibility

## Your Task:
Create a fully functional applet that implements the described functionality. Focus on:
1. **Starting with PHP tag** - Always begin with `<?php` even for HTML-only applets
2. **User interface** that matches the description and uses proper theming
3. **Form handling** and data collection with validation
4. **Chat integration only if requested** - Only include "Send to Chat" if explicitly requested in the user's requirements
5. **Error handling** and user feedback with notifications

**Important**: Only include the "Send to Chat" button and sendDataToChat() function if the user's requirements explicitly request chat integration or sharing functionality. Do not add it unless specifically requested.

The code should be production-ready, follow modern web development best practices, and work seamlessly within the AI Dashboard applet system.