<?php
/**
 * Custom Mini-App: Simple Coder
 *
 * This file contains the custom logic for your mini-app.
 * Edit this file to customize your app's functionality.
 *
 * Available functions from init.php:
 * - sendToChat(data, customMessage) - Send data to chat bot
 * - showNotification(message, type) - Show notifications
 * - closeApp() - Close the mini-app
 */

// Optional: Custom request handler
function handleMiniAppRequest($data) {
    switch ($data['action']) {
        case 'submit_form':
            // Custom form submission logic here
            return ['success' => true, 'message' => 'Form submitted successfully'];

        case 'send_to_chat':
            // Custom chat integration logic here
            return ['success' => true, 'message' => 'Sent to chat successfully'];

        default:
            return ['success' => false, 'error' => 'Unknown action'];
    }
}
?>

<!-- Custom Mini-App Content -->
<div class="space-y-4">

    <div class="mb-4">
        <label for="field_0" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Input</label>
        <input type="text" id="field_0" placeholder="Enter text here"
               class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
    </div>
</div>

<!-- Action Buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="submitForm()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
        Submit
    </button>
    <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
        Clear
    </button>
    <button onclick="sendDataToChat()" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600">
        Send to Chat
    </button>
</div>

<script>
    // Custom JavaScript functions

    function collectFormData() {
        const data = {};
        data['Input'] = document.getElementById('field_0').value;
        return data;
    }

    function submitForm() {
        const data = collectFormData();

        // You can customize this function to handle form submission
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'submit_form',
                data: data
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showNotification(result.message, 'success');
            } else {
                showNotification('Error: ' + result.error, 'error');
            }
        })
        .catch(error => {
            showNotification('Error submitting form', 'error');
        });
    }

    function clearForm() {
        document.querySelectorAll('input, textarea, select').forEach(element => {
            if (element.type === 'checkbox') {
                element.checked = false;
            } else {
                element.value = '';
            }
        });
    }

    function sendDataToChat() {
        const data = collectFormData();
        sendToChat(data); // Uses the built-in integration function
    }
</script>