<?php
?>

<!-- CodeMirror CSS and JS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/monokai.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/default.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/search/matchesonscrollbar.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/dialog/dialog.min.css">

<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/search/searchcursor.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/search/search.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/dialog/dialog.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/search/jump-to-line.min.js"></script>

<!-- Language modes -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/python/python.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/php/php.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/sql/sql.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/clike/clike.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/go/go.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/rust/rust.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/shell/shell.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/yaml/yaml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/markdown/markdown.min.js"></script>

<div class="w-full h-full flex flex-col bg-white dark:bg-gray-900">
    <!-- Header with controls -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-4">
            <!-- Language Selection -->
            <div class="flex items-center space-x-2">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Language:</label>
                <select id="languageSelect" class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                    <option value="javascript">JavaScript</option>
                    <option value="python">Python</option>
                    <option value="php">PHP</option>
                    <option value="xml">HTML/XML</option>
                    <option value="css">CSS</option>
                    <option value="sql">SQL</option>
                    <option value="text/x-java">Java</option>
                    <option value="text/x-csrc">C</option>
                    <option value="text/x-c++src">C++</option>
                    <option value="text/x-csharp">C#</option>
                    <option value="go">Go</option>
                    <option value="rust">Rust</option>
                    <option value="shell">Shell/Bash</option>
                    <option value="yaml">YAML</option>
                    <option value="markdown">Markdown</option>
                    <option value="text/plain">Plain Text</option>
                </select>
            </div>

            <!-- AI Action Selection -->
            <div class="flex items-center space-x-2">
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Action:</label>
                <select id="aiActionSelect" class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                    <option value="Fix this code">Fix this code</option>
                    <option value="Refine and optimize this code">Refine and optimize this code</option>
                    <option value="Explain this code in detail">Explain this code in detail</option>
                    <option value="Add comments to this code">Add comments to this code</option>
                    <option value="Convert this code to">Convert this code to another language</option>
                    <option value="Review this code for security issues">Review this code for security issues</option>
                    <option value="Write unit tests for this code">Write unit tests for this code</option>
                    <option value="Debug this code">Debug this code</option>
                    <option value="Simplify this code">Simplify this code</option>
                    <option value="Document this code">Document this code</option>
                </select>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="flex items-center space-x-2">
            <button id="copyBtn" class="px-3 py-1 text-sm bg-green-500 hover:bg-green-600 text-white rounded-md font-medium transition-colors">
                Copy Code
            </button>
            <button id="clearBtn" class="px-3 py-1 text-sm bg-gray-500 hover:bg-gray-600 text-white rounded-md font-medium transition-colors">
                Clear
            </button>
            <button id="sendToChatBtn" class="px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md font-medium transition-colors">
                Send to Chat
            </button>
        </div>
    </div>

    <!-- Editor Container -->
    <div class="flex-1 overflow-hidden">
        <textarea id="codeEditor" class="w-full h-full"></textarea>
    </div>

    <!-- Status bar -->
    <div class="flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-400">
        <div>
            <span id="cursorPos">Line 1, Column 1</span>
        </div>
        <div>
            <span id="charCount">0 characters</span>
            <span class="ml-4" id="lineCount">1 line</span>
        </div>
    </div>
</div>

<style>
/* Custom CodeMirror styling for dark mode */
.CodeMirror {
    height: 100% !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.4;
}

.dark .CodeMirror {
    background: #1f2937 !important;
    color: #f9fafb !important;
}

.dark .CodeMirror-gutters {
    background: #374151 !important;
    border-right: 1px solid #4b5563 !important;
}

.dark .CodeMirror-linenumber {
    color: #9ca3af !important;
}

.dark .CodeMirror-cursor {
    border-left: 1px solid #f9fafb !important;
}

.dark .CodeMirror-selected {
    background: #374151 !important;
}

.dark .CodeMirror-line::selection,
.dark .CodeMirror-line > span::selection,
.dark .CodeMirror-line > span > span::selection {
    background: #374151 !important;
}

/* Search dialog styling */
.dark .CodeMirror-dialog {
    background: #374151 !important;
    color: #f9fafb !important;
    border: 1px solid #4b5563 !important;
}

.dark .CodeMirror-dialog input {
    background: #1f2937 !important;
    color: #f9fafb !important;
    border: 1px solid #4b5563 !important;
}

/* Light mode adjustments */
.CodeMirror {
    background: #ffffff !important;
    color: #1f2937 !important;
}

.CodeMirror-gutters {
    background: #f9fafb !important;
    border-right: 1px solid #e5e7eb !important;
}

.CodeMirror-linenumber {
    color: #6b7280 !important;
}

/* Focus styles */
.CodeMirror-focused .CodeMirror-cursor {
    border-left: 2px solid #3b82f6 !important;
}
</style>

<script>
let editor;
let isDarkMode = false;

// Initialize the editor
function initializeEditor() {
    editor = CodeMirror.fromTextArea(document.getElementById('codeEditor'), {
        lineNumbers: true,
        mode: 'javascript',
        theme: 'default',
        indentUnit: 4,
        tabSize: 4,
        indentWithTabs: false,
        lineWrapping: true,
        autoCloseBrackets: true,
        matchBrackets: true,
        highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true},
        extraKeys: {
            'Ctrl-F': 'findPersistent',
            'Cmd-F': 'findPersistent',
            'F3': 'findNext',
            'Shift-F3': 'findPrev',
            'Ctrl-G': 'jumpToLine',
            'Cmd-G': 'jumpToLine',
            'Ctrl-H': 'replace',
            'Cmd-H': 'replace'
        }
    });

    // Update status bar on cursor activity
    editor.on('cursorActivity', updateStatusBar);
    editor.on('change', updateStatusBar);

    // Initialize theme based on current mode
    detectAndSetTheme();
    
    // Set up theme observer
    observeThemeChanges();
    
    // Set initial content
    editor.setValue('// Welcome to Simple Coder!\n// Start typing your code here...\n\nfunction helloWorld() {\n    console.log("Hello, World!");\n}\n\nhelloWorld();');
    
    // Focus the editor
    setTimeout(() => editor.focus(), 100);
}

// Detect current theme and set editor theme
function detectAndSetTheme() {
    isDarkMode = document.documentElement.classList.contains('dark') || 
                 document.body.classList.contains('dark') ||
                 document.querySelector('.dark') !== null;
    
    if (isDarkMode) {
        editor.setOption('theme', 'monokai');
    } else {
        editor.setOption('theme', 'default');
    }
}

// Observe theme changes
function observeThemeChanges() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && 
                (mutation.attributeName === 'class' || mutation.attributeName === 'data-theme')) {
                detectAndSetTheme();
            }
        });
    });

    // Observe changes on document element and body
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class', 'data-theme'] });
    observer.observe(document.body, { attributes: true, attributeFilter: ['class', 'data-theme'] });
    
    // Also check for theme changes in parent elements
    let parent = document.querySelector('.simple-coder')?.parentElement;
    while (parent && parent !== document.body) {
        observer.observe(parent, { attributes: true, attributeFilter: ['class', 'data-theme'] });
        parent = parent.parentElement;
    }
}

// Update status bar information
function updateStatusBar() {
    const cursor = editor.getCursor();
    const content = editor.getValue();
    const lines = content.split('\n').length;
    const chars = content.length;
    
    document.getElementById('cursorPos').textContent = `Line ${cursor.line + 1}, Column ${cursor.ch + 1}`;
    document.getElementById('charCount').textContent = `${chars} characters`;
    document.getElementById('lineCount').textContent = `${lines} ${lines === 1 ? 'line' : 'lines'}`;
}

// Language selection handler
document.getElementById('languageSelect').addEventListener('change', function() {
    const mode = this.value;
    editor.setOption('mode', mode);
    
    // Clear and refocus
    setTimeout(() => editor.focus(), 100);
});

// Copy button handler
document.getElementById('copyBtn').addEventListener('click', function() {
    const code = editor.getValue();
    if (!code.trim()) {
        showNotification('No code to copy!', 'warning');
        return;
    }
    
    navigator.clipboard.writeText(code).then(() => {
        showNotification('Code copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        editor.selectAll();
        document.execCommand('copy');
        showNotification('Code copied to clipboard!', 'success');
    });
});

// Clear button handler
document.getElementById('clearBtn').addEventListener('click', function() {
    if (editor.getValue().trim() && !confirm('Are you sure you want to clear all code?')) {
        return;
    }
    
    editor.setValue('');
    editor.focus();
    showNotification('Editor cleared!', 'success');
});

// Send to Chat button handler
document.getElementById('sendToChatBtn').addEventListener('click', function() {
    const code = editor.getValue();
    const action = document.getElementById('aiActionSelect').value;
    const language = document.getElementById('languageSelect').options[document.getElementById('languageSelect').selectedIndex].text;
    
    if (!code.trim()) {
        showNotification('Please enter some code first!', 'warning');
        return;
    }
    
    // Prepare the message for the AI
    let message = `${action}`;
    if (action === "Convert this code to") {
        message += " [specify target language]";
    }
    message += `:\n\n\`\`\`${language.toLowerCase()}\n${code}\n\`\`\``;
    
    // Send to chat using the provided integration function
    sendToChat({ code: code, action: action, language: language }, message);
    
    showNotification('Code sent to chat!', 'success');
});

// Initialize when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure CodeMirror is fully loaded
    setTimeout(initializeEditor, 100);
});

// Handle window resize
window.addEventListener('resize', function() {
    if (editor) {
        setTimeout(() => editor.refresh(), 100);
    }
});
</script>