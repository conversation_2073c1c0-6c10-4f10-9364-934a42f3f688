<?php
// Simple Coder - Full-featured code editor with AI integration
?>

<div class="w-full space-y-4">
    <!-- Header with controls -->
    <div class="flex flex-wrap items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <!-- Language Selector -->
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Language:</label>
            <select id="languageSelect" class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                <option value="javascript">JavaScript</option>
                <option value="python">Python</option>
                <option value="html">HTML</option>
                <option value="css">CSS</option>
                <option value="php">PHP</option>
                <option value="java">Java</option>
                <option value="cpp">C++</option>
                <option value="csharp">C#</option>
                <option value="sql">SQL</option>
                <option value="json">JSON</option>
                <option value="xml">XML</option>
                <option value="markdown">Markdown</option>
                <option value="yaml">YAML</option>
                <option value="shell">Shell</option>
                <option value="go">Go</option>
                <option value="rust">Rust</option>
                <option value="typescript">TypeScript</option>
                <option value="ruby">Ruby</option>
            </select>
        </div>

        <!-- AI Action Selector -->
        <div class="flex items-center space-x-2">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Action:</label>
            <select id="actionSelect" class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                <option value="explain">Explain this code</option>
                <option value="fix">Fix this code</option>
                <option value="refactor">Refactor this code</option>
                <option value="optimize">Optimize this code</option>
                <option value="comment">Add comments to this code</option>
                <option value="debug">Debug this code</option>
                <option value="convert">Convert this code to another language</option>
                <option value="test">Write tests for this code</option>
                <option value="review">Review this code for best practices</option>
                <option value="document">Generate documentation for this code</option>
            </select>
        </div>

        <!-- Utility Buttons -->
        <div class="flex items-center space-x-2 ml-auto">
            <button onclick="copyCode()" class="px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 focus:ring-2 focus:ring-green-500 transition-colors">
                📋 Copy
            </button>
            <button onclick="clearEditor()" class="px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600 focus:ring-2 focus:ring-red-500 transition-colors">
                🗑️ Clear
            </button>
        </div>
    </div>

    <!-- Code Editor Container -->
    <div class="relative border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
        <div id="editor" class="min-h-96"></div>
    </div>

    <!-- Search Bar (Initially Hidden) -->
    <div id="searchBar" class="hidden flex items-center space-x-2 p-3 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg">
        <input type="text" id="searchInput" placeholder="Search in code..." class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
        <button onclick="findNext()" class="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600">Next</button>
        <button onclick="findPrev()" class="px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600">Prev</button>
        <button onclick="closeSearch()" class="px-3 py-1 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600">Close</button>
    </div>

    <!-- Stats and Info -->
    <div class="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div id="editorStats">Lines: 1 | Characters: 0 | Selection: 0</div>
        <div class="flex space-x-4">
            <span>Ctrl+F: Find</span>
            <span>Ctrl+S: Save to clipboard</span>
            <span>Tab: Indent</span>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button onclick="sendToAI()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 transition-colors">
            🤖 Send to Chat
        </button>
        <button onclick="formatCode()" class="py-2 px-4 bg-purple-500 text-white rounded-lg font-medium hover:bg-purple-600 focus:ring-2 focus:ring-purple-500 transition-colors">
            ✨ Format
        </button>
        <button onclick="toggleFullscreen()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 focus:ring-2 focus:ring-gray-500 transition-colors">
            ⛶ Fullscreen
        </button>
    </div>
</div>

<!-- Load CodeMirror and dependencies -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/theme/monokai.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/theme/default.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/search/matchesonscrollbar.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/dialog/dialog.min.css">

<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/edit/closebrackets.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/edit/matchbrackets.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/search/search.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/search/searchcursor.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/dialog/dialog.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/selection/active-line.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/fold/foldcode.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/fold/foldgutter.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/addon/fold/brace-fold.min.js"></script>

<!-- Language Mode Scripts -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/python/python.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/php/php.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/clike/clike.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/sql/sql.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/markdown/markdown.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/yaml/yaml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/shell/shell.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/go/go.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/rust/rust.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.0/mode/ruby/ruby.min.js"></script>

<script>
let editor;
let isFullscreen = false;
let searchCursor;

// Initialize CodeMirror
function initializeEditor() {
    editor = CodeMirror(document.getElementById('editor'), {
        value: '// Welcome to Simple Coder!\n// Start typing your code here...\n\nfunction hello() {\n    console.log("Hello, World!");\n}',
        mode: 'javascript',
        theme: getTheme(),
        lineNumbers: true,
        autoCloseBrackets: true,
        matchBrackets: true,
        indentUnit: 4,
        smartIndent: true,
        lineWrapping: true,
        foldGutter: true,
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        styleActiveLine: true,
        extraKeys: {
            "Ctrl-F": openSearch,
            "Cmd-F": openSearch,
            "Ctrl-S": copyCode,
            "Cmd-S": copyCode,
            "F11": toggleFullscreen,
            "Esc": function(cm) {
                if (isFullscreen) toggleFullscreen();
                else closeSearch();
            }
        }
    });

    // Update stats on change
    editor.on('change', updateStats);
    editor.on('cursorActivity', updateStats);
    
    // Language change handler
    document.getElementById('languageSelect').addEventListener('change', changeLanguage);
    
    // Update theme when system theme changes
    watchThemeChanges();
    
    updateStats();
}

// Get current theme based on dark mode
function getTheme() {
    return document.documentElement.classList.contains('dark') ? 'monokai' : 'default';
}

// Watch for theme changes
function watchThemeChanges() {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class') {
                editor.setOption('theme', getTheme());
            }
        });
    });
    
    observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
    });
}

// Language mode mapping
const languageModes = {
    'javascript': 'javascript',
    'typescript': 'javascript',
    'python': 'python',
    'html': 'htmlmixed',
    'css': 'css',
    'php': 'php',
    'java': 'text/x-java',
    'cpp': 'text/x-c++src',
    'csharp': 'text/x-csharp',
    'sql': 'sql',
    'json': 'application/json',
    'xml': 'xml',
    'markdown': 'markdown',
    'yaml': 'yaml',
    'shell': 'shell',
    'go': 'go',
    'rust': 'rust',
    'ruby': 'ruby'
};

// Change language mode
function changeLanguage() {
    const language = document.getElementById('languageSelect').value;
    const mode = languageModes[language] || 'text/plain';
    editor.setOption('mode', mode);
    updateStats();
}

// Update editor statistics
function updateStats() {
    const doc = editor.getDoc();
    const selection = doc.getSelection();
    const lineCount = doc.lineCount();
    const charCount = doc.getValue().length;
    const selectionLength = selection.length;
    
    document.getElementById('editorStats').textContent = 
        `Lines: ${lineCount} | Characters: ${charCount} | Selection: ${selectionLength}`;
}

// Copy code to clipboard
function copyCode() {
    const code = editor.getValue();
    navigator.clipboard.writeText(code).then(() => {
        showNotification('Code copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Code copied to clipboard!', 'success');
    });
}

// Clear editor
function clearEditor() {
    if (confirm('Are you sure you want to clear all code?')) {
        editor.setValue('');
        showNotification('Editor cleared', 'success');
    }
}

// Send to AI chat
function sendToAI() {
    const code = editor.getValue().trim();
    const action = document.getElementById('actionSelect').value;
    const language = document.getElementById('languageSelect').value;
    
    if (!code) {
        showNotification('Please enter some code first', 'error');
        return;
    }
    
    const actionMessages = {
        'explain': 'Please explain this code',
        'fix': 'Please fix any issues in this code',
        'refactor': 'Please refactor this code to improve it',
        'optimize': 'Please optimize this code for better performance',
        'comment': 'Please add helpful comments to this code',
        'debug': 'Please help me debug this code',
        'convert': 'Please convert this code to another language',
        'test': 'Please write unit tests for this code',
        'review': 'Please review this code and suggest improvements',
        'document': 'Please generate documentation for this code'
    };
    
    const message = `${actionMessages[action]} (${language.toUpperCase()}):

\`\`\`${language}
${code}
\`\`\``;

    sendToChat({
        action: action,
        language: language,
        code: code
    }, message);
    
    showNotification('Code sent to AI chat!', 'success');
}

// Format code (basic indentation)
function formatCode() {
    const code = editor.getValue();
    editor.setValue(code);
    editor.execCommand('selectAll');
    editor.execCommand('indentAuto');
    editor.setCursor(0, 0);
    showNotification('Code formatted!', 'success');
}

// Toggle fullscreen mode
function toggleFullscreen() {
    const editorContainer = document.getElementById('editor').parentElement;
    
    if (!isFullscreen) {
        editorContainer.style.position = 'fixed';
        editorContainer.style.top = '0';
        editorContainer.style.left = '0';
        editorContainer.style.width = '100vw';
        editorContainer.style.height = '100vh';
        editorContainer.style.zIndex = '9999';
        editorContainer.style.backgroundColor = getComputedStyle(document.body).backgroundColor;
        isFullscreen = true;
        showNotification('Entered fullscreen mode (Press Esc to exit)', 'success');
    } else {
        editorContainer.style.position = '';
        editorContainer.style.top = '';
        editorContainer.style.left = '';
        editorContainer.style.width = '';
        editorContainer.style.height = '';
        editorContainer.style.zIndex = '';
        editorContainer.style.backgroundColor = '';
        isFullscreen = false;
        showNotification('Exited fullscreen mode', 'success');
    }
    
    editor.refresh();
}

// Search functionality
function openSearch() {
    document.getElementById('searchBar').classList.remove('hidden');
    document.getElementById('searchInput').focus();
}

function closeSearch() {
    document.getElementById('searchBar').classList.add('hidden');
    if (searchCursor) {
        searchCursor.clear();
        searchCursor = null;
    }
}

function findNext() {
    const query = document.getElementById('searchInput').value;
    if (!query) return;
    
    if (!searchCursor) {
        searchCursor = editor.getSearchCursor(query);
    }
    
    if (searchCursor.findNext()) {
        editor.setSelection(searchCursor.from(), searchCursor.to());
        editor.scrollIntoView(searchCursor.from());
    } else {
        // Start from beginning
        searchCursor = editor.getSearchCursor(query);
        if (searchCursor.findNext()) {
            editor.setSelection(searchCursor.from(), searchCursor.to());
            editor.scrollIntoView(searchCursor.from());
        } else {
            showNotification('No matches found', 'error');
        }
    }
}

function findPrev() {
    const query = document.getElementById('searchInput').value;
    if (!query) return;
    
    if (!searchCursor) {
        searchCursor = editor.getSearchCursor(query, editor.getCursor());
    }
    
    if (searchCursor.findPrevious()) {
        editor.setSelection(searchCursor.from(), searchCursor.to());
        editor.scrollIntoView(searchCursor.from());
    } else {
        // Start from end
        searchCursor = editor.getSearchCursor(query, {line: editor.lastLine(), ch: 0});
        if (searchCursor.findPrevious()) {
            editor.setSelection(searchCursor.from(), searchCursor.to());
            editor.scrollIntoView(searchCursor.from());
        } else {
            showNotification('No matches found', 'error');
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeEditor);

// Handle search input enter key
document.addEventListener('keydown', function(e) {
    if (e.target.id === 'searchInput' && e.key === 'Enter') {
        e.preventDefault();
        if (e.shiftKey) {
            findPrev();
        } else {
            findNext();
        }
    }
});
</script>

<style>
/* Custom styles for better integration */
.CodeMirror {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    height: auto;
    min-height: 400px;
}

.CodeMirror-scroll {
    min-height: 400px;
}

/* Dark theme adjustments */
.dark .CodeMirror.cm-s-monokai {
    background: #1f2937;
    color: #f9fafb;
}

.dark .CodeMirror.cm-s-monokai .CodeMirror-gutters {
    background: #374151;
    border-right: 1px solid #4b5563;
}

.dark .CodeMirror.cm-s-monokai .CodeMirror-linenumber {
    color: #9ca3af;
}

/* Light theme adjustments */
.CodeMirror.cm-s-default {
    background: #ffffff;
    color: #1f2937;
}

.CodeMirror.cm-s-default .CodeMirror-gutters {
    background: #f9fafb;
    border-right: 1px solid #e5e7eb;
}

.CodeMirror.cm-s-default .CodeMirror-linenumber {
    color: #6b7280;
}

/* Active line highlighting */
.CodeMirror-activeline-background {
    background: rgba(59, 130, 246, 0.1) !important;
}

/* Selection styling */
.CodeMirror-selected {
    background: rgba(59, 130, 246, 0.3) !important;
}

/* Matching brackets */
.CodeMirror-matchingbracket {
    background: rgba(34, 197, 94, 0.3) !important;
    color: inherit !important;
}

/* Search highlighting */
.CodeMirror-searching {
    background: rgba(251, 191, 36, 0.5) !important;
}

/* Fullscreen styles */
.CodeMirror-fullscreen {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999;
}
</style>