<?php
/**
 * Delete Custom Mini App
 * Handles deletion of custom mini-apps
 */

// Include necessary files
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Set JSON response header
header('Content-Type: application/json');

// Handle POST requests only
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['appId'])) {
    echo json_encode(['success' => false, 'error' => 'Missing app ID']);
    exit;
}

$appId = $data['appId'];

// Helper function to recursively delete a directory
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $files = array_diff(scandir($dir), ['.', '..']);

    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }

    return rmdir($dir);
}

try {
    $registryFile = __DIR__ . '/custom-apps-registry.json';
    
    // Load registry
    if (!file_exists($registryFile)) {
        echo json_encode(['success' => false, 'error' => 'App not found']);
        exit;
    }
    
    $registry = json_decode(file_get_contents($registryFile), true);
    
    if (!$registry || !isset($registry[$appId])) {
        echo json_encode(['success' => false, 'error' => 'App not found in registry']);
        exit;
    }
    
    // Delete app files/folders based on structure
    $appData = $registry[$appId];

    // Check if it's folder-based (new structure)
    if (isset($appData['folder']) || (isset($appData['structure']) && $appData['structure'] === 'folder-based')) {
        $appFolder = __DIR__ . '/' . ($appData['folder'] ?? $appId);

        if (is_dir($appFolder)) {
            if (!deleteDirectory($appFolder)) {
                throw new Exception('Failed to delete app folder');
            }
        }
    }
    // Handle file-based (legacy structure)
    elseif (isset($appData['file'])) {
        $appFile = __DIR__ . '/' . $appData['file'];

        if (file_exists($appFile)) {
            if (!unlink($appFile)) {
                throw new Exception('Failed to delete app file');
            }
        }
    }
    
    // Remove from registry
    unset($registry[$appId]);
    
    // Save updated registry
    if (file_put_contents($registryFile, json_encode($registry, JSON_PRETTY_PRINT)) === false) {
        throw new Exception('Failed to update registry');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Mini app deleted successfully'
    ]);
    
} catch (Exception $e) {
    error_log('Mini app deletion error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
