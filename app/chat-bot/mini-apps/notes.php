<?php
/**
 * Chat <PERSON> - Notes Mini App
 * 
 * A quick note-taking app with auto-save functionality
 * that integrates with the <PERSON><PERSON> Bo<PERSON> assistant.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];
$dataStorage = $app->getDataStorage();
$userId = $_SESSION['user_id'];

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'save_note':
            echo json_encode(saveNote($input, $dataStorage, $userId));
            break;
        case 'load_notes':
            echo json_encode(loadNotes($dataStorage, $userId));
            break;
        case 'delete_note':
            echo json_encode(deleteNote($input, $dataStorage, $userId));
            break;
        case 'send_to_chat':
            echo json_encode(sendToChat($input, $app));
            break;
        // Removed register_integration to avoid API issues
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
    exit;
}

/**
 * Save note
 */
function saveNote($input, $dataStorage, $userId) {
    try {
        $noteId = $input['note_id'] ?? uniqid('note_');
        $title = trim($input['title'] ?? '');
        $content = trim($input['content'] ?? '');
        
        if (empty($content)) {
            throw new Exception('Note content cannot be empty');
        }
        
        if (empty($title)) {
            $title = substr($content, 0, 50) . (strlen($content) > 50 ? '...' : '');
        }
        
        $noteData = [
            'title' => $title,
            'content' => $content,
            'word_count' => str_word_count($content),
            'char_count' => strlen($content)
        ];
        
        $success = $dataStorage->storeData($userId, 'notes', $noteId, $noteData, [
            'type' => 'quick_note',
            'source' => 'mini_app'
        ]);
        
        if ($success) {
            return [
                'success' => true,
                'message' => 'Note saved successfully',
                'note_id' => $noteId,
                'title' => $title
            ];
        } else {
            throw new Exception('Failed to save note');
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Load notes
 */
function loadNotes($dataStorage, $userId) {
    try {
        $notes = $dataStorage->getData($userId, 'notes');
        
        $formattedNotes = [];
        foreach ($notes as $note) {
            $formattedNotes[] = [
                'id' => $note['data_key'],
                'title' => $note['data_value']['title'] ?? 'Untitled',
                'content' => $note['data_value']['content'] ?? '',
                'word_count' => $note['data_value']['word_count'] ?? 0,
                'char_count' => $note['data_value']['char_count'] ?? 0,
                'created_at' => $note['created_at'] ?? '',
                'updated_at' => $note['updated_at'] ?? ''
            ];
        }
        
        return [
            'success' => true,
            'notes' => $formattedNotes,
            'count' => count($formattedNotes)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Delete note
 */
function deleteNote($input, $dataStorage, $userId) {
    try {
        $noteId = $input['note_id'] ?? '';
        
        if (empty($noteId)) {
            throw new Exception('Note ID is required');
        }
        
        $success = $dataStorage->deleteData($userId, 'notes', $noteId);
        
        if ($success) {
            return [
                'success' => true,
                'message' => 'Note deleted successfully'
            ];
        } else {
            throw new Exception('Failed to delete note');
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Send note to chat
 */
function sendToChat($input, $app) {
    try {
        $content = $input['content'] ?? '';
        $title = $input['title'] ?? '';

        if (empty($content)) {
            throw new Exception('Note content is required');
        }

        // Simple approach: Save directly to data storage
        $dataStorage = $app->getDataStorage();
        $userId = $_SESSION['user_id'] ?? 'anonymous';

        // Create a conversation ID
        $conversationId = 'conv_note_' . uniqid();

        // Format the message
        $noteTitle = !empty($title) ? $title : 'Quick Note';
        $formattedMessage = "📝 **Quick Notes - {$noteTitle}**\n\n{$content}";

        // Save the message
        $aiResponse = 'I received your note about "' . $noteTitle . '". This looks useful! Would you like me to help you organize this information or expand on any particular points?';

        $success = $dataStorage->saveChatMessage(
            $userId,
            $conversationId,
            $formattedMessage,
            $aiResponse,
            'notes',
            0,
            'mini_app_integration',
            ['mini_app_id' => 'notes', 'title' => $title, 'word_count' => str_word_count($content)]
        );

        // Debug: Log the save attempt
        error_log("Notes sendToChat: userId=$userId, conversationId=$conversationId, success=" . ($success ? 'true' : 'false'));

        if ($success) {
            return [
                'success' => true,
                'message' => 'Note sent to chat successfully',
                'conversation_id' => $conversationId,
                'redirect_url' => "?route=/chat&conversation_id=" . $conversationId
            ];
        } else {
            throw new Exception('Failed to save note to chat');
        }

    } catch (Exception $e) {
        error_log("Notes sendToChat error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Register notes integration with chat bot
 */
function registerIntegration($app) {
    try {
        $integrationData = [
            'action' => 'register_mini_app',
            'mini_app_id' => 'notes',
            'capabilities' => [
                'create_note' => [
                    'name' => 'Create Note',
                    'description' => 'Create a new note with title and content',
                    'parameters' => ['title', 'content']
                ],
                'search_notes' => [
                    'name' => 'Search Notes',
                    'description' => 'Search through saved notes',
                    'parameters' => ['query']
                ]
            ]
        ];

        $response = makeIntegrationRequest($integrationData);

        return $response ?: [
            'success' => false,
            'error' => 'Failed to register integration'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Make request to integration API
 */
function makeIntegrationRequest($data) {
    $url = '../api/mini-app-integration.php';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        return json_decode($response, true);
    }

    return null;
}

// Removed complex integration function - using direct approach now

// If not an API request, return the mini-app HTML

// Check for theme and padding parameters
$theme = $_GET['theme'] ?? 'dark';
$addPadding = isset($_GET['padding']) && $_GET['padding'] === 'true';

// Set body classes based on parameters
$bodyClasses = [];
if ($theme === 'light') {
    $bodyClasses[] = 'bg-gray-100';
} else {
    $bodyClasses[] = 'bg-gray-100 dark:bg-gray-900';
}
if ($addPadding) {
    $bodyClasses[] = 'pt-4';
}
$bodyClassString = implode(' ', $bodyClasses);
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo $theme; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Notes - Chat Bot Mini App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>
<body class="<?php echo $bodyClassString; ?>">
    <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Quick Notes</h2>
            <button onclick="closeNotes()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Note Editor -->
            <div class="space-y-4">
                <div>
                    <label for="noteTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Title (optional)
                    </label>
                    <input type="text" id="noteTitle" placeholder="Enter note title..."
                           class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
                
                <div>
                    <label for="noteContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Content
                    </label>
                    <textarea id="noteContent" rows="12" placeholder="Start typing your note..."
                              class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"></textarea>
                </div>
                
                <!-- Stats -->
                <div class="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span id="wordCount">0 words</span>
                    <span id="charCount">0 characters</span>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <button onclick="saveNote()" id="saveBtn" 
                            class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600">
                        Save Note
                    </button>
                    <button onclick="clearNote()" 
                            class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600">
                        Clear
                    </button>
                    <button onclick="sendCurrentNoteToChat()" 
                            class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600">
                        Send to Chat
                    </button>
                </div>
            </div>
            
            <!-- Saved Notes -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Saved Notes</h3>
                    <button onclick="loadNotes()" 
                            class="py-1 px-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm hover:bg-gray-400 dark:hover:bg-gray-500">
                        Refresh
                    </button>
                </div>
                
                <div id="notesList" class="space-y-2 max-h-96 overflow-y-auto">
                    <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                        Loading notes...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentNoteId = null;
        let autoSaveTimeout = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadNotes();
            setupAutoSave();
            updateStats();
        });

        // Removed integration registration to avoid API issues
        
        function setupAutoSave() {
            const content = document.getElementById('noteContent');
            const title = document.getElementById('noteTitle');
            
            [content, title].forEach(element => {
                element.addEventListener('input', function() {
                    updateStats();
                    
                    // Auto-save after 2 seconds of inactivity
                    clearTimeout(autoSaveTimeout);
                    autoSaveTimeout = setTimeout(() => {
                        if (content.value.trim()) {
                            saveNote(true); // Silent save
                        }
                    }, 2000);
                });
            });
        }
        
        function updateStats() {
            const content = document.getElementById('noteContent').value;
            const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
            const charCount = content.length;
            
            document.getElementById('wordCount').textContent = `${wordCount} words`;
            document.getElementById('charCount').textContent = `${charCount} characters`;
        }
        
        async function saveNote(silent = false) {
            const title = document.getElementById('noteTitle').value.trim();
            const content = document.getElementById('noteContent').value.trim();
            
            if (!content) {
                if (!silent) alert('Please enter some content before saving');
                return;
            }
            
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.textContent;
            
            if (!silent) {
                saveBtn.textContent = 'Saving...';
                saveBtn.disabled = true;
            }
            
            try {
                const response = await fetch('notes.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'save_note',
                        note_id: currentNoteId,
                        title: title,
                        content: content
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentNoteId = result.note_id;
                    if (!silent) {
                        alert('Note saved successfully!');
                        loadNotes();
                    }
                } else {
                    if (!silent) alert('Error saving note: ' + result.error);
                }
            } catch (error) {
                if (!silent) alert('Error saving note');
                console.error('Save error:', error);
            } finally {
                if (!silent) {
                    saveBtn.textContent = originalText;
                    saveBtn.disabled = false;
                }
            }
        }
        
        async function loadNotes() {
            try {
                const response = await fetch('notes.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'load_notes'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayNotes(result.notes);
                } else {
                    document.getElementById('notesList').innerHTML = 
                        '<div class="text-center text-red-500 py-4">Error loading notes</div>';
                }
            } catch (error) {
                document.getElementById('notesList').innerHTML = 
                    '<div class="text-center text-red-500 py-4">Error loading notes</div>';
                console.error('Load error:', error);
            }
        }
        
        function displayNotes(notes) {
            const notesList = document.getElementById('notesList');
            
            if (notes.length === 0) {
                notesList.innerHTML = 
                    '<div class="text-center text-gray-500 dark:text-gray-400 py-8">No notes saved yet</div>';
                return;
            }
            
            notesList.innerHTML = notes.map(note => `
                <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="flex items-start justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white truncate">${note.title}</h4>
                        <div class="flex space-x-1 ml-2">
                            <button onclick="editNote('${note.id}')" 
                                    class="text-blue-500 hover:text-blue-700 text-sm">Edit</button>
                            <button onclick="sendNoteToChat('${note.id}')" 
                                    class="text-green-500 hover:text-green-700 text-sm">Send</button>
                            <button onclick="deleteNote('${note.id}')" 
                                    class="text-red-500 hover:text-red-700 text-sm">Delete</button>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">${note.content.substring(0, 100)}${note.content.length > 100 ? '...' : ''}</p>
                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
                        <span>${note.word_count} words</span>
                        <span>${new Date(note.updated_at).toLocaleDateString()}</span>
                    </div>
                </div>
            `).join('');
        }
        
        function editNote(noteId) {
            // Find the note and load it into the editor
            // This would require storing the notes data or making another API call
            alert('Edit functionality would load the note into the editor');
        }
        
        async function deleteNote(noteId) {
            if (!confirm('Are you sure you want to delete this note?')) {
                return;
            }
            
            try {
                const response = await fetch('notes.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'delete_note',
                        note_id: noteId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    loadNotes();
                } else {
                    alert('Error deleting note: ' + result.error);
                }
            } catch (error) {
                alert('Error deleting note');
                console.error('Delete error:', error);
            }
        }
        
        async function sendNoteToChat(noteId) {
            alert('Send to chat functionality would integrate with the main chat interface');
        }
        
        async function sendCurrentNoteToChat() {
            const title = document.getElementById('noteTitle').value.trim();
            const content = document.getElementById('noteContent').value.trim();

            if (!content) {
                alert('Please enter some content before sending to chat');
                return;
            }

            // Check if we can send directly to the chat bot window
            if (window.chatBotWindow && !window.chatBotWindow.closed) {
                try {
                    // Format the note message
                    const noteMessage = title ?
                        `📝 **${title}**\n\n${content}` :
                        `📝 ${content}`;

                    // Call the function in the chat bot window
                    window.chatBotWindow.addMessageFromMiniApp(noteMessage);

                    // Focus the chat bot window
                    window.chatBotWindow.focus();

                    // Show success message
                    showNotification('Note sent to chat successfully!', 'success');

                    return; // Exit early, no need for API call
                } catch (e) {
                    console.error('Error communicating with chat bot window:', e);
                    // Fall through to API call
                }
            }

            // Fallback: Use API call if direct communication failed
            try {
                const response = await fetch('notes.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'send_to_chat',
                        title: title,
                        content: content
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Show success message
                    showNotification('Note sent to chat successfully!', 'success');

                    // Open in new tab as fallback
                    if (result.redirect_url) {
                        window.open(result.redirect_url, '_blank');
                    }
                } else {
                    showNotification('Error sending to chat: ' + result.error, 'error');
                }
            } catch (error) {
                alert('Error sending to chat');
                console.error('Send error:', error);
            }
        }

        // Helper function for notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        function clearNote() {
            if (confirm('Clear the current note?')) {
                document.getElementById('noteTitle').value = '';
                document.getElementById('noteContent').value = '';
                currentNoteId = null;
                updateStats();
            }
        }
        
        function closeNotes() {
            if (window.parent && window.parent.closeMiniApp) {
                window.parent.closeMiniApp();
            } else {
                window.close();
            }
        }
    </script>
</body>
</html>
