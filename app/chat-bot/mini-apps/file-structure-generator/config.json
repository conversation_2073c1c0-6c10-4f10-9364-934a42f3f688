{"name": "File Structure Generator", "icon": "🗃", "description": "I want this app to allow the user to paste in a file structure that is represent by text like this:\n\n└── 📁 lib/\n\n├── 🐘 init.php\n\n├── 📁 functions/\n\n│ ├── 🐘 admin-bar-menu.php\n\n│ └── 🐘 general.php\n\n├── 📁 languages/\n\n│ └── 📄 instant-ide-manager.pot\n\n├── 📁 admin/\n\n│ ├── 🐘 settings.php\n\n│ ├── 🐘 build-menu.php\n\n│ ├── 📁 boxes/\n\n│ │ ├── 🐘 settings.php\n\n│ │ └── 🐘 admin-access.php\n\n│ └── 📁 update/\n\n│ ├── 🐘 edd-updater.php\n\n│ ├── 🐘 update.php\n\n│ └── 🐘 EDD_SL_Plugin_Updater.php\n\n├── 📁 js/\n\n│ ├── 📜 scripts.js\n\n│ └── 📜 admin-options.js\n\n└── 📁 css/\n\n├── 🎨 admin.css\n\n├── 🎨 icons.css\n\n└── 📁 fonts/\n\n├── 🖼️ freelancer.svg\n\n├── 📄 freelancer.ttf\n\n├── 📄 freelancer.woff2\n\n├── 📄 freelancer.woff\n\n└── 📄 freelancer.eot\n\n\n\nBe sure that it accounts for a wide array of file structure text representations, not just the specific one above. Also make sure to include a basic example of what file structure text looks like to make it clear to the user how to use the app. And then I want that file structure text to result into a zipped file that includes the files and folders that accurately represent the structure text. I then want this zipped file downloaded to the local computer.", "type": "form", "id": "file-structure-generator", "version": "1.0.0", "created_at": "2025-06-17 15:12:33", "fields": [{"label": "Input", "type": "text", "placeholder": "Enter text here"}], "editable": true, "structure": "folder-based"}