<?php
// Required PHP opening tag for AI Dashboard integration
?>

<div class="space-y-4">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">File Structure Generator</h2>
        <p class="text-gray-600 dark:text-gray-400">Convert ASCII file structure to downloadable ZIP</p>
    </div>

    <!-- Example Section -->
    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Supported Formats:</h3>
        <div class="grid md:grid-cols-2 gap-4">
            <div>
                <p class="text-xs text-gray-600 dark:text-gray-400 mb-1">Box Drawing Characters:</p>
                <pre class="text-xs text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 p-2 rounded border overflow-x-auto"><code>lib/
├── init.php
├── functions/
│   ├── admin.php
│   └── utils.php
└── css/
    └── style.css</code></pre>
            </div>
            <div>
                <p class="text-xs text-gray-600 dark:text-gray-400 mb-1">ASCII Characters:</p>
                <pre class="text-xs text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 p-2 rounded border overflow-x-auto"><code>lib/
|-- init.php
|-- functions/
|   |-- admin.php
|   +-- utils.php
+-- css/
    +-- style.css</code></pre>
            </div>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
            <strong>Tip:</strong> Folders should end with "/" - files without extension are also supported
        </p>
    </div>

    <!-- Input Section -->
    <div>
        <label for="fileStructure" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Paste Your File Structure:
        </label>
        <textarea
            id="fileStructure"
            placeholder="Paste your ASCII file structure here..."
            class="w-full h-64 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-mono text-sm resize-vertical focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
    </div>

    <!-- Options -->
    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
        <h4 class="font-medium text-gray-900 dark:text-white">Options:</h4>
        <label class="flex items-center space-x-2">
            <input type="checkbox" id="createReadme" checked class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
            <span class="text-sm text-gray-700 dark:text-gray-300">Include README.md with original structure</span>
        </label>
        <label class="flex items-center space-x-2">
            <input type="checkbox" id="addGitkeep" class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
            <span class="text-sm text-gray-700 dark:text-gray-300">Add .gitkeep files to empty directories</span>
        </label>
        <div class="flex items-center space-x-2">
            <label for="zipName" class="text-sm text-gray-700 dark:text-gray-300">ZIP filename:</label>
            <input type="text" id="zipName" value="file-structure" 
                   class="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500">
            <span class="text-sm text-gray-500">.zip</span>
        </div>
    </div>

    <!-- Preview Section -->
    <div id="previewSection" class="hidden bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h4 class="font-medium text-gray-900 dark:text-white mb-2">Parsed Structure Preview:</h4>
        <div id="structurePreview" class="text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 p-3 rounded border max-h-40 overflow-y-auto font-mono"></div>
        <p id="itemCount" class="text-xs text-gray-500 dark:text-gray-400 mt-2"></p>
    </div>

    <!-- Status -->
    <div id="status" class="hidden p-3 rounded-lg"></div>
</div>

<!-- Action buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="generateZip()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 disabled:opacity-50 transition-colors" id="generateBtn">
        Generate & Download ZIP
    </button>
    <button onclick="previewStructure()" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 transition-colors">
        Preview
    </button>
    <button onclick="clearForm()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 transition-colors">
        Clear
    </button>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script>
function clearForm() {
    document.getElementById('fileStructure').value = '';
    document.getElementById('previewSection').classList.add('hidden');
    hideStatus();
}

function showStatus(message, type = 'info') {
    const status = document.getElementById('status');
    status.className = `p-3 rounded-lg ${
        type === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 
        type === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
        'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    }`;
    status.textContent = message;
    status.classList.remove('hidden');
}

function hideStatus() {
    document.getElementById('status').classList.add('hidden');
}

function parseFileStructure(text) {
    const lines = text.trim().split('\n');
    const structure = [];
    const stack = [{ children: structure, level: -1 }];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (!line.trim()) continue;
        
        // Calculate indentation level by analyzing the line structure
        let level = 0;
        let cleanLine = line;
        
        // Method 1: Count leading whitespace and tree characters
        const leadingMatch = line.match(/^(\s*[│\|]*\s*[├└\+\\\-─]*\s*)/);
        if (leadingMatch) {
            const leadingPart = leadingMatch[1];
            // Convert tree characters and spaces to level
            level = Math.floor(leadingPart.replace(/[├└\+\\\-─]/g, '').length / 2);
            cleanLine = line.substring(leadingPart.length);
        }
        
        // Method 2: If no tree chars, count indentation
        if (level === 0) {
            const spaceMatch = line.match(/^(\s*)/);
            if (spaceMatch) {
                const spaces = spaceMatch[1];
                level = Math.floor(spaces.length / 2); // Assume 2-space indentation
                if (spaces.length % 4 === 0 && spaces.length > 0) {
                    level = spaces.length / 4; // Try 4-space indentation
                }
                cleanLine = line.substring(spaces.length);
            }
        }
        
        // Clean up the filename/dirname
        cleanLine = cleanLine.replace(/^[├└\+\\\-─│\|\s]+/, '').trim();
        
        if (!cleanLine) continue;
        
        // Determine if it's a directory
        const isDirectory = cleanLine.endsWith('/') || 
                           cleanLine.endsWith('\\') ||
                           // Check if next line is indented more (indicating children)
                           (i + 1 < lines.length && getLineLevel(lines[i + 1]) > level);
        
        const name = cleanLine.replace(/[\/\\]*$/, ''); // Remove trailing slashes
        
        // Find correct parent level
        while (stack.length > 1 && stack[stack.length - 1].level >= level) {
            stack.pop();
        }
        
        const item = {
            name: name,
            type: isDirectory ? 'directory' : 'file',
            level: level,
            children: isDirectory ? [] : undefined,
            path: getFullPath(stack, name)
        };
        
        const parent = stack[stack.length - 1];
        parent.children.push(item);
        
        if (isDirectory) {
            stack.push({ children: item.children, level: level, name: name });
        }
    }
    
    return structure;
}

function getLineLevel(line) {
    if (!line || !line.trim()) return 0;
    
    const leadingMatch = line.match(/^(\s*[│\|]*\s*[├└\+\\\-─]*\s*)/);
    if (leadingMatch) {
        return Math.floor(leadingMatch[1].replace(/[├└\+\\\-─]/g, '').length / 2);
    }
    
    const spaceMatch = line.match(/^(\s*)/);
    if (spaceMatch) {
        const spaces = spaceMatch[1].length;
        return spaces % 4 === 0 && spaces > 0 ? spaces / 4 : Math.floor(spaces / 2);
    }
    
    return 0;
}

function getFullPath(stack, name) {
    const pathParts = [];
    for (let i = 1; i < stack.length; i++) {
        if (stack[i].name) {
            pathParts.push(stack[i].name);
        }
    }
    pathParts.push(name);
    return pathParts.join('/');
}

function createZipFromStructure(structure, zip, basePath = '') {
    for (let item of structure) {
        const fullPath = basePath + item.name;
        
        if (item.type === 'directory') {
            // Create directory
            const folder = zip.folder(fullPath);
            
            // Add .gitkeep if requested and directory is empty
            if (document.getElementById('addGitkeep').checked && 
                (!item.children || item.children.length === 0)) {
                folder.file('.gitkeep', '');
            }
            
            if (item.children && item.children.length > 0) {
                createZipFromStructure(item.children, zip, fullPath + '/');
            }
        } else {
            // Create empty file with basic content based on extension
            let content = '';
            const ext = item.name.split('.').pop().toLowerCase();
            
            // Add minimal content for common file types
            if (ext === 'php') {
                content = '<?php\n// ' + item.name + '\n';
            } else if (ext === 'js') {
                content = '// ' + item.name + '\n';
            } else if (ext === 'css') {
                content = '/* ' + item.name + ' */\n';
            } else if (ext === 'html') {
                content = '<!DOCTYPE html>\n<html>\n<head>\n    <title>' + item.name + '</title>\n</head>\n<body>\n\n</body>\n</html>';
            } else if (ext === 'md') {
                content = '# ' + item.name.replace('.md', '') + '\n\n';
            } else if (ext === 'json') {
                content = '{\n  \n}';
            }
            
            zip.file(fullPath, content);
        }
    }
}

function previewStructure() {
    const textArea = document.getElementById('fileStructure');
    const text = textArea.value.trim();
    
    if (!text) {
        showStatus('Please paste a file structure first.', 'error');
        return;
    }
    
    try {
        const structure = parseFileStructure(text);
        
        if (structure.length === 0) {
            showStatus('No valid file structure found. Please check your format.', 'error');
            return;
        }
        
        // Generate preview
        const preview = generatePreview(structure, 0);
        document.getElementById('structurePreview').innerHTML = preview;
        document.getElementById('itemCount').textContent = `${countItems(structure)} items total (${countDirectories(structure)} folders, ${countFiles(structure)} files)`;
        document.getElementById('previewSection').classList.remove('hidden');
        
        showStatus('Structure parsed successfully!', 'success');
        
    } catch (error) {
        showStatus('Error parsing structure: ' + error.message, 'error');
    }
}

function generatePreview(structure, level) {
    let html = '';
    for (let i = 0; i < structure.length; i++) {
        const item = structure[i];
        const indent = '&nbsp;'.repeat(level * 4);
        const isLast = i === structure.length - 1;
        const prefix = level === 0 ? '' : (isLast ? '└── ' : '├── ');
        const icon = item.type === 'directory' ? '📁' : '📄';
        
        html += `${indent}${prefix}${icon} ${item.name}${item.type === 'directory' ? '/' : ''}<br>`;
        
        if (item.children && item.children.length > 0) {
            html += generatePreview(item.children, level + 1);
        }
    }
    return html;
}

async function generateZip() {
    const textArea = document.getElementById('fileStructure');
    const generateBtn = document.getElementById('generateBtn');
    const includeReadme = document.getElementById('createReadme').checked;
    const zipName = document.getElementById('zipName').value.trim() || 'file-structure';
    
    const text = textArea.value.trim();
    
    if (!text) {
        showStatus('Please paste a file structure first.', 'error');
        return;
    }
    
    try {
        generateBtn.disabled = true;
        generateBtn.textContent = 'Generating...';
        showStatus('Parsing file structure...', 'info');
        
        // Parse the structure
        const structure = parseFileStructure(text);
        
        if (structure.length === 0) {
            throw new Error('No valid file structure found. Please check your format.');
        }
        
        showStatus('Creating ZIP file...', 'info');
        
        // Create ZIP
        const zip = new JSZip();
        
        // Add README if requested
        if (includeReadme) {
            const timestamp = new Date().toISOString().split('T')[0];
            const readmeContent = `# File Structure\n\nGenerated on: ${timestamp}\n\nThis project was created from the following structure:\n\n\`\`\`\n${text}\n\`\`\`\n\n## Statistics\n- Total items: ${countItems(structure)}\n- Directories: ${countDirectories(structure)}\n- Files: ${countFiles(structure)}\n\n---\n*Generated by File Structure Generator*`;
            zip.file('README.md', readmeContent);
        }
        
        // Add files and folders
        createZipFromStructure(structure, zip);
        
        showStatus('Downloading ZIP file...', 'info');
        
        // Generate and download
        const content = await zip.generateAsync({ 
            type: 'blob',
            compression: 'DEFLATE',
            compressionOptions: { level: 6 }
        });
        
        // Create download
        const url = window.URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = zipName + '.zip';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        const itemCount = countItems(structure);
        showStatus(`Successfully generated ZIP with ${itemCount} items!`, 'success');
        showNotification(`ZIP file "${zipName}.zip" downloaded successfully with ${itemCount} items!`, 'success');
        
    } catch (error) {
        showStatus('Error: ' + error.message, 'error');
        showNotification('Failed to generate ZIP: ' + error.message, 'error');
    } finally {
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate & Download ZIP';
    }
}

function countItems(structure) {
    let count = 0;
    for (let item of structure) {
        count++;
        if (item.children) {
            count += countItems(item.children);
        }
    }
    return count;
}

function countDirectories(structure) {
    let count = 0;
    for (let item of structure) {
        if (item.type === 'directory') {
            count++;
            if (item.children) {
                count += countDirectories(item.children);
            }
        }
    }
    return count;
}

function countFiles(structure) {
    let count = 0;
    for (let item of structure) {
        if (item.type === 'file') {
            count++;
        } else if (item.children) {
            count += countFiles(item.children);
        }
    }
    return count;
}

// Add example on load
window.addEventListener('DOMContentLoaded', function() {
    const example = `lib/
├── init.php
├── functions/
│   ├── admin-bar-menu.php
│   └── general.php
├── languages/
│   └── instant-ide-manager.pot
├── admin/
│   ├── settings.php
│   ├── build-menu.php
│   ├── boxes/
│   │   ├── settings.php
│   │   └── admin-access.php
│   └── update/
│       ├── edd-updater.php
│       ├── update.php
│       └── EDD_SL_Plugin_Updater.php
├── js/
│   ├── scripts.js
│   └── admin-options.js
└── css/
    ├── admin.css
    ├── icons.css
    └── fonts/
        ├── freelancer.svg
        ├── freelancer.ttf
        ├── freelancer.woff2
        ├── freelancer.woff
        └── freelancer.eot`;
    
    const textArea = document.getElementById('fileStructure');
    textArea.placeholder = `Paste your ASCII file structure here...\n\nTry the example below or paste your own:\n\n${example}`;
});
</script>