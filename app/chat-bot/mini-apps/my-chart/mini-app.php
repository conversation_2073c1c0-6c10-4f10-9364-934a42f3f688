<?php
// US National Debt Chart Applet for AI Dashboard
// Displays historical US national debt from 1900-2024
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>

<div class="space-y-4">
    <!-- Header -->
    <div class="text-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">US National Debt History</h2>
        <p class="text-sm text-gray-600 dark:text-gray-400">Federal debt from 1900 to 2024 (in trillions of dollars)</p>
    </div>

    <!-- Chart Container -->
    <div class="w-full h-96 bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <canvas id="debtChart" width="400" height="200"></canvas>
    </div>

    <!-- Key Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
            <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200">Current Debt (2024)</h3>
            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">$34.8T</p>
            <p class="text-sm text-blue-600 dark:text-blue-300">122% of GDP</p>
        </div>
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
            <h3 class="text-lg font-semibold text-green-800 dark:text-green-200">Debt in 1900</h3>
            <p class="text-2xl font-bold text-green-900 dark:text-green-100">$2.1B</p>
            <p class="text-sm text-green-600 dark:text-green-300">8% of GDP</p>
        </div>
        <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg text-center">
            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">Growth Factor</h3>
            <p class="text-2xl font-bold text-red-900 dark:text-red-100">16,570x</p>
            <p class="text-sm text-red-600 dark:text-red-300">Over 124 years</p>
        </div>
    </div>

    <!-- Analysis Section -->
    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mt-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Key Growth Periods</h3>
        <div class="text-sm text-gray-700 dark:text-gray-300 space-y-2">
            <p><strong>1910s-1920s:</strong> WWI drove debt from $2.9B to $25.5B</p>
            <p><strong>1940s:</strong> WWII caused dramatic increase from $43B to $269B</p>
            <p><strong>1980s-1990s:</strong> Debt tripled during this decade</p>
            <p><strong>2008-2009:</strong> Financial crisis pushed debt over $10 trillion</p>
            <p><strong>2020-2022:</strong> COVID-19 response added over $7 trillion</p>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button onclick="updateChart()" class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors">
        Refresh Chart
    </button>
    <button onclick="toggleScale()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium hover:bg-gray-600 transition-colors">
        Toggle Scale
    </button>
    <button onclick="sendDataToChat()" class="py-2 px-4 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 transition-colors">
        Send to Chat
    </button>
</div>

<script>
// Historical US National Debt Data (in billions, then converted to trillions for display)
const debtData = {
    1900: 2.1,    1905: 2.3,    1910: 2.9,    1915: 3.1,    1920: 25.5,
    1925: 20.5,   1930: 16.2,   1935: 28.7,   1940: 43.0,   1945: 260.1,
    1950: 256.1,  1955: 272.8,  1960: 284.1,  1965: 313.8,  1970: 370.9,
    1975: 533.2,  1980: 907.7,  1985: 1823.1, 1990: 3233.3, 1995: 4974.0,
    2000: 5674.2, 2005: 7933.0, 2010: 13561.6, 2015: 18150.6, 2020: 27748.0,
    2021: 28428.9, 2022: 30928.9, 2023: 33167.7, 2024: 34800.0
};

let chart;
let isLogScale = false;

// Convert billions to trillions for display
const processedData = Object.fromEntries(
    Object.entries(debtData).map(([year, debt]) => [year, debt / 1000])
);

function initChart() {
    const ctx = document.getElementById('debtChart').getContext('2d');
    
    // Prepare data for Chart.js
    const years = Object.keys(processedData);
    const debts = Object.values(processedData);
    
    chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: years,
            datasets: [{
                label: 'National Debt (Trillions $)',
                data: debts,
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1,
                hoverBackgroundColor: 'rgba(59, 130, 246, 0.8)',
                hoverBorderColor: 'rgba(59, 130, 246, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#000'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.parsed.y;
                            return `Debt: $${value.toFixed(2)} trillion`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    type: isLogScale ? 'logarithmic' : 'linear',
                    ticks: {
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#000',
                        callback: function(value) {
                            return '$' + value.toFixed(1) + 'T';
                        }
                    },
                    title: {
                        display: true,
                        text: 'Debt (Trillions USD)',
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#000'
                    },
                    grid: {
                        color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    }
                },
                x: {
                    ticks: {
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#000',
                        maxTicksLimit: 15
                    },
                    title: {
                        display: true,
                        text: 'Year',
                        color: document.documentElement.classList.contains('dark') ? '#fff' : '#000'
                    },
                    grid: {
                        color: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    }
                }
            }
        }
    });
}

function updateChart() {
    if (chart) {
        chart.destroy();
    }
    initChart();
    showNotification('Chart updated successfully!', 'success');
}

function toggleScale() {
    isLogScale = !isLogScale;
    updateChart();
    showNotification(`Switched to ${isLogScale ? 'logarithmic' : 'linear'} scale`, 'success');
}

function collectFormData() {
    const currentDebt = processedData[2024];
    const startDebt = processedData[1900];
    const growthFactor = currentDebt / startDebt;
    
    return {
        title: "US National Debt Analysis (1900-2024)",
        startingDebt: `$${startDebt.toFixed(1)} trillion (1900)`,
        currentDebt: `$${currentDebt.toFixed(1)} trillion (2024)`,
        growthFactor: `${Math.round(growthFactor).toLocaleString()}x increase`,
        keyEvents: [
            "WWI (1917-1918): Debt increased from $2.9B to $25.5B",
            "WWII (1941-1945): Debt jumped from $43B to $260B", 
            "1980s: Debt tripled during this decade",
            "2008 Financial Crisis: Pushed debt over $10 trillion",
            "COVID-19 (2020-2022): Added over $7 trillion to debt"
        ],
        data: processedData,
        analysis: "The US national debt has grown exponentially, particularly during major wars and economic crises. The most dramatic growth occurred during WWII and again during the COVID-19 pandemic."
    };
}

function sendDataToChat() {
    const data = collectFormData();
    const customMessage = `Here's an analysis of US National Debt from 1900-2024: The debt has grown from $${data.startingDebt} to ${data.currentDebt}, representing a ${data.growthFactor}. Key growth periods include major wars, economic crises, and the recent pandemic response.`;
    
    sendToChat(data, customMessage);
    showNotification('Data sent to chat successfully!', 'success');
}

// Initialize chart when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initChart, 100); // Small delay to ensure canvas is ready
});

// Handle theme changes
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.attributeName === 'class') {
            updateChart();
        }
    });
});

observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
});
</script>