<?php
/**
 * Mini App: My Chart
 * Integration and Bootstrap File
 *
 * This file handles all AI Dashboard integrations and loads the custom mini-app logic.
 * DO NOT EDIT - This file is managed by AI Dashboard
 */

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if ($data && isset($data['action'])) {
        // Include the custom mini-app logic
        include_once __DIR__ . '/mini-app.php';

        // Call the custom handler if it exists
        if (function_exists('handleMiniAppRequest')) {
            echo json_encode(handleMiniAppRequest($data));
        } else {
            // Default handlers
            switch ($data['action']) {
                case 'submit_form':
                    echo json_encode(['success' => true, 'message' => 'Form submitted successfully']);
                    break;

                case 'send_to_chat':
                    echo json_encode(['success' => true, 'message' => 'Sent to chat successfully']);
                    break;

                default:
                    echo json_encode(['success' => false, 'error' => 'Unknown action']);
            }
        }
        exit;
    }
}

// Get theme and padding parameters
$theme = $_GET['theme'] ?? 'dark';
$addPadding = isset($_GET['padding']) && $_GET['padding'] === 'true';

// Set body classes based on parameters
$bodyClasses = [];
if ($theme === 'light') {
    $bodyClasses[] = 'bg-gray-100';
} else {
    $bodyClasses[] = 'bg-gray-100 dark:bg-gray-900';
}
if ($addPadding) {
    $bodyClasses[] = 'pt-4';
}
$bodyClassString = implode(' ', $bodyClasses);

// App metadata
$appConfig = [
    'name' => 'My Chart',
    'icon' => '🗃',
    'description' => 'I would like this app to display a bar graph that represents the United States national debt from 1900 to 2024 to help understand how much and when it started really growing.',
    'id' => 'my-chart',
    'theme' => $theme
];
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo $theme; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Chart - Mini App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>
<body class="<?php echo $bodyClassString; ?>">
    <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-6">
            <div class="text-2xl mr-3">🗃</div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">My Chart</h2>
        </div>

        <!-- Custom Mini-App Content -->
        <div id="miniAppContent">
            <?php
            // Include the custom mini-app content
            include_once __DIR__ . '/mini-app.php';
            ?>
        </div>
    </div>

    <!-- AI Dashboard Integration Scripts -->
    <script>
        // App configuration
        const appConfig = <?php echo json_encode($appConfig); ?>;

        // Close app function
        function closeApp() {
            if (window.parent && window.parent.closeMiniApp) {
                window.parent.closeMiniApp();
            } else {
                window.close();
            }
        }

        // Notification system
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Send to chat function
        async function sendToChat(data, customMessage = null) {
            // Check if we can send directly to the chat bot window
            if (window.chatBotWindow && !window.chatBotWindow.closed) {
                try {
                    // Format the message
                    const message = customMessage || `🗃 **My Chart**\n\n` + Object.entries(data).map(([key, value]) => `**${key}:** ${value}`).join('\n');

                    // Call the function in the chat bot window
                    window.chatBotWindow.addMessageFromMiniApp(message);

                    // Focus the chat bot window
                    window.chatBotWindow.focus();

                    // Show success message
                    showNotification('Sent to chat successfully!', 'success');

                    return true;
                } catch (e) {
                    console.error('Error communicating with chat bot window:', e);
                }
            }

            // Fallback: show data in alert
            const message = Object.entries(data).map(([key, value]) => `${key}: ${value}`).join('\n');
            alert('Data to send to chat:\n\n' + message);
            return false;
        }
    </script>

    <!-- Include custom mini-app scripts -->
    <script>
        <?php
        // Include custom JavaScript if the mini-app provides it
        $customJSFile = __DIR__ . '/mini-app.js';
        if (file_exists($customJSFile)) {
            echo file_get_contents($customJSFile);
        }
        ?>
    </script>
</body>
</html>