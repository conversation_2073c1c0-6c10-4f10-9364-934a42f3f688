<?php
/**
 * Save Applet Code
 * Handles saving edited applet.php files
 */

// Set JSON response header first
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);

try {
    // Include necessary files with error handling
    $configPath = __DIR__ . '/../../../config.php';
    $authPath = __DIR__ . '/../../../auth.php';
    
    if (!file_exists($configPath)) {
        throw new Exception('Config file not found at: ' . $configPath);
    }
    
    if (!file_exists($authPath)) {
        throw new Exception('Auth file not found at: ' . $authPath);
    }
    
    require_once $configPath;
    require_once $authPath;

    // Check if functions exist before calling
    if (function_exists('requireAuth')) {
        requireAuth();
    }

    // Handle POST requests only
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        exit;
    }

    // Get JSON input
    $input = file_get_contents('php://input');
    if ($input === false) {
        throw new Exception('Failed to read input');
    }
    
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('JSON decode error: ' . json_last_error_msg());
    }
    
    if (!$data) {
        echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
        exit;
    }

    // Validate required fields
    if (empty($data['appId']) || !isset($data['code'])) {
        echo json_encode(['success' => false, 'error' => 'Missing required fields: appId and code']);
        exit;
    }

    $appId = $data['appId'];
    $code = $data['code'];

    // Validate app ID (security check)
    if (!preg_match('/^[a-z0-9\-_]+$/i', $appId)) {
        echo json_encode(['success' => false, 'error' => 'Invalid app ID format']);
        exit;
    }

    // Check if app folder exists
    $appFolderPath = __DIR__ . '/' . $appId;
    if (!is_dir($appFolderPath)) {
        echo json_encode(['success' => false, 'error' => 'App folder not found']);
        exit;
    }

    // Check if it's a folder-based app
    $configFile = $appFolderPath . '/config.json';
    if (!file_exists($configFile)) {
        echo json_encode(['success' => false, 'error' => 'App is not editable (no config file found)']);
        exit;
    }

    $config = json_decode(file_get_contents($configFile), true);
    if (!$config || !isset($config['editable']) || !$config['editable']) {
        echo json_encode(['success' => false, 'error' => 'App is not editable']);
        exit;
    }

    // Create backup of current file
    $miniAppFile = $appFolderPath . '/mini-app.php';
    $backupFile = $appFolderPath . '/mini-app.php.backup.' . date('Y-m-d_H-i-s');
    
    if (file_exists($miniAppFile)) {
        if (!copy($miniAppFile, $backupFile)) {
            error_log("Warning: Failed to create backup for $appId");
        }
    }

    // Basic PHP syntax validation (alternative approach for shared hosting)
    // Check for basic PHP syntax issues without using exec()
    $syntaxErrors = [];

    // Check for unclosed PHP tags
    $trimmedCode = trim($code);
    $endsWithCloseTag = (substr($trimmedCode, -2) === '?>');
    if (substr_count($code, '<?php') !== substr_count($code, '?>') && !$endsWithCloseTag) {
        // This is actually fine - PHP files don't need closing tags
    }

    // Check for basic syntax issues
    if (strpos($code, '<?php') === false && strpos($code, '<?') === false) {
        // Allow files without PHP tags - they might be pure HTML/CSS/JS
        // Just add a warning in the log but don't prevent saving
        error_log('Warning: Applet code saved without PHP opening tag for app: ' . $appId);
    }

    // Try to catch obvious syntax errors
    $lines = explode("\n", $code);
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '//') === 0 || strpos($line, '#') === 0) continue;

        // Check for unmatched brackets (basic check)
        $openBrackets = substr_count($line, '{');
        $closeBrackets = substr_count($line, '}');
        $openParens = substr_count($line, '(');
        $closeParens = substr_count($line, ')');

        // Note: This is a very basic check and won't catch all syntax errors
        // but it's better than nothing when exec() is not available
    }

    if (!empty($syntaxErrors)) {
        echo json_encode([
            'success' => false,
            'error' => 'Potential syntax issues: ' . implode(', ', $syntaxErrors)
        ]);
        exit;
    }

    // Save the new code
    if (file_put_contents($miniAppFile, $code) === false) {
        throw new Exception('Failed to save code to file');
    }

    // Update config with last modified time
    $config['last_modified'] = date('Y-m-d H:i:s');
    file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

    echo json_encode([
        'success' => true,
        'message' => 'Code saved successfully',
        'backup_created' => file_exists($backupFile)
    ]);

} catch (Exception $e) {
    error_log('Code save error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} catch (Error $e) {
    error_log('Fatal error in save-code.php: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Fatal error: ' . $e->getMessage()]);
}
?>
