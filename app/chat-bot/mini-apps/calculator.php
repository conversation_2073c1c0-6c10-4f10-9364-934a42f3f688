<?php
/**
 * <PERSON><PERSON> - Calculator Mini App
 * 
 * A comprehensive calculator with basic and scientific functions
 * that integrates with the <PERSON><PERSON> assistant.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['chat_bot_app'];

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'calculate':
            echo json_encode(handleCalculation($input));
            break;
        case 'send_to_chat':
            echo json_encode(sendToChat($input, $app));
            break;
        // Removed register_integration to avoid API issues
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
    exit;
}

/**
 * Handle calculation request
 */
function handleCalculation($input) {
    try {
        $expression = $input['expression'] ?? '';
        $operation = $input['operation'] ?? 'basic';
        
        if (empty($expression)) {
            throw new Exception('Expression is required');
        }
        
        // Sanitize expression
        $expression = preg_replace('/[^0-9+\-*\/\(\)\.\s]/', '', $expression);
        
        if ($operation === 'scientific') {
            $result = evaluateScientificExpression($expression, $input);
        } else {
            $result = evaluateBasicExpression($expression);
        }
        
        return [
            'success' => true,
            'result' => $result,
            'expression' => $expression,
            'formatted_result' => number_format($result, 8)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Evaluate basic mathematical expression
 */
function evaluateBasicExpression($expression) {
    // Remove whitespace
    $expression = str_replace(' ', '', $expression);
    
    // Basic validation
    if (!preg_match('/^[0-9+\-*\/\(\)\.]+$/', $expression)) {
        throw new Exception('Invalid characters in expression');
    }
    
    // Check for balanced parentheses
    if (substr_count($expression, '(') !== substr_count($expression, ')')) {
        throw new Exception('Unbalanced parentheses');
    }
    
    // Evaluate using PHP's eval (with safety checks)
    $result = @eval("return $expression;");
    
    if ($result === false || $result === null) {
        throw new Exception('Invalid expression');
    }
    
    if (!is_numeric($result)) {
        throw new Exception('Result is not a number');
    }
    
    return $result;
}

/**
 * Evaluate scientific expression
 */
function evaluateScientificExpression($expression, $input) {
    $function = $input['function'] ?? '';
    $value = floatval($input['value'] ?? 0);
    
    switch ($function) {
        case 'sin':
            return sin(deg2rad($value));
        case 'cos':
            return cos(deg2rad($value));
        case 'tan':
            return tan(deg2rad($value));
        case 'log':
            return log10($value);
        case 'ln':
            return log($value);
        case 'sqrt':
            return sqrt($value);
        case 'pow':
            $exponent = floatval($input['exponent'] ?? 2);
            return pow($value, $exponent);
        case 'factorial':
            return factorial(intval($value));
        default:
            return evaluateBasicExpression($expression);
    }
}

/**
 * Calculate factorial
 */
function factorial($n) {
    if ($n < 0) {
        throw new Exception('Factorial of negative number is undefined');
    }
    if ($n > 170) {
        throw new Exception('Factorial too large');
    }
    
    $result = 1;
    for ($i = 2; $i <= $n; $i++) {
        $result *= $i;
    }
    return $result;
}

/**
 * Send calculation result to chat
 */
function sendToChat($input, $app) {
    try {
        $result = $input['result'] ?? '';
        $expression = $input['expression'] ?? '';

        if (empty($result) || empty($expression)) {
            throw new Exception('Result and expression are required');
        }

        // Simple approach: Save directly to data storage
        $dataStorage = $app->getDataStorage();
        $userId = $_SESSION['user_id'] ?? 'anonymous';

        // Create a conversation ID
        $conversationId = 'conv_calc_' . uniqid();

        // Format the message
        $formattedMessage = "📊 **Calculator Result**\n\n" .
                           "Expression: `{$expression}`\n" .
                           "Result: **{$result}**";

        // Save the message
        $aiResponse = 'I see you calculated ' . $expression . ' = ' . $result . '. That\'s a great calculation! Need help with anything else?';

        $success = $dataStorage->saveChatMessage(
            $userId,
            $conversationId,
            $formattedMessage,
            $aiResponse,
            'calculator',
            0,
            'mini_app_integration',
            ['mini_app_id' => 'calculator', 'expression' => $expression, 'result' => $result]
        );

        // Debug: Log the save attempt
        error_log("Calculator sendToChat: userId=$userId, conversationId=$conversationId, success=" . ($success ? 'true' : 'false'));

        if ($success) {
            return [
                'success' => true,
                'message' => 'Calculation sent to chat successfully',
                'conversation_id' => $conversationId,
                'redirect_url' => "?route=/chat&conversation_id=" . $conversationId
            ];
        } else {
            throw new Exception('Failed to save message to chat');
        }

    } catch (Exception $e) {
        error_log("Calculator sendToChat error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Register calculator integration with chat bot
 */
function registerIntegration($app) {
    try {
        $integrationData = [
            'action' => 'register_mini_app',
            'mini_app_id' => 'calculator',
            'capabilities' => [
                'calculate' => [
                    'name' => 'Calculate Expression',
                    'description' => 'Evaluate mathematical expressions',
                    'parameters' => ['expression']
                ],
                'scientific_function' => [
                    'name' => 'Scientific Function',
                    'description' => 'Perform scientific calculations',
                    'parameters' => ['function', 'value', 'exponent']
                ]
            ]
        ];

        $response = makeIntegrationRequest($integrationData);

        return $response ?: [
            'success' => false,
            'error' => 'Failed to register integration'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Make request to integration API
 */
function makeIntegrationRequest($data) {
    $url = '../api/mini-app-integration.php';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        return json_decode($response, true);
    }

    return null;
}

// Removed complex integration function - using direct approach now

// If not an API request, return the mini-app HTML

// Check for theme and padding parameters
$theme = $_GET['theme'] ?? 'dark';
$addPadding = isset($_GET['padding']) && $_GET['padding'] === 'true';

// Set body classes based on parameters
$bodyClasses = [];
if ($theme === 'light') {
    $bodyClasses[] = 'bg-gray-100';
} else {
    $bodyClasses[] = 'bg-gray-100 dark:bg-gray-900';
}
if ($addPadding) {
    $bodyClasses[] = 'pt-4';
}
$bodyClassString = implode(' ', $bodyClasses);
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo $theme; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator - Chat Bot Mini App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>
<body class="<?php echo $bodyClassString; ?>">
    <div class="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Calculator</h2>
            <button onclick="closeCalculator()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Display -->
        <div class="mb-4">
            <input type="text" id="display" readonly 
                   class="w-full p-4 text-right text-2xl bg-gray-50 dark:bg-gray-700 border rounded-lg text-gray-900 dark:text-white"
                   value="0">
        </div>
        
        <!-- Mode Toggle -->
        <div class="mb-4 flex space-x-2">
            <button onclick="setMode('basic')" id="basicMode" 
                    class="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium">
                Basic
            </button>
            <button onclick="setMode('scientific')" id="scientificMode" 
                    class="flex-1 py-2 px-4 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium">
                Scientific
            </button>
        </div>
        
        <!-- Basic Calculator -->
        <div id="basicCalculator" class="grid grid-cols-4 gap-2 mb-4">
            <button onclick="clearDisplay()" class="col-span-2 p-3 bg-red-500 text-white rounded-lg font-medium">Clear</button>
            <button onclick="deleteLast()" class="p-3 bg-yellow-500 text-white rounded-lg font-medium">⌫</button>
            <button onclick="appendToDisplay('/')" class="p-3 bg-gray-500 text-white rounded-lg font-medium">÷</button>
            
            <button onclick="appendToDisplay('7')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">7</button>
            <button onclick="appendToDisplay('8')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">8</button>
            <button onclick="appendToDisplay('9')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">9</button>
            <button onclick="appendToDisplay('*')" class="p-3 bg-gray-500 text-white rounded-lg font-medium">×</button>
            
            <button onclick="appendToDisplay('4')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">4</button>
            <button onclick="appendToDisplay('5')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">5</button>
            <button onclick="appendToDisplay('6')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">6</button>
            <button onclick="appendToDisplay('-')" class="p-3 bg-gray-500 text-white rounded-lg font-medium">-</button>
            
            <button onclick="appendToDisplay('1')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">1</button>
            <button onclick="appendToDisplay('2')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">2</button>
            <button onclick="appendToDisplay('3')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">3</button>
            <button onclick="appendToDisplay('+')" class="p-3 bg-gray-500 text-white rounded-lg font-medium">+</button>
            
            <button onclick="appendToDisplay('0')" class="col-span-2 p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">0</button>
            <button onclick="appendToDisplay('.')" class="p-3 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-medium">.</button>
            <button onclick="calculate()" class="p-3 bg-green-500 text-white rounded-lg font-medium">=</button>
        </div>
        
        <!-- Scientific Calculator -->
        <div id="scientificCalculator" class="hidden space-y-2 mb-4">
            <div class="grid grid-cols-3 gap-2">
                <button onclick="scientificFunction('sin')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">sin</button>
                <button onclick="scientificFunction('cos')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">cos</button>
                <button onclick="scientificFunction('tan')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">tan</button>
                
                <button onclick="scientificFunction('log')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">log</button>
                <button onclick="scientificFunction('ln')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">ln</button>
                <button onclick="scientificFunction('sqrt')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">√</button>
                
                <button onclick="scientificFunction('pow')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">x²</button>
                <button onclick="scientificFunction('factorial')" class="p-2 bg-blue-500 text-white rounded-lg text-sm">x!</button>
                <button onclick="appendToDisplay('(')" class="p-2 bg-gray-500 text-white rounded-lg text-sm">(</button>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex space-x-2">
            <button onclick="sendToChat()" class="flex-1 py-2 px-4 bg-green-500 text-white rounded-lg font-medium">
                Send to Chat
            </button>
            <button onclick="copyResult()" class="py-2 px-4 bg-gray-500 text-white rounded-lg font-medium">
                Copy
            </button>
        </div>
    </div>

    <script>
        let currentMode = 'basic';
        let lastResult = null;
        
        function setMode(mode) {
            currentMode = mode;
            
            if (mode === 'basic') {
                document.getElementById('basicCalculator').classList.remove('hidden');
                document.getElementById('scientificCalculator').classList.add('hidden');
                document.getElementById('basicMode').className = 'flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium';
                document.getElementById('scientificMode').className = 'flex-1 py-2 px-4 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium';
            } else {
                document.getElementById('basicCalculator').classList.add('hidden');
                document.getElementById('scientificCalculator').classList.remove('hidden');
                document.getElementById('basicMode').className = 'flex-1 py-2 px-4 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium';
                document.getElementById('scientificMode').className = 'flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg font-medium';
            }
        }
        
        function appendToDisplay(value) {
            const display = document.getElementById('display');
            if (display.value === '0' || display.value === 'Error') {
                display.value = value;
            } else {
                display.value += value;
            }
        }
        
        function clearDisplay() {
            document.getElementById('display').value = '0';
        }
        
        function deleteLast() {
            const display = document.getElementById('display');
            if (display.value.length > 1) {
                display.value = display.value.slice(0, -1);
            } else {
                display.value = '0';
            }
        }
        
        async function calculate() {
            const display = document.getElementById('display');
            const expression = display.value;
            
            try {
                const response = await fetch('calculator.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'calculate',
                        expression: expression,
                        operation: currentMode
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    display.value = result.result;
                    lastResult = result;
                } else {
                    display.value = 'Error';
                    console.error('Calculation error:', result.error);
                }
            } catch (error) {
                display.value = 'Error';
                console.error('Request error:', error);
            }
        }
        
        async function scientificFunction(func) {
            const display = document.getElementById('display');
            const value = parseFloat(display.value);
            
            try {
                const response = await fetch('calculator.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'calculate',
                        expression: display.value,
                        operation: 'scientific',
                        function: func,
                        value: value
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    display.value = result.result;
                    lastResult = result;
                } else {
                    display.value = 'Error';
                    console.error('Calculation error:', result.error);
                }
            } catch (error) {
                display.value = 'Error';
                console.error('Request error:', error);
            }
        }
        
        async function sendToChat() {
            if (!lastResult) {
                alert('No calculation result to send');
                return;
            }

            // Check if we can send directly to the chat bot window
            if (window.chatBotWindow && !window.chatBotWindow.closed) {
                try {
                    // Format the calculation message
                    const calcMessage = `🧮 **Calculation Result**\n\n${lastResult.expression} = ${lastResult.result}`;

                    // Call the function in the chat bot window
                    window.chatBotWindow.addMessageFromMiniApp(calcMessage);

                    // Focus the chat bot window
                    window.chatBotWindow.focus();

                    // Show success message
                    showNotification('Calculation sent to chat successfully!', 'success');

                    return; // Exit early, no need for API call
                } catch (e) {
                    console.error('Error communicating with chat bot window:', e);
                    // Fall through to API call
                }
            }

            // Fallback: Use API call if direct communication failed
            try {
                console.log('Sending calculation to chat:', lastResult);

                const response = await fetch('calculator.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'send_to_chat',
                        result: lastResult.result,
                        expression: lastResult.expression
                    })
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('Invalid JSON response: ' + responseText.substring(0, 100));
                }

                console.log('Response data:', result);

                if (result.success) {
                    // Show success message
                    showNotification('Calculation sent to chat successfully!', 'success');

                    // Open in new tab as fallback
                    if (result.redirect_url) {
                        window.open(result.redirect_url, '_blank');
                    }
                } else {
                    showNotification('Error sending to chat: ' + result.error, 'error');
                    console.error('Send to chat error:', result);
                }
            } catch (error) {
                alert('Error sending to chat: ' + error.message);
                console.error('Request error:', error);
            }
        }

        // Helper function for notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        function copyResult() {
            const display = document.getElementById('display');
            navigator.clipboard.writeText(display.value).then(() => {
                alert('Result copied to clipboard!');
            }).catch(() => {
                alert('Failed to copy result');
            });
        }
        
        function closeCalculator() {
            if (window.parent && window.parent.closeMiniApp) {
                window.parent.closeMiniApp();
            } else {
                window.close();
            }
        }
        
        // Removed integration registration to avoid API issues

        // Keyboard support
        document.addEventListener('keydown', function(event) {
            const key = event.key;

            if ('0123456789+-*/.'.includes(key)) {
                appendToDisplay(key === '*' ? '*' : key === '/' ? '/' : key);
            } else if (key === 'Enter' || key === '=') {
                calculate();
            } else if (key === 'Escape') {
                clearDisplay();
            } else if (key === 'Backspace') {
                deleteLast();
            }
        });
    </script>
</body>
</html>
