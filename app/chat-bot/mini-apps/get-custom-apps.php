<?php
/**
 * Get Custom Mini Apps
 * Returns list of custom mini-apps from registry
 */

// Set JSON response header first
header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output, but log them

try {
    // Include necessary files with error handling
    $configPath = __DIR__ . '/../../../config.php';
    $authPath = __DIR__ . '/../../../auth.php';

    if (!file_exists($configPath)) {
        throw new Exception('Config file not found at: ' . $configPath);
    }

    if (!file_exists($authPath)) {
        throw new Exception('Auth file not found at: ' . $authPath);
    }

    require_once $configPath;
    require_once $authPath;

    // Check if functions exist before calling
    if (function_exists('requireAuth')) {
        requireAuth();
    }

    $registryFile = __DIR__ . '/custom-apps-registry.json';

    if (!file_exists($registryFile)) {
        echo json_encode(['success' => true, 'apps' => []]);
        exit;
    }

    $registryContent = file_get_contents($registryFile);
    if ($registryContent === false) {
        throw new Exception('Failed to read registry file');
    }

    $registry = json_decode($registryContent, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('JSON decode error: ' . json_last_error_msg());
    }

    if (!$registry) {
        echo json_encode(['success' => true, 'apps' => []]);
        exit;
    }

    // Filter out apps whose files/folders don't exist and update structure info
    $validApps = [];
    foreach ($registry as $appId => $appData) {
        $isValid = false;

        // Check for folder-based structure (new)
        if (isset($appData['folder']) || isset($appData['structure']) && $appData['structure'] === 'folder-based') {
            $appFolder = __DIR__ . '/' . ($appData['folder'] ?? $appId);
            if (is_dir($appFolder) && file_exists($appFolder . '/init.php')) {
                $isValid = true;
                $appData['structure'] = 'folder-based';
                $appData['editable'] = true;

                // Update with latest config if available
                $configFile = $appFolder . '/config.json';
                if (file_exists($configFile)) {
                    $config = json_decode(file_get_contents($configFile), true);
                    if ($config) {
                        $appData = array_merge($appData, $config);
                    }
                }
            }
        }
        // Check for file-based structure (legacy)
        elseif (isset($appData['file'])) {
            $appFile = __DIR__ . '/' . $appData['file'];
            if (file_exists($appFile)) {
                $isValid = true;
                $appData['structure'] = 'file-based';
                $appData['editable'] = false;
            }
        }

        if ($isValid) {
            $validApps[$appId] = $appData;
        }
    }

    echo json_encode(['success' => true, 'apps' => $validApps]);

} catch (Exception $e) {
    error_log('Error getting custom apps: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
} catch (Error $e) {
    error_log('Fatal error getting custom apps: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Fatal error: ' . $e->getMessage()]);
}
?>
