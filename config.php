<?php
/**
 * Configuration file for AI Dashboard Application
 * Contains global settings, constants, and session management
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Application Configuration
define('APP_NAME', 'AI Dashboard');
define('APP_VERSION', '1.0.0');
define('DATA_DIR', __DIR__ . '/data');
define('LOGS_DIR', __DIR__ . '/logs');
define('CONFIG_DIR', __DIR__ . '/config');
define('INCLUDES_DIR', __DIR__ . '/includes');
define('USERS_FILE', DATA_DIR . '/users.json');
define('APP_SETTINGS_FILE', CONFIG_DIR . '/app.json');
define('ERROR_LOG_FILE', LOGS_DIR . '/errors.json');
define('ACTIVITY_LOG_FILE', LOGS_DIR . '/activity.json');

// Security Configuration
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds

// Create directories if they don't exist
$directories = [DATA_DIR, LOGS_DIR, CONFIG_DIR, INCLUDES_DIR];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Initialize files if they don't exist
if (!file_exists(USERS_FILE)) {
    file_put_contents(USERS_FILE, json_encode([], JSON_PRETTY_PRINT));
}

if (!file_exists(ERROR_LOG_FILE)) {
    file_put_contents(ERROR_LOG_FILE, json_encode([], JSON_PRETTY_PRINT));
}

if (!file_exists(ACTIVITY_LOG_FILE)) {
    file_put_contents(ACTIVITY_LOG_FILE, json_encode([], JSON_PRETTY_PRINT));
}

// Initialize app settings with defaults
if (!file_exists(APP_SETTINGS_FILE)) {
    $defaultSettings = [
        'app_name' => 'AI Dashboard',
        'timezone' => 'UTC',
        'maintenance_mode' => false,
        'registration_enabled' => true,
        'max_login_attempts' => 5,
        'session_timeout' => 3600,
        'password_min_length' => 8,
        'require_email_verification' => false,
        'default_user_role' => 'user',
        'admin_email' => '',
        'site_description' => 'Modern AI Dashboard Application',
        'theme' => 'default',
        'enable_logging' => true,
        'log_retention_days' => 30,
        'show_footer_links' => false,
        'footer_links' => [
            ['text' => 'Privacy Policy', 'url' => '#'],
            ['text' => 'Terms of Service', 'url' => '#'],
            ['text' => 'Contact Us', 'url' => '#']
        ]
    ];
    file_put_contents(APP_SETTINGS_FILE, json_encode($defaultSettings, JSON_PRETTY_PRINT));
}

// Include error handler and logger
require_once INCLUDES_DIR . '/error_handler.php';
require_once INCLUDES_DIR . '/settings.php';

// Check for maintenance mode (allow admins and maintenance page itself)
$currentScript = basename($_SERVER['SCRIPT_NAME']);
$allowedScripts = ['maintenance.php', 'login.php', 'logout.php'];

if (isMaintenanceMode() && !in_array($currentScript, $allowedScripts)) {
    // Allow admins to bypass maintenance mode
    if (!isLoggedIn() || !isAdmin()) {
        header('Location: maintenance.php');
        exit();
    }
}

/**
 * Check if user is logged in
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['username']);
}

/**
 * Require authentication - redirect to login if not authenticated
 */
function requireAuth() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

/**
 * Redirect authenticated users away from auth pages
 */
function redirectIfAuthenticated() {
    if (isLoggedIn()) {
        header('Location: dashboard.php');
        exit();
    }
}

/**
 * Get current user data
 * @return array|null
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'],
        'role' => $_SESSION['user_role'] ?? 'user'
    ];
}

/**
 * Check if current user has admin role
 * @return bool
 */
function isAdmin() {
    return isLoggedIn() && ($_SESSION['user_role'] ?? 'user') === 'admin';
}

/**
 * Require admin role - redirect if not admin
 */
function requireAdmin() {
    if (!isAdmin()) {
        setFlashMessage('Access denied. Admin privileges required.', 'error');
        header('Location: dashboard.php');
        exit();
    }
}

/**
 * Check if user has specific role
 * @param string $role
 * @return bool
 */
function hasRole($role) {
    return isLoggedIn() && ($_SESSION['user_role'] ?? 'user') === $role;
}

/**
 * Set flash message for next page load
 * @param string $message
 * @param string $type (success, error, warning, info)
 */
function setFlashMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

/**
 * Get and clear flash message
 * @return array|null
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'message' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type']
        ];
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        return $message;
    }
    return null;
}

/**
 * Sanitize input data
 * @param string $data
 * @return string
 */
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * @param string $token
 * @return bool
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
?>
