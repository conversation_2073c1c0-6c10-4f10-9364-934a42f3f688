<?php
// Include theme, toast, and icon systems
require_once __DIR__ . '/../includes/theme.php';
require_once __DIR__ . '/../includes/toast.php';
require_once __DIR__ . '/../includes/icons.php';
require_once __DIR__ . '/../includes/app_manager.php';

// Convert any flash messages to toasts
if (isset($_SESSION['flash_message'])) {
    flashToToast($_SESSION['flash_message'], $_SESSION['flash_type'] ?? 'info');
    unset($_SESSION['flash_message'], $_SESSION['flash_type']);
}
?>
<!DOCTYPE html>
<html lang="en" class="<?php echo getThemeClasses(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo strpos($_SERVER['REQUEST_URI'], '/admin/') !== false ? '../assets/css/style.css' : 'assets/css/style.css'; ?>">
    
    <!-- Favicon -->
    <?php echo getFaviconHtml(); ?>
    
    <!-- Meta tags -->
    <meta name="description" content="AI Dashboard - Modern web application for AI management">
    <meta name="author" content="AI Dashboard Team">
    
    <style>
        /* Custom Tailwind configuration */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out transform hover:scale-105;
        }
        
        .btn-secondary {
            @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 ease-in-out;
        }
        
        .input-field {
            @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200;
        }
        
        .alert {
            @apply p-4 rounded-lg mb-4;
        }
        
        .alert-success {
            @apply bg-green-100 border border-green-400 text-green-700;
        }
        
        .alert-error {
            @apply bg-red-100 border border-red-400 text-red-700;
        }
        
        .alert-warning {
            @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
        }
        
        .alert-info {
            @apply bg-blue-100 border border-blue-400 text-blue-700;
        }
    </style>

    <!-- Tailwind Dark Mode Configuration -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
                    }
                }
            }
        }

        // Mobile menu toggle function
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            } else {
                mobileMenu.classList.add('hidden');
                document.body.style.overflow = ''; // Restore scrolling
            }
        }

        // Admin dropdown toggle function
        function toggleAdminDropdown() {
            const dropdown = document.getElementById('adminDropdownMenu');
            const icon = document.getElementById('adminDropdownIcon');

            if (dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                dropdown.style.position = 'absolute';
                dropdown.style.right = '0';
                dropdown.style.top = '100%';
                dropdown.style.zIndex = '50';
                icon.style.transform = 'rotate(180deg)';
            } else {
                dropdown.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Apps dropdown toggle function
        function toggleAppsDropdown() {
            const dropdown = document.getElementById('appsDropdownMenu');

            if (dropdown && dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
                dropdown.style.position = 'absolute';
                dropdown.style.right = '0';
                dropdown.style.top = '100%';
                dropdown.style.zIndex = '50';
            } else if (dropdown) {
                dropdown.classList.add('hidden');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const adminDropdown = document.getElementById('adminDropdown');
            const adminDropdownMenu = document.getElementById('adminDropdownMenu');
            const appsDropdown = document.getElementById('appsDropdown');
            const appsDropdownMenu = document.getElementById('appsDropdownMenu');

            if (adminDropdown && adminDropdownMenu && !adminDropdown.contains(event.target)) {
                adminDropdownMenu.classList.add('hidden');
                document.getElementById('adminDropdownIcon').style.transform = 'rotate(0deg)';
            }

            if (appsDropdown && appsDropdownMenu && !appsDropdown.contains(event.target)) {
                appsDropdownMenu.classList.add('hidden');
            }
        });
    </script>
</head>
<body class="<?php echo getBodyClasses(); ?>">
    
    <!-- Navigation -->
    <nav class="<?php echo getThemeComponentClasses('nav'); ?> shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="<?php
                        $requestUri = $_SERVER['REQUEST_URI'];
                        if (isLoggedIn()) {
                            if (strpos($requestUri, '/admin/') !== false) {
                                echo '../dashboard.php';
                            } elseif (strpos($requestUri, '/app/') !== false) {
                                echo '../../dashboard.php';
                            } else {
                                echo 'dashboard.php';
                            }
                        } else {
                            if (strpos($requestUri, '/admin/') !== false) {
                                echo '../index.php';
                            } elseif (strpos($requestUri, '/app/') !== false) {
                                echo '../../index.php';
                            } else {
                                echo 'index.php';
                            }
                        }
                    ?>" class="flex items-center space-x-2">
                        <?php echo getIcon('app-logo', 'text-2xl'); ?>
                        <span class="text-xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>"><?php echo APP_NAME; ?></span>
                    </a>
                </div>

                <div class="flex items-center space-x-4">
                    <?php if (isLoggedIn()): ?>
                        <!-- Navigation Menu -->
                        <div class="hidden md:flex items-center space-x-1">
                            <!-- Chat Bot with Dynamic Name -->
                            <?php
                            $botName = getUserBotName();
                            $chatBotUrl = '';
                            if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
                                $chatBotUrl = '../app/chat-bot/';
                            } elseif (strpos($_SERVER['REQUEST_URI'], '/app/') !== false) {
                                $chatBotUrl = '../chat-bot/';
                            } else {
                                $chatBotUrl = 'app/chat-bot/';
                            }
                            ?>
                            <a href="<?php echo $chatBotUrl; ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> px-3 py-2 rounded-md text-sm font-medium">
                                <?php echo htmlspecialchars($botName); ?>
                            </a>

                            <!-- Applets -->
                            <a href="<?php echo $chatBotUrl; ?>?route=/applets" class="<?php echo getThemeComponentClasses('nav-link'); ?> px-3 py-2 rounded-md text-sm font-medium">
                                Applets
                            </a>

                            <!-- Custom Apps (excluding Chat Bot since it's now explicitly shown) -->
                            <?php echo generateAppMenuHTML(false); ?>

                            <a href="<?php
                                $requestUri = $_SERVER['REQUEST_URI'];
                                if (strpos($requestUri, '/admin/') !== false) {
                                    echo '../ai_settings.php';
                                } elseif (strpos($requestUri, '/app/') !== false) {
                                    echo '../../ai_settings.php';
                                } else {
                                    echo 'ai_settings.php';
                                }
                            ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> px-3 py-2 rounded-md text-sm font-medium">AI Settings</a>

                            <?php if (isAdmin()): ?>
                                <div class="relative">
                                    <button id="adminDropdown" class="<?php echo getThemeComponentClasses('nav-link'); ?> px-3 py-2 rounded-md text-sm font-medium flex items-center" onclick="toggleAdminDropdown()">
                                        Admin
                                        <svg class="ml-1 h-4 w-4 transform transition-transform" id="adminDropdownIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="adminDropdownMenu" class="w-48 bg-white dark:bg-gray-800 rounded-md shadow-xl py-1 hidden border border-gray-200 dark:border-gray-700">
                                        <a href="<?php
                                            $requestUri = $_SERVER['REQUEST_URI'];
                                            if (strpos($requestUri, '/admin/') !== false) {
                                                echo 'users.php';
                                            } elseif (strpos($requestUri, '/app/') !== false) {
                                                echo '../../admin/users.php';
                                            } else {
                                                echo 'admin/users.php';
                                            }
                                        ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">User Management</a>
                                        <a href="<?php
                                            $requestUri = $_SERVER['REQUEST_URI'];
                                            if (strpos($requestUri, '/admin/') !== false) {
                                                echo 'settings.php';
                                            } elseif (strpos($requestUri, '/app/') !== false) {
                                                echo '../../admin/settings.php';
                                            } else {
                                                echo 'admin/settings.php';
                                            }
                                        ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">Settings</a>
                                        <a href="<?php
                                            $requestUri = $_SERVER['REQUEST_URI'];
                                            if (strpos($requestUri, '/admin/') !== false) {
                                                echo 'logs.php';
                                            } elseif (strpos($requestUri, '/app/') !== false) {
                                                echo '../../admin/logs.php';
                                            } else {
                                                echo 'admin/logs.php';
                                            }
                                        ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">System Logs</a>
                                        <a href="<?php
                                            $requestUri = $_SERVER['REQUEST_URI'];
                                            if (strpos($requestUri, '/admin/') !== false) {
                                                echo 'backup.php';
                                            } elseif (strpos($requestUri, '/app/') !== false) {
                                                echo '../../admin/backup.php';
                                            } else {
                                                echo 'admin/backup.php';
                                            }
                                        ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">Data Backup</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Welcome Text -->
                        <span class="<?php echo getThemeComponentClasses('text-secondary'); ?>">Welcome,
                            <a href="<?php
                                $requestUri = $_SERVER['REQUEST_URI'];
                                if (strpos($requestUri, '/admin/') !== false) {
                                    echo '../profile.php';
                                } elseif (strpos($requestUri, '/app/') !== false) {
                                    echo '../../profile.php';
                                } else {
                                    echo 'profile.php';
                                }
                            ?>" class="font-semibold text-blue-600 dark:text-blue-400 hover:opacity-75 transition-opacity duration-200">
                                <?php echo htmlspecialchars($_SESSION['username']); ?>
                            </a>
                            <?php if (isAdmin()): ?>
                                <span class="ml-1 text-xs <?php echo getThemeComponentClasses('badge-admin'); ?> px-2 py-1 rounded">Admin</span>
                            <?php endif; ?>
                        </span>

                        <!-- Mobile Menu Button -->
                        <div class="md:hidden">
                            <button id="mobileMenuBtn" class="<?php echo getThemeComponentClasses('nav-link'); ?> p-2 rounded-md" onclick="toggleMobileMenu()">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>

                        <a href="<?php
                            $requestUri = $_SERVER['REQUEST_URI'];
                            if (strpos($requestUri, '/admin/') !== false) {
                                echo '../logout.php';
                            } elseif (strpos($requestUri, '/app/') !== false) {
                                echo '../../logout.php';
                            } else {
                                echo 'logout.php';
                            }
                        ?>" class="bg-red-600 hover:bg-red-700 hover:shadow-md focus:bg-red-700 focus:shadow-md active:bg-red-800 text-white px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 no-underline transform hover:scale-105">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="<?php echo getThemeComponentClasses('nav-link'); ?> px-3 py-2 rounded-md text-sm font-medium">Login</a>
                        <?php if (isRegistrationEnabled()): ?>
                            <a href="register.php" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-3 py-2 rounded-md text-sm font-medium transition duration-200">Register</a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobileMenu" class="hidden md:hidden fixed inset-0 z-50">
        <div class="fixed inset-0 bg-black bg-opacity-50" onclick="toggleMobileMenu()"></div>
        <div class="fixed top-0 right-0 h-full w-64 <?php echo getThemeComponentClasses('nav'); ?> shadow-lg transform transition-transform duration-300 ease-in-out">
            <div class="flex items-center justify-between p-4 border-b <?php echo getThemeComponentClasses('divider'); ?>">
                <span class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Menu</span>
                <button onclick="toggleMobileMenu()" class="<?php echo getThemeComponentClasses('nav-link'); ?> p-2 rounded-md">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="p-4 space-y-2">
                <!-- Chat Bot with Dynamic Name -->
                <?php
                $botName = getUserBotName();
                $chatBotUrl = '';
                if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
                    $chatBotUrl = '../app/chat-bot/';
                } elseif (strpos($_SERVER['REQUEST_URI'], '/app/') !== false) {
                    $chatBotUrl = '../chat-bot/';
                } else {
                    $chatBotUrl = 'app/chat-bot/';
                }
                ?>
                <a href="<?php echo $chatBotUrl; ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                    <?php echo htmlspecialchars($botName); ?>
                </a>

                <!-- Applets -->
                <a href="<?php echo $chatBotUrl; ?>?route=/applets" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                    Applets
                </a>

                <!-- Main Navigation -->
                <a href="<?php
                    $requestUri = $_SERVER['REQUEST_URI'];
                    if (strpos($requestUri, '/admin/') !== false) {
                        echo '../ai_settings.php';
                    } elseif (strpos($requestUri, '/app/') !== false) {
                        echo '../../ai_settings.php';
                    } else {
                        echo 'ai_settings.php';
                    }
                ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                    AI Settings
                </a>

                <!-- Custom Apps (excluding Chat Bot since it's now explicitly shown) -->
                <?php echo generateAppMenuHTML(true); ?>

                <?php if (isAdmin()): ?>
                    <!-- Admin Section -->
                    <div class="pt-4 border-t <?php echo getThemeComponentClasses('divider'); ?>">
                        <h3 class="px-3 text-xs font-semibold <?php echo getThemeComponentClasses('text-muted'); ?> uppercase tracking-wider">Admin</h3>
                        <div class="mt-2 space-y-1">
                            <a href="<?php
                                $requestUri = $_SERVER['REQUEST_URI'];
                                if (strpos($requestUri, '/admin/') !== false) {
                                    echo 'users.php';
                                } elseif (strpos($requestUri, '/app/') !== false) {
                                    echo '../../admin/users.php';
                                } else {
                                    echo 'admin/users.php';
                                }
                            ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                                User Management
                            </a>
                            <a href="<?php
                                $requestUri = $_SERVER['REQUEST_URI'];
                                if (strpos($requestUri, '/admin/') !== false) {
                                    echo 'settings.php';
                                } elseif (strpos($requestUri, '/app/') !== false) {
                                    echo '../../admin/settings.php';
                                } else {
                                    echo 'admin/settings.php';
                                }
                            ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                                Settings
                            </a>
                            <a href="<?php
                                $requestUri = $_SERVER['REQUEST_URI'];
                                if (strpos($requestUri, '/admin/') !== false) {
                                    echo 'logs.php';
                                } elseif (strpos($requestUri, '/app/') !== false) {
                                    echo '../../admin/logs.php';
                                } else {
                                    echo 'admin/logs.php';
                                }
                            ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                                System Logs
                            </a>
                            <a href="<?php
                                $requestUri = $_SERVER['REQUEST_URI'];
                                if (strpos($requestUri, '/admin/') !== false) {
                                    echo 'backup.php';
                                } elseif (strpos($requestUri, '/app/') !== false) {
                                    echo '../../admin/backup.php';
                                } else {
                                    echo 'admin/backup.php';
                                }
                            ?>" class="<?php echo getThemeComponentClasses('nav-link'); ?> block px-3 py-2 rounded-md text-base font-medium">
                                Data Backup
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Logout -->
                <div class="pt-4 border-t <?php echo getThemeComponentClasses('divider'); ?>">
                    <a href="<?php
                        $requestUri = $_SERVER['REQUEST_URI'];
                        if (strpos($requestUri, '/admin/') !== false) {
                            echo '../logout.php';
                        } elseif (strpos($requestUri, '/app/') !== false) {
                            echo '../../logout.php';
                        } else {
                            echo 'logout.php';
                        }
                    ?>" class="bg-red-600 hover:bg-red-700 text-white block px-3 py-2 rounded-md text-base font-medium">
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php 
    $flash = getFlashMessage();
    if ($flash): 
    ?>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            <div class="alert alert-<?php echo $flash['type']; ?>">
                <?php echo htmlspecialchars($flash['message']); ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Main Content Container -->
    <main class="flex-grow w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
