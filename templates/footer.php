    </main>
    
    <!-- Footer -->
    <footer class="<?php echo getThemeComponentClasses('nav'); ?> mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center space-x-2 mb-4 md:mb-0">
                    <?php echo getIcon('app-logo', 'text-2xl'); ?>
                    <span class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>"><?php echo APP_NAME; ?></span>
                    <span class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">v<?php echo APP_VERSION; ?></span>
                </div>
                
                <?php if (isLoggedIn()): ?>
                    <div class="text-xs <?php echo getThemeComponentClasses('text-muted'); ?>">
                        Session active for <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </div>
                <?php endif; ?>
            </div>

        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="<?php echo strpos($_SERVER['REQUEST_URI'], '/admin/') !== false ? '../assets/js/app.js' : 'assets/js/app.js'; ?>"></script>
    
    <!-- Additional JavaScript for form validation and UX -->
    <script>
        // Flash message auto-hide
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                // Auto-hide success messages after 5 seconds
                if (alert.classList.contains('alert-success')) {
                    setTimeout(function() {
                        alert.style.opacity = '0';
                        alert.style.transition = 'opacity 0.5s ease-out';
                        setTimeout(function() {
                            alert.remove();
                        }, 500);
                    }, 5000);
                }
            });
        });
        
        // Form validation helpers
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return true;
            
            const inputs = form.querySelectorAll('input[required]');
            let isValid = true;
            
            inputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('border-red-500');
                    isValid = false;
                } else {
                    input.classList.remove('border-red-500');
                }
            });
            
            return isValid;
        }
        
        // Password strength indicator
        function checkPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            return strength;
        }
        
        // Real-time password validation
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');

            if (passwordInput && confirmPasswordInput) {
                function validatePasswords() {
                    const password = passwordInput.value;
                    const confirmPassword = confirmPasswordInput.value;

                    if (password && confirmPassword && password !== confirmPassword) {
                        confirmPasswordInput.classList.add('border-red-500');
                        confirmPasswordInput.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPasswordInput.classList.remove('border-red-500');
                        confirmPasswordInput.setCustomValidity('');
                    }
                }

                passwordInput.addEventListener('input', validatePasswords);
                confirmPasswordInput.addEventListener('input', validatePasswords);
            }
        });

        // Admin dropdown functionality
        function toggleAdminDropdown() {
            const menu = document.getElementById('adminDropdownMenu');
            const icon = document.getElementById('adminDropdownIcon');
            const button = document.getElementById('adminDropdown');

            if (menu.classList.contains('hidden')) {
                // Move dropdown to body level and position it
                document.body.appendChild(menu);

                // Get button position
                const buttonRect = button.getBoundingClientRect();

                // Position dropdown
                menu.style.position = 'fixed';
                menu.style.top = (buttonRect.bottom + 8) + 'px';
                menu.style.right = (window.innerWidth - buttonRect.right) + 'px';
                menu.style.zIndex = '2147483647';

                menu.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                menu.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('adminDropdown');
            const menu = document.getElementById('adminDropdownMenu');
            const icon = document.getElementById('adminDropdownIcon');

            if (dropdown && menu && !dropdown.contains(event.target) && !menu.contains(event.target)) {
                menu.classList.add('hidden');
                if (icon) icon.style.transform = 'rotate(0deg)';
            }
        });

        // Apply theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const userTheme = '<?php echo getUserTheme(); ?>';

            if (userTheme === 'dark') {
                document.documentElement.classList.add('dark');
            } else if (userTheme === 'light') {
                document.documentElement.classList.remove('dark');
            } else if (userTheme === 'auto') {
                // Check system preference
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }

                // Listen for system theme changes
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                    if (userTheme === 'auto') {
                        if (e.matches) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    }
                });
            }
        });
    </script>

    <!-- Toast Container -->
    <?php echo renderToastContainer(); ?>

    <!-- Toast JavaScript -->
    <?php echo getToastJavaScript(); ?>
</body>
</html>
