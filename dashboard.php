<?php
/**
 * User Dashboard Page
 * Protected area for authenticated users
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/roles.php';

// Require authentication
requireAuth();

// Check if a custom homepage app is configured
// But only redirect if not explicitly accessing dashboard (no 'view=dashboard' parameter)
$viewDashboard = isset($_GET['view']) && $_GET['view'] === 'dashboard';

if (!$viewDashboard) {
    $settings = loadSettings();
    $homepageApp = $settings['dashboard_homepage_app'] ?? '';

    if (!empty($homepageApp)) {
        // Check if the app exists
        $appPath = "app/{$homepageApp}/";
        if (is_dir($appPath)) {
            // Redirect to the homepage app
            header("Location: {$appPath}");
            exit;
        }
    }
}

// Get current user data
$currentUser = getCurrentUser();
$userData = getUserById($currentUser['id']);

// Get user activity logs
$userLogs = getUserActivityLogs($currentUser['id'], 10);

// Set page title
$pageTitle = 'Dashboard';

// Include header
include 'templates/header.php';
?>

<!-- Dashboard Content -->
<div class="space-y-8">
    
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">
                    Welcome back, <?php echo htmlspecialchars($currentUser['username']); ?>! <?php echo getIcon('welcome-wave', 'text-base'); ?>
                    <?php if (isAdmin()): ?>
                        <span class="ml-2 text-lg bg-red-500 text-white px-3 py-1 rounded-full">Admin</span>
                    <?php endif; ?>
                </h1>
                <p class="text-blue-100 text-lg">
                    You're successfully logged into your <?php echo APP_NAME; ?> dashboard.
                    <?php if (isAdmin()): ?>
                        <br><span class="text-yellow-200"><?php echo getIcon('admin-key', 'text-base'); ?> You have administrative privileges.</span>
                    <?php endif; ?>
                </p>
            </div>
            <div class="opacity-20"><?php echo getIcon('app-logo', 'text-6xl'); ?></div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('user-profile', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Account Status</h3>
                    <p class="text-green-600 dark:text-green-400 font-medium">Active</p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('calendar-date', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Member Since</h3>
                    <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                        <?php echo date('M j, Y', strtotime($userData['created_at'])); ?>
                    </p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('clock-time', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Last Login</h3>
                    <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                        <?php
                        if ($userData['last_login']) {
                            echo date('M j, Y g:i A', strtotime($userData['last_login']));
                        } else {
                            echo 'First login';
                        }
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Dashboard Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Account Information -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Account Information</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">Username</label>
                        <p class="mt-1 <?php echo getThemeComponentClasses('text-primary'); ?>"><?php echo htmlspecialchars($userData['username']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">User ID</label>
                        <p class="mt-1 <?php echo getThemeComponentClasses('text-muted'); ?> font-mono text-sm"><?php echo htmlspecialchars($userData['id']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">Role</label>
                        <p class="mt-1">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                <?php
                                $userRole = $userData['role'] ?? 'user';
                                echo $userRole === 'admin' ? getThemeComponentClasses('badge-admin') :
                                     ($userRole === 'moderator' ? getThemeComponentClasses('badge-moderator') : getThemeComponentClasses('badge-user'));
                                ?>">
                                <?php echo htmlspecialchars(getRoleDisplayName($userRole)); ?>
                            </span>
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">Account Created</label>
                        <p class="mt-1 <?php echo getThemeComponentClasses('text-primary'); ?>">
                            <?php echo date('F j, Y \a\t g:i A', strtotime($userData['created_at'])); ?>
                        </p>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t <?php echo getThemeComponentClasses('divider'); ?>">
                    <a href="profile.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                        Edit Profile
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="p-6 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Quick Actions</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php if (isAdmin()): ?>
                        <a href="admin/users.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('user-group', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">User Management</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Manage user accounts and roles</p>
                            </div>
                        </a>

                        <a href="admin/settings.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('settings-gear', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">Application Settings</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Configure application behavior</p>
                            </div>
                        </a>

                        <a href="admin/logs.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('logs-clipboard', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">System Logs</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Monitor system activity and errors</p>
                            </div>
                        </a>

                        <a href="admin/backup.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('backup-disk', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">Data Backup</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Create and manage data backups</p>
                            </div>
                        </a>
                    <?php else: ?>
                        <a href="profile.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('settings-gear', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">Profile Settings</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Manage your account preferences</p>
                            </div>
                        </a>

                        <a href="ai_settings.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('ai-robot', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">AI Settings</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Configure AI models and API keys</p>
                            </div>
                        </a>

                        <a href="activity.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('activity-chart', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">My Activity</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">View your account activity</p>
                            </div>
                        </a>

                        <a href="help.php" class="flex items-center p-4 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                            <div class="mr-4"><?php echo getIcon('chat-support', 'text-2xl'); ?></div>
                            <div>
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">Help & Support</h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Get help and documentation</p>
                            </div>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="p-6 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Recent Activity</h2>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php if (!empty($userLogs)): ?>
                    <?php foreach ($userLogs as $log): ?>
                        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg">
                            <div class="text-2xl mr-4">
                                <?php
                                $action = $log['action'] ?? '';
                                if (strpos($action, 'login') !== false) echo getIcon('security-lock', 'text-2xl');
                                elseif (strpos($action, 'logout') !== false) echo getIcon('door-logout', 'text-2xl');
                                elseif (strpos($action, 'settings') !== false) echo getIcon('settings-gear', 'text-2xl');
                                elseif (strpos($action, 'user') !== false) echo getIcon('user-profile', 'text-2xl');
                                else echo getIcon('document-note', 'text-2xl');
                                ?>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">
                                    <?php
                                    $actionParts = explode('.', $action);
                                    echo htmlspecialchars(ucwords(str_replace('_', ' ', end($actionParts))));
                                    ?>
                                </h3>
                                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                                    <?php echo htmlspecialchars($log['description'] ?? 'Activity performed'); ?>
                                </p>
                            </div>
                            <div class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">
                                <?php echo date('M j, g:i A', strtotime($log['timestamp'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="flex items-center p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
                        <div class="mr-4"><?php echo getIcon('success-check', 'text-2xl'); ?></div>
                        <div class="flex-1">
                            <h3 class="font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">Account Created</h3>
                            <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                                Your account was successfully created on <?php echo date('M j, Y', strtotime($userData['created_at'])); ?>
                            </p>
                        </div>
                        <div class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">
                            <?php echo date('g:i A', strtotime($userData['created_at'])); ?>
                        </div>
                    </div>

                    <div class="text-center py-8 <?php echo getThemeComponentClasses('text-muted'); ?>">
                        <div class="mb-2"><?php echo getIcon('document-note', 'text-4xl'); ?></div>
                        <p>Your activity will appear here as you use the application.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="bg-gray-50 dark:bg-gray-800 border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg p-6">
        <h2 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-4">System Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">Application Version:</span>
                <span class="<?php echo getThemeComponentClasses('text-muted'); ?> ml-2"><?php echo APP_VERSION; ?></span>
            </div>
            <div>
                <span class="font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">PHP Version:</span>
                <span class="<?php echo getThemeComponentClasses('text-muted'); ?> ml-2"><?php echo PHP_VERSION; ?></span>
            </div>
            <div>
                <span class="font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">Session ID:</span>
                <span class="<?php echo getThemeComponentClasses('text-muted'); ?> ml-2 font-mono"><?php echo substr(session_id(), 0, 8); ?>...</span>
            </div>
            <div>
                <span class="font-medium <?php echo getThemeComponentClasses('text-secondary'); ?>">Server Time:</span>
                <span class="<?php echo getThemeComponentClasses('text-muted'); ?> ml-2"><?php echo date('Y-m-d H:i:s T'); ?></span>
            </div>
        </div>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
