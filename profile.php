<?php
/**
 * User Profile Management Page
 * Allows users to edit their profile information
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/roles.php';
require_once 'includes/theme.php';

// Require authentication
requireAuth();

// Get current user data
$currentUser = getCurrentUser();
$userData = getUserById($currentUser['id']);

// Handle form submission
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $newUsername = sanitizeInput($_POST['username'] ?? '');
                
                // Validate username
                if (empty($newUsername)) {
                    $message = 'Username is required.';
                    $messageType = 'error';
                } elseif (strlen($newUsername) < 3) {
                    $message = 'Username must be at least 3 characters long.';
                    $messageType = 'error';
                } elseif ($newUsername !== $userData['username'] && usernameExists($newUsername)) {
                    $message = 'Username already exists.';
                    $messageType = 'error';
                } else {
                    // Update user data
                    $users = loadUsers();
                    foreach ($users as $index => $user) {
                        if ($user['id'] === $currentUser['id']) {
                            $users[$index]['username'] = $newUsername;
                            $users[$index]['updated_at'] = date('Y-m-d H:i:s');
                            
                            if (saveUsers($users)) {
                                // Update session
                                $_SESSION['username'] = $newUsername;
                                
                                // Log activity
                                logActivity('profile.updated', 'User updated profile information', [
                                    'old_username' => $userData['username'],
                                    'new_username' => $newUsername
                                ]);
                                
                                $message = 'Profile updated successfully.';
                                $messageType = 'success';
                                
                                // Refresh user data
                                $userData = getUserById($currentUser['id']);
                            } else {
                                $message = 'Failed to update profile.';
                                $messageType = 'error';
                            }
                            break;
                        }
                    }
                }
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';

                // Validate passwords
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    $message = 'All password fields are required.';
                    $messageType = 'error';
                } elseif (!password_verify($currentPassword, $userData['password'])) {
                    $message = 'Current password is incorrect.';
                    $messageType = 'error';
                } elseif ($newPassword !== $confirmPassword) {
                    $message = 'New passwords do not match.';
                    $messageType = 'error';
                } elseif (strlen($newPassword) < getSetting('password_min_length', 8)) {
                    $minLength = getSetting('password_min_length', 8);
                    $message = "New password must be at least {$minLength} characters long.";
                    $messageType = 'error';
                } else {
                    // Update password
                    $users = loadUsers();
                    foreach ($users as $index => $user) {
                        if ($user['id'] === $currentUser['id']) {
                            $users[$index]['password'] = password_hash($newPassword, PASSWORD_DEFAULT);
                            $users[$index]['password_updated_at'] = date('Y-m-d H:i:s');

                            if (saveUsers($users)) {
                                // Log activity
                                logActivity('profile.password_changed', 'User changed password');

                                $message = 'Password changed successfully.';
                                $messageType = 'success';
                            } else {
                                $message = 'Failed to change password.';
                                $messageType = 'error';
                            }
                            break;
                        }
                    }
                }
                break;

            case 'update_theme':
                $newTheme = $_POST['theme_preference'] ?? 'auto';

                if (setUserTheme($newTheme)) {
                    $message = 'Theme preference updated successfully.';
                    $messageType = 'success';

                    // Refresh user data
                    $userData = getUserById($currentUser['id']);
                } else {
                    $message = 'Failed to update theme preference.';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Set page title
$pageTitle = 'Profile Settings';

// Include header
include 'templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-1">Manage your account information and preferences</p>
    </div>
    
    <!-- Display Messages -->
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>
    
    <div class="grid lg:grid-cols-2 gap-8">
        <!-- Profile Information -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow flex flex-col">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Profile Information</h2>
            </div>

            <form method="POST" class="p-6 flex-grow flex flex-col">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="update_profile">
                
                <div class="space-y-6">
                    <div>
                        <label for="username" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Username
                        </label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo htmlspecialchars($userData['username']); ?>"
                            required
                            minlength="3"
                            maxlength="50"
                        >
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            User ID
                        </label>
                        <p class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?> font-mono <?php echo getThemeComponentClasses('card'); ?> p-2 rounded border <?php echo getThemeComponentClasses('divider'); ?>">
                            <?php echo htmlspecialchars($userData['id']); ?>
                        </p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Role
                        </label>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            <?php
                            $userRole = $userData['role'] ?? 'user';
                            echo $userRole === 'admin' ? getThemeComponentClasses('badge-admin') :
                                 ($userRole === 'moderator' ? getThemeComponentClasses('badge-moderator') : getThemeComponentClasses('badge-user'));
                            ?>">
                            <?php echo htmlspecialchars(getRoleDisplayName($userRole)); ?>
                        </span>
                    </div>

                    <div>
                        <label class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Member Since
                        </label>
                        <p class="text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                            <?php echo date('F j, Y', strtotime($userData['created_at'])); ?>
                        </p>
                    </div>
                </div>

                <div class="mt-6 pt-6 mt-auto">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                        Update Profile
                    </button>
                </div>
            </form>
        </div>

        <!-- Change Password -->
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow flex flex-col">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Change Password</h2>
            </div>

            <form method="POST" class="p-6 flex-grow flex flex-col" id="passwordForm">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="change_password">

                <div class="space-y-6">
                    <div>
                        <label for="current_password" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Current Password
                        </label>
                        <input
                            type="password"
                            id="current_password"
                            name="current_password"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            required
                        >
                    </div>

                    <div>
                        <label for="new_password" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            New Password
                        </label>
                        <input
                            type="password"
                            id="new_password"
                            name="new_password"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            required
                            minlength="<?php echo getSetting('password_min_length', 8); ?>"
                        >
                        <p class="text-xs <?php echo getThemeComponentClasses('text-muted'); ?> mt-1">
                            Must be at least <?php echo getSetting('password_min_length', 8); ?> characters long
                        </p>
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Confirm New Password
                        </label>
                        <input
                            type="password"
                            id="confirm_password"
                            name="confirm_password"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            required
                        >
                        <p id="passwordMatch" class="text-xs mt-1 hidden"></p>
                    </div>
                </div>

                <div class="mt-6 pt-6 mt-auto">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                        Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Theme Preferences -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Theme Preferences</h2>
        </div>

        <form method="POST" class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="update_theme">

            <div class="space-y-4">
                <div>
                    <label for="theme_preference" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                        Choose Theme
                    </label>
                    <select id="theme_preference" name="theme_preference" class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg">
                        <?php
                        $currentTheme = getUserTheme();
                        $themeOptions = [
                            'light' => '☀️ Light Mode',
                            'dark' => '🌙 Dark Mode',
                            'auto' => '🔄 System Default'
                        ];
                        ?>
                        <?php foreach ($themeOptions as $value => $label): ?>
                            <option value="<?php echo $value; ?>" <?php echo $currentTheme === $value ? 'selected' : ''; ?>>
                                <?php echo $label; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs <?php echo getThemeComponentClasses('text-muted'); ?> mt-1">
                        System Default will follow your device's theme setting
                    </p>
                </div>

                <div class="pt-4">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                        Update Theme
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Account Statistics -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Account Statistics</h2>
        </div>

        <div class="p-6">
            <div class="grid md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        <?php echo date('j', strtotime($userData['created_at'])); ?>
                    </div>
                    <div class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Days as Member</div>
                </div>

                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        <?php echo $userData['last_login'] ? date('M j', strtotime($userData['last_login'])) : 'Never'; ?>
                    </div>
                    <div class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Last Login</div>
                </div>

                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        <?php echo count(getUserActivityLogs($currentUser['id'], 100)); ?>
                    </div>
                    <div class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">Total Activities</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const matchIndicator = document.getElementById('passwordMatch');
    
    function validatePasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (newPassword && confirmPassword) {
            if (newPassword === confirmPassword) {
                confirmPasswordInput.classList.remove('border-red-500');
                confirmPasswordInput.classList.add('border-green-500');
                matchIndicator.textContent = 'Passwords match ✓';
                matchIndicator.className = 'text-xs mt-1 text-green-600';
                matchIndicator.classList.remove('hidden');
            } else {
                confirmPasswordInput.classList.remove('border-green-500');
                confirmPasswordInput.classList.add('border-red-500');
                matchIndicator.textContent = 'Passwords do not match';
                matchIndicator.className = 'text-xs mt-1 text-red-600';
                matchIndicator.classList.remove('hidden');
            }
        } else {
            matchIndicator.classList.add('hidden');
            confirmPasswordInput.classList.remove('border-red-500', 'border-green-500');
        }
    }
    
    newPasswordInput.addEventListener('input', validatePasswordMatch);
    confirmPasswordInput.addEventListener('input', validatePasswordMatch);
});
</script>

<?php include 'templates/footer.php'; ?>
