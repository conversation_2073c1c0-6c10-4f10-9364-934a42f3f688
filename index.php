<?php
/**
 * Landing page - redirects users based on authentication status
 */

require_once 'config.php';
require_once 'includes/theme.php';

// Check for custom homepage
$settings = loadSettings();
if (($settings['homepage_type'] ?? 'default') === 'custom' && !empty($settings['custom_homepage_html'])) {
    // Display custom homepage HTML directly
    echo $settings['custom_homepage_html'];
    exit();
}

// Redirect authenticated users to dashboard
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

// Set page title
$pageTitle = 'Welcome';

// Include header
include 'templates/header.php';
?>

<!-- Hero Section -->
<div class="text-center">
    <div class="max-w-6xl mx-auto">
        <!-- Hero Content -->
        <div class="mb-12">
            <div class="mb-6"><?php echo getIcon('app-logo', 'text-6xl'); ?></div>
            <h1 class="text-4xl md:text-6xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?> mb-6">
                Welcome to <span class="text-blue-600 dark:text-blue-400"><?php echo APP_NAME; ?></span>
            </h1>
            <p class="text-xl <?php echo getThemeComponentClasses('text-secondary'); ?> mb-8 max-w-2xl mx-auto">
                A modern AI-powered dashboard with custom app integration, theme support, and professional design.
                Experience seamless AI interactions, flexible customization, and enterprise-grade features.
            </p>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <a href="register.php" class="<?php echo getThemeComponentClasses('button-primary'); ?> text-lg px-8 py-3 inline-block rounded-lg font-medium transition duration-200">
                Get Started
            </a>
            <a href="login.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> text-lg px-8 py-3 inline-block rounded-lg font-medium transition duration-200">
                Sign In
            </a>
        </div>
        
        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
                <div class="mb-4"><?php echo getIcon('ai-robot', 'text-3xl'); ?></div>
                <h3 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">AI Integration</h3>
                <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                    Built-in AI chat interface with conversation history and configurable AI models.
                </p>
            </div>

            <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
                <div class="mb-4"><?php echo getIcon('app-grid', 'text-3xl'); ?></div>
                <h3 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">Custom Apps</h3>
                <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                    Integrate custom applications with full dashboard management and backup support.
                </p>
            </div>

            <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
                <div class="mb-4"><?php echo getIcon('theme-toggle', 'text-3xl'); ?></div>
                <h3 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">Theme Support</h3>
                <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                    Light and dark themes with customizable icons and professional design system.
                </p>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="mt-16 <?php echo getThemeComponentClasses('card'); ?> p-8 rounded-lg shadow">
            <h2 class="text-2xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?> mb-6">Platform Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                <div>
                    <h4 class="font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2"><?php echo getIcon('ai-robot', 'text-base'); ?> AI Integration</h4>
                    <ul class="<?php echo getThemeComponentClasses('text-secondary'); ?> space-y-1">
                        <li>• Multiple AI model support</li>
                        <li>• Conversation history management</li>
                        <li>• User-specific AI configurations</li>
                        <li>• Secure API key management</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2"><?php echo getIcon('app-grid', 'text-base'); ?> App Management</h4>
                    <ul class="<?php echo getThemeComponentClasses('text-secondary'); ?> space-y-1">
                        <li>• Custom app integration</li>
                        <li>• Automated backup system</li>
                        <li>• Update management</li>
                        <li>• Menu integration</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2"><?php echo getIcon('theme-toggle', 'text-base'); ?> Customization</h4>
                    <ul class="<?php echo getThemeComponentClasses('text-secondary'); ?> space-y-1">
                        <li>• Light and dark themes</li>
                        <li>• Custom icon system</li>
                        <li>• Configurable homepage</li>
                        <li>• Role-based access control</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2"><?php echo getIcon('security-lock', 'text-base'); ?> Enterprise Ready</h4>
                    <ul class="<?php echo getThemeComponentClasses('text-secondary'); ?> space-y-1">
                        <li>• Centralized logging system</li>
                        <li>• CSRF protection</li>
                        <li>• Flat-file architecture</li>
                        <li>• No database dependencies</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Call to Action -->
        <div class="mt-12 text-center">
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mb-4">Ready to get started?</p>
            <a href="register.php" class="<?php echo getThemeComponentClasses('button-primary'); ?> text-lg px-8 py-3 inline-block rounded-lg font-medium transition duration-200">
                Create Your Account
            </a>
        </div>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
