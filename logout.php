<?php
/**
 * User Logout Page
 * Handles user session cleanup and logout
 */

require_once 'config.php';
require_once 'auth.php';

// Check if user is logged in
if (isLoggedIn()) {
    // Get username for goodbye message
    $username = $_SESSION['username'];
    
    // Perform logout
    logoutUser();
    
    // Set flash message for next page
    setFlashMessage('You have been successfully logged out. See you soon, ' . htmlspecialchars($username) . '!', 'success');
} else {
    // User wasn't logged in
    setFlashMessage('You were not logged in.', 'info');
}

// Redirect to login page
header('Location: login.php');
exit();
?>
