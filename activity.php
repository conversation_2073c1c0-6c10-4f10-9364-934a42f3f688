<?php
/**
 * User Activity Page
 * Shows user's personal activity log
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/roles.php';

// Require authentication
requireAuth();

// Get current user data
$currentUser = getCurrentUser();

// Get pagination parameters
$limit = (int)($_GET['limit'] ?? 25);
$page = (int)($_GET['page'] ?? 1);
$offset = ($page - 1) * $limit;

// Get user activity logs
$userLogs = getUserActivityLogs($currentUser['id'], $limit + 1); // Get one extra to check if there are more
$hasMore = count($userLogs) > $limit;
if ($hasMore) {
    array_pop($userLogs); // Remove the extra item
}

// Set page title
$pageTitle = 'My Activity';

// Include header
include 'templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">My Activity</h1>
            <p class="text-gray-600 mt-1">Your account activity and actions</p>
        </div>
        <div>
            <a href="dashboard.php" class="btn-secondary">← Back to Dashboard</a>
        </div>
    </div>
    
    <!-- Activity Statistics -->
    <div class="grid md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="text-3xl mr-4">📊</div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Total Activities</h3>
                    <p class="text-2xl font-bold text-blue-600">
                        <?php echo count(getUserActivityLogs($currentUser['id'], 1000)); ?>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('clock-time', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
                    <p class="text-2xl font-bold text-green-600">
                        <?php 
                        $recentLogs = array_filter(getUserActivityLogs($currentUser['id'], 100), function($log) {
                            return strtotime($log['timestamp']) > strtotime('-24 hours');
                        });
                        echo count($recentLogs);
                        ?>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('calendar-date', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Last Activity</h3>
                    <p class="text-lg font-bold text-purple-600">
                        <?php 
                        $allLogs = getUserActivityLogs($currentUser['id'], 1);
                        if (!empty($allLogs)) {
                            echo date('M j, g:i A', strtotime($allLogs[0]['timestamp']));
                        } else {
                            echo 'No activity';
                        }
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activity Filters -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
                <div>
                    <label for="activityLimit" class="block text-sm font-medium text-gray-700 mb-1">Show</label>
                    <select id="activityLimit" onchange="updateLimit()" class="border border-gray-300 rounded px-3 py-2">
                        <option value="10" <?php echo $limit === 10 ? 'selected' : ''; ?>>10 entries</option>
                        <option value="25" <?php echo $limit === 25 ? 'selected' : ''; ?>>25 entries</option>
                        <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50 entries</option>
                        <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100 entries</option>
                    </select>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="location.reload()" class="btn-secondary">
                    🔄 Refresh
                </button>
            </div>
        </div>
    </div>
    
    <!-- Activity Log -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Activity Log</h2>
        </div>
        
        <div class="divide-y divide-gray-200">
            <?php if (empty($userLogs)): ?>
                <div class="px-6 py-8 text-center text-gray-500">
                    <div class="text-4xl mb-4">📝</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Activity Yet</h3>
                    <p>Your activity will appear here as you use the application.</p>
                </div>
            <?php else: ?>
                <?php foreach ($userLogs as $log): ?>
                    <div class="px-6 py-4 hover:bg-gray-50">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <?php
                                    $action = $log['action'] ?? '';
                                    if (strpos($action, 'login') !== false) echo getIcon('security-lock', 'text-2xl');
                                    elseif (strpos($action, 'logout') !== false) echo getIcon('door-logout', 'text-2xl');
                                    elseif (strpos($action, 'profile') !== false) echo '👤';
                                    elseif (strpos($action, 'settings') !== false) echo getIcon('settings-gear', 'text-2xl');
                                    elseif (strpos($action, 'password') !== false) echo '🔑';
                                    elseif (strpos($action, 'registered') !== false) echo '✅';
                                    else echo getIcon('document-note', 'text-2xl');
                                    ?>
                                </div>
                            </div>
                            
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900">
                                            <?php 
                                            $actionParts = explode('.', $action);
                                            $actionName = end($actionParts);
                                            echo htmlspecialchars(ucwords(str_replace('_', ' ', $actionName)));
                                            ?>
                                        </h3>
                                        <p class="text-sm text-gray-600 mt-1">
                                            <?php echo htmlspecialchars($log['description'] ?? 'Activity performed'); ?>
                                        </p>
                                        
                                        <?php if (!empty($log['data']) && is_array($log['data'])): ?>
                                            <div class="mt-2">
                                                <button 
                                                    onclick="toggleDetails('details-<?php echo md5($log['timestamp']); ?>')"
                                                    class="text-xs text-blue-600 hover:text-blue-800"
                                                >
                                                    View Details
                                                </button>
                                                <div id="details-<?php echo md5($log['timestamp']); ?>" class="hidden mt-2 p-3 bg-gray-50 rounded text-xs">
                                                    <pre class="whitespace-pre-wrap"><?php echo htmlspecialchars(json_encode($log['data'], JSON_PRETTY_PRINT)); ?></pre>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">
                                            <?php echo date('M j, Y', strtotime($log['timestamp'])); ?>
                                        </p>
                                        <p class="text-xs text-gray-400">
                                            <?php echo date('g:i:s A', strtotime($log['timestamp'])); ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="mt-2 flex items-center text-xs text-gray-500 space-x-4">
                                    <span>IP: <?php echo htmlspecialchars($log['ip_address'] ?? 'Unknown'); ?></span>
                                    <span>Session: <?php echo htmlspecialchars(substr($log['session_id'] ?? 'Unknown', 0, 8)); ?>...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($page > 1 || $hasMore): ?>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    Showing <?php echo count($userLogs); ?> entries
                </div>
                <div class="flex space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?limit=<?php echo $limit; ?>&page=<?php echo $page - 1; ?>" 
                           class="btn-secondary">Previous</a>
                    <?php endif; ?>
                    <?php if ($hasMore): ?>
                        <a href="?limit=<?php echo $limit; ?>&page=<?php echo $page + 1; ?>" 
                           class="btn-secondary">Next</a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function updateLimit() {
    const limit = document.getElementById('activityLimit').value;
    const url = new URL(window.location);
    url.searchParams.set('limit', limit);
    url.searchParams.set('page', '1');
    window.location = url;
}

function toggleDetails(elementId) {
    const element = document.getElementById(elementId);
    if (element.classList.contains('hidden')) {
        element.classList.remove('hidden');
    } else {
        element.classList.add('hidden');
    }
}
</script>

<?php include 'templates/footer.php'; ?>
