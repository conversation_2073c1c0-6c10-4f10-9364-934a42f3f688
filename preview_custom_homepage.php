<?php
/**
 * Custom Homepage Preview
 * Displays the custom homepage HTML for preview purposes
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/roles.php';

// Require admin access
requireAdmin();

// Verify CSRF token
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        die('Invalid security token.');
    }
}

// Load current settings
$settings = loadSettings();
$customHtml = $settings['custom_homepage_html'] ?? '';

if (empty($customHtml)) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Custom Homepage Preview</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
                margin: 0;
                padding: 40px;
                background: #f3f4f6;
                color: #374151;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                text-align: center;
            }
            .icon {
                font-size: 48px;
                margin-bottom: 20px;
            }
            h1 {
                color: #1f2937;
                margin-bottom: 16px;
            }
            p {
                margin-bottom: 24px;
                line-height: 1.6;
            }
            .button {
                display: inline-block;
                background: #3b82f6;
                color: white;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 6px;
                font-weight: 500;
            }
            .button:hover {
                background: #2563eb;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="icon">⚠️</div>
            <h1>No Custom Homepage Configured</h1>
            <p>You haven't configured any custom HTML for your homepage yet.</p>
            <p>Click "Edit HTML" in the settings to add your custom homepage content.</p>
            <a href="javascript:window.close()" class="button">Close Preview</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Output the custom HTML directly
echo $customHtml;
?>
