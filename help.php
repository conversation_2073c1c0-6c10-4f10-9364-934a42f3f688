<?php
/**
 * Help & Support Page
 * Provides documentation and support information
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/roles.php';

// Require authentication
requireAuth();

// Get current user data
$currentUser = getCurrentUser();

// Set page title
$pageTitle = 'Help & Support';

// Include header
include 'templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Help & Support</h1>
            <p class="text-gray-600 mt-1">Documentation and support resources</p>
        </div>
        <div>
            <a href="dashboard.php" class="btn-secondary">← Back to Dashboard</a>
        </div>
    </div>
    
    <!-- Quick Help Cards -->
    <div class="grid md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="mb-4"><?php echo getIcon('rocket-deploy', 'text-3xl'); ?></div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Getting Started</h3>
            <p class="text-gray-600 text-sm">Learn the basics of using the AI Dashboard platform.</p>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="mb-4"><?php echo getIcon('user-profile', 'text-3xl'); ?></div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Account Management</h3>
            <p class="text-gray-600 text-sm">Manage your profile, password, and account settings.</p>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="mb-4"><?php echo getIcon('security-lock', 'text-3xl'); ?></div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Security</h3>
            <p class="text-gray-600 text-sm">Learn about security features and best practices.</p>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Frequently Asked Questions</h2>
        </div>
        
        <div class="p-6 space-y-6">
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">How do I change my password?</h3>
                <p class="text-gray-600">
                    Go to your <a href="profile.php" class="text-blue-600 hover:text-blue-800">Profile Settings</a> page and use the "Change Password" section. 
                    You'll need to enter your current password and then your new password twice for confirmation.
                </p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">How do I view my account activity?</h3>
                <p class="text-gray-600">
                    Visit the <a href="activity.php" class="text-blue-600 hover:text-blue-800">My Activity</a> page to see a detailed log of all your account activities, 
                    including logins, profile changes, and other actions.
                </p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">What are user roles?</h3>
                <p class="text-gray-600">
                    User roles determine what features you can access:
                </p>
                <ul class="mt-2 ml-4 space-y-1 text-gray-600">
                    <li>• <strong>User:</strong> Standard access to personal features</li>
                    <li>• <strong>Moderator:</strong> Limited administrative access</li>
                    <li>• <strong>Admin:</strong> Full system access and user management</li>
                </ul>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Why is my account locked?</h3>
                <p class="text-gray-600">
                    Accounts are automatically locked after multiple failed login attempts for security. 
                    Wait 30 minutes and try again, or contact an administrator for assistance.
                </p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">How do I update my profile information?</h3>
                <p class="text-gray-600">
                    Go to <a href="profile.php" class="text-blue-600 hover:text-blue-800">Profile Settings</a> to update your username and view your account information. 
                    Some information like your User ID and role can only be changed by administrators.
                </p>
            </div>
            
            <?php if (isAdmin()): ?>
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Admin Features</h3>
                <p class="text-gray-600">
                    As an administrator, you have access to:
                </p>
                <ul class="mt-2 ml-4 space-y-1 text-gray-600">
                    <li>• <a href="admin/users.php" class="text-blue-600 hover:text-blue-800">User Management</a> - Manage user accounts and roles</li>
                    <li>• <a href="admin/settings.php" class="text-blue-600 hover:text-blue-800">Application Settings</a> - Configure system behavior</li>
                    <li>• <a href="admin/logs.php" class="text-blue-600 hover:text-blue-800">System Logs</a> - Monitor activity and errors</li>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Contact Support -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <div class="mr-4"><?php echo getIcon('chat-support', 'text-3xl'); ?></div>
            <div>
                <h3 class="text-lg font-semibold text-blue-900 mb-2">Need More Help?</h3>
                <p class="text-blue-700 mb-4">
                    If you can't find the answer to your question here, don't hesitate to reach out for support.
                </p>
                
                <div class="space-y-2 text-sm">
                    <div class="flex items-center text-blue-700">
                        <span class="font-medium mr-2">Application Version:</span>
                        <span><?php echo APP_VERSION; ?></span>
                    </div>
                    <div class="flex items-center text-blue-700">
                        <span class="font-medium mr-2">Your Role:</span>
                        <span><?php echo getRoleDisplayName($currentUser['role']); ?></span>
                    </div>
                    <div class="flex items-center text-blue-700">
                        <span class="font-medium mr-2">User ID:</span>
                        <span class="font-mono"><?php echo $currentUser['id']; ?></span>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        Contact Support
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">System Information</h2>
        </div>
        
        <div class="p-6">
            <div class="grid md:grid-cols-2 gap-6 text-sm">
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Application Details</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Version:</span>
                            <span class="font-mono"><?php echo APP_VERSION; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">PHP Version:</span>
                            <span class="font-mono"><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Timezone:</span>
                            <span><?php echo getAppTimezone(); ?></span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Your Account</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Username:</span>
                            <span><?php echo htmlspecialchars($currentUser['username']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Role:</span>
                            <span><?php echo getRoleDisplayName($currentUser['role']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Session ID:</span>
                            <span class="font-mono"><?php echo substr(session_id(), 0, 8); ?>...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
