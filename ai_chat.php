<?php
/**
 * AI Chat Example
 * Demonstrates how to integrate with the AI system
 */

require_once 'config.php';
require_once 'auth.php';
require_once 'includes/ai_client.php';
require_once 'includes/theme.php';
require_once 'includes/toast.php';
require_once 'includes/logger.php';

// Require user to be logged in
requireAuth();

$currentUser = getCurrentUser();
$response = '';
$error = '';

// Handle chat submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $message = trim($_POST['message'] ?? '');
        
        if (empty($message)) {
            $error = 'Please enter a message.';
        } else {
            try {
                // Check if user has AI configured
                if (!currentUserHasAI()) {
                    $error = 'Please configure your AI settings first.';
                } else {
                    // Create AI client and send message
                    $client = createAIClient();
                    $result = $client->sendMessage($message);
                    
                    if ($result['success']) {
                        $response = $result['content'];
                        
                        // Log the AI usage
                        logActivity($currentUser['id'], 'ai_chat_used', 'Used AI chat feature with model: ' . $result['model']);
                    } else {
                        $error = 'Failed to get AI response.';
                    }
                }
            } catch (Exception $e) {
                $error = 'AI Error: ' . $e->getMessage();
            }
        }
    }
}

// Set page title
$pageTitle = 'AI Chat';
include 'templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">AI Chat</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Chat with your configured AI model</p>
        </div>
        <div class="flex space-x-3">
            <a href="ai_settings.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">AI Settings</a>
            <a href="dashboard.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">← Dashboard</a>
        </div>
    </div>

    <!-- AI Status -->
    <?php if (!currentUserHasAI()): ?>
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow border-l-4 border-yellow-500">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="text-yellow-500 mr-3">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">AI Not Configured</h3>
                        <p class="text-gray-600 dark:text-gray-300 mt-1">
                            Please configure your AI settings to use the chat feature.
                        </p>
                        <a href="ai_settings.php" class="inline-flex items-center mt-2 text-blue-600 dark:text-blue-400 hover:underline">
                            Configure AI Settings →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <?php $activeConfig = getActiveAIConfig($currentUser['id']); ?>
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow border-l-4 border-green-500">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="text-green-500 mr-3">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">AI Ready</h3>
                        <p class="text-gray-600 dark:text-gray-300 mt-1">
                            Using: <?php echo htmlspecialchars($activeConfig['display_name']); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Chat Interface -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Chat Interface</h2>
        </div>
        
        <div class="p-6">
            <!-- Chat Form -->
            <form method="POST" class="space-y-4">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Your Message
                    </label>
                    <textarea 
                        id="message" 
                        name="message" 
                        rows="4" 
                        class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                        placeholder="Type your message here..."
                        <?php echo !currentUserHasAI() ? 'disabled' : ''; ?>
                    ><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <?php if (currentUserHasAI()): ?>
                            Ready to chat with AI
                        <?php else: ?>
                            Configure AI settings to enable chat
                        <?php endif; ?>
                    </div>
                    <button 
                        type="submit" 
                        class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200"
                        <?php echo !currentUserHasAI() ? 'disabled' : ''; ?>
                    >
                        💬 Send Message
                    </button>
                </div>
            </form>
            
            <!-- Error Display -->
            <?php if ($error): ?>
                <div class="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <div class="flex items-center">
                        <div class="text-red-500 mr-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <p class="text-red-700 dark:text-red-300"><?php echo htmlspecialchars($error); ?></p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Response Display -->
            <?php if ($response): ?>
                <div class="mt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">AI Response:</h3>
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div class="prose dark:prose-invert max-w-none">
                            <?php echo nl2br(htmlspecialchars($response)); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Integration Example -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Integration Example</h2>
        </div>
        
        <div class="p-6">
            <p class="text-gray-600 dark:text-gray-300 mb-4">
                This page demonstrates how easy it is to integrate AI functionality into your applications:
            </p>
            
            <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre class="text-sm text-gray-800 dark:text-gray-200"><code><?php echo htmlspecialchars('// Simple AI integration example
try {
    $client = createAIClient();
    $result = $client->sendMessage("Hello, AI!");
    
    if ($result[\'success\']) {
        echo $result[\'content\'];
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}'); ?></code></pre>
            </div>
            
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-4">
                The AI system automatically handles provider-specific API calls, authentication, and response parsing.
            </p>
        </div>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
