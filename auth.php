<?php
/**
 * Authentication helper functions
 * Handles user registration, login, and user data management
 */

require_once 'config.php';
require_once 'includes/logger.php';
require_once 'includes/settings.php';

/**
 * Load users from JSON file
 * @return array
 */
function loadUsers() {
    if (!file_exists(USERS_FILE)) {
        return [];
    }
    
    $content = file_get_contents(USERS_FILE);
    $users = json_decode($content, true);
    
    return is_array($users) ? $users : [];
}

/**
 * Save users to JSON file
 * @param array $users
 * @return bool
 */
function saveUsers($users) {
    return file_put_contents(USERS_FILE, json_encode($users, JSON_PRETTY_PRINT)) !== false;
}

/**
 * Check if username already exists
 * @param string $username
 * @return bool
 */
function usernameExists($username) {
    $users = loadUsers();
    foreach ($users as $user) {
        if (strtolower($user['username']) === strtolower($username)) {
            return true;
        }
    }
    return false;
}

/**
 * Register a new user
 * @param string $username
 * @param string $password
 * @param string $role Optional role (defaults to setting)
 * @return array Result array with success status and message
 */
function registerUser($username, $password, $role = null) {
    // Check if registration is enabled
    if (!isRegistrationEnabled()) {
        logSecurityEvent('registration_disabled', "Registration attempt when disabled", [
            'attempted_username' => $username
        ]);
        return ['success' => false, 'message' => 'Registration is currently disabled.'];
    }

    // Validate input
    if (empty($username) || empty($password)) {
        return ['success' => false, 'message' => 'Username and password are required.'];
    }

    if (strlen($username) < 3) {
        return ['success' => false, 'message' => 'Username must be at least 3 characters long.'];
    }

    $minPasswordLength = getSetting('password_min_length', PASSWORD_MIN_LENGTH);
    if (strlen($password) < $minPasswordLength) {
        return ['success' => false, 'message' => "Password must be at least {$minPasswordLength} characters long."];
    }

    // Check if username already exists
    if (usernameExists($username)) {
        logSecurityEvent('duplicate_registration', "Registration attempt with existing username", [
            'attempted_username' => $username
        ]);
        return ['success' => false, 'message' => 'Username already exists.'];
    }

    // Load existing users
    $users = loadUsers();

    // First user becomes admin
    if (empty($users)) {
        $role = 'admin';
    } elseif ($role === null) {
        // Determine role for subsequent users
        $role = getSetting('default_user_role', 'user');
    }

    // Create new user
    $newUser = [
        'id' => uniqid(),
        'username' => sanitizeInput($username),
        'password' => password_hash($password, PASSWORD_DEFAULT),
        'role' => $role,
        'created_at' => date('Y-m-d H:i:s'),
        'last_login' => null,
        'login_attempts' => 0,
        'locked_until' => null,
        'email_verified' => !getSetting('require_email_verification', false)
    ];

    // Add user to array
    $users[] = $newUser;

    // Save users
    if (saveUsers($users)) {
        // Log successful registration
        logActivity('user.registered', "New user registered", [
            'username' => $username,
            'role' => $role,
            'is_first_user' => empty($users) - 1 === 0
        ]);

        return ['success' => true, 'message' => 'User registered successfully.'];
    } else {
        logError("Failed to save user data during registration", 'error', ['username' => $username]);
        return ['success' => false, 'message' => 'Failed to save user data.'];
    }
}

/**
 * Authenticate user login
 * @param string $username
 * @param string $password
 * @return array Result array with success status and message
 */
function loginUser($username, $password) {
    // Validate input
    if (empty($username) || empty($password)) {
        return ['success' => false, 'message' => 'Username and password are required.'];
    }

    // Load users
    $users = loadUsers();
    $maxAttempts = getSetting('max_login_attempts', 5);

    // Find user
    foreach ($users as $index => $user) {
        if (strtolower($user['username']) === strtolower($username)) {
            // Check if account is locked
            if (isset($user['locked_until']) && $user['locked_until']) {
                $lockedUntil = strtotime($user['locked_until']);
                if ($lockedUntil > time()) {
                    $remainingTime = ceil(($lockedUntil - time()) / 60);
                    logSecurityEvent('login_attempt_locked', "Login attempt on locked account", [
                        'username' => $username,
                        'locked_until' => $user['locked_until']
                    ]);
                    return ['success' => false, 'message' => "Account is locked. Try again in {$remainingTime} minutes."];
                } else {
                    // Unlock account
                    $users[$index]['locked_until'] = null;
                    $users[$index]['login_attempts'] = 0;
                }
            }

            // Verify password
            if (password_verify($password, $user['password'])) {
                // Reset login attempts on successful login
                $users[$index]['login_attempts'] = 0;
                $users[$index]['locked_until'] = null;
                $users[$index]['last_login'] = date('Y-m-d H:i:s');
                $users[$index]['last_login_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

                saveUsers($users);

                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_role'] = $user['role'] ?? 'user';
                $_SESSION['login_time'] = time();

                // Log successful login
                logActivity('user.login', "User logged in successfully", [
                    'username' => $username,
                    'role' => $user['role'] ?? 'user'
                ]);

                return ['success' => true, 'message' => 'Login successful.'];
            } else {
                // Increment login attempts
                $attempts = ($user['login_attempts'] ?? 0) + 1;
                $users[$index]['login_attempts'] = $attempts;

                // Lock account if max attempts reached
                if ($attempts >= $maxAttempts) {
                    $users[$index]['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 minutes'));
                    logSecurityEvent('account_locked', "Account locked due to too many failed login attempts", [
                        'username' => $username,
                        'attempts' => $attempts,
                        'locked_until' => $users[$index]['locked_until']
                    ]);
                    saveUsers($users);
                    return ['success' => false, 'message' => 'Account locked due to too many failed attempts. Try again in 30 minutes.'];
                }

                saveUsers($users);

                // Log failed login attempt
                logSecurityEvent('login_failed', "Failed login attempt", [
                    'username' => $username,
                    'attempts' => $attempts,
                    'remaining_attempts' => $maxAttempts - $attempts
                ]);

                $remainingAttempts = $maxAttempts - $attempts;
                return ['success' => false, 'message' => "Invalid password. {$remainingAttempts} attempts remaining."];
            }
        }
    }

    // Log failed login for non-existent user
    logSecurityEvent('login_failed_unknown_user', "Login attempt with unknown username", [
        'attempted_username' => $username
    ]);

    return ['success' => false, 'message' => 'Invalid username or password.'];
}

/**
 * Logout user
 */
function logoutUser() {
    // Log logout activity before clearing session
    if (isLoggedIn()) {
        logActivity('user.logout', "User logged out", [
            'username' => $_SESSION['username'],
            'session_duration' => time() - ($_SESSION['login_time'] ?? time())
        ]);
    }

    // Clear all session variables
    $_SESSION = [];

    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Destroy the session
    session_destroy();
}

/**
 * Get user by ID
 * @param string $userId
 * @return array|null
 */
function getUserById($userId) {
    $users = loadUsers();
    foreach ($users as $user) {
        if ($user['id'] === $userId) {
            return $user;
        }
    }
    return null;
}
?>
