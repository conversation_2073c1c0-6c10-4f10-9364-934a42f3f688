<?php
/**
 * Theme Management System
 * Handles dark mode and theme preferences
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../auth.php';
require_once __DIR__ . '/logger.php';

/**
 * Get user's theme preference
 * @param string $userId User ID (optional, uses session if not provided)
 * @return string Theme preference ('light', 'dark', 'auto')
 */
function getUserTheme($userId = null) {
    if (!$userId && isLoggedIn()) {
        $userId = $_SESSION['user_id'];
    }

    // Always check user data file first to ensure consistency across sessions
    if ($userId) {
        $userData = getUserById($userId);
        if ($userData && isset($userData['theme_preference'])) {
            // Clear any stale session cache and update with current file data
            unset($_SESSION['theme_preference']);
            $_SESSION['theme_preference'] = $userData['theme_preference'];
            return $userData['theme_preference'];
        }
    }

    // Check session as fallback (for non-logged-in users)
    if (isset($_SESSION['theme_preference'])) {
        return $_SESSION['theme_preference'];
    }

    // Default to auto (system preference)
    return 'auto';
}

/**
 * Set user's theme preference
 * @param string $theme Theme preference ('light', 'dark', 'auto')
 * @param string $userId User ID (optional, uses session if not provided)
 * @return bool Success status
 */
function setUserTheme($theme, $userId = null) {
    if (!in_array($theme, ['light', 'dark', 'auto'])) {
        return false;
    }
    
    if (!$userId && isLoggedIn()) {
        $userId = $_SESSION['user_id'];
    }
    
    // Update session
    $_SESSION['theme_preference'] = $theme;
    
    // Update user data file if user is logged in
    if ($userId) {
        $users = loadUsers();
        foreach ($users as $index => $user) {
            if ($user['id'] === $userId) {
                $users[$index]['theme_preference'] = $theme;
                $users[$index]['theme_updated_at'] = date('Y-m-d H:i:s');
                
                if (saveUsers($users)) {
                    // Log theme change
                    logActivity('theme.changed', "Theme preference changed to {$theme}", [
                        'new_theme' => $theme,
                        'user_id' => $userId
                    ]);
                    return true;
                }
                break;
            }
        }
    }
    
    return false;
}

/**
 * Get theme CSS classes for current user
 * @return string CSS classes for theme
 */
function getThemeClasses() {
    $theme = getUserTheme();
    
    switch ($theme) {
        case 'dark':
            return 'dark';
        case 'light':
            return '';
        case 'auto':
        default:
            return ''; // Let CSS handle auto detection
    }
}

/**
 * Generate theme toggle button HTML
 * @return string HTML for theme toggle button
 */
function getThemeToggleButton() {
    $currentTheme = getUserTheme();
    
    return '
    <button 
        id="themeToggle" 
        class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200"
        title="Toggle theme"
        onclick="toggleTheme()"
    >
        <svg id="themeIcon" class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <!-- Sun icon (light mode) -->
            <path id="sunIcon" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" class="' . ($currentTheme === 'dark' ? 'hidden' : '') . '"></path>
            <!-- Moon icon (dark mode) -->
            <path id="moonIcon" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" class="' . ($currentTheme === 'light' ? 'hidden' : '') . '"></path>
        </svg>
    </button>';
}

/**
 * Get theme configuration for JavaScript
 * @return array Theme configuration
 */
function getThemeConfig() {
    return [
        'current' => getUserTheme(),
        'available' => ['light', 'dark', 'auto'],
        'labels' => [
            'light' => 'Light Mode',
            'dark' => 'Dark Mode',
            'auto' => 'System Default'
        ]
    ];
}

/**
 * Apply theme to page body classes
 * @return string Body classes with theme
 */
function getBodyClasses() {
    $theme = getUserTheme();
    $classes = ['min-h-screen', 'transition-colors', 'duration-200'];
    
    switch ($theme) {
        case 'dark':
            $classes[] = 'dark';
            $classes[] = 'bg-gray-900';
            $classes[] = 'text-white';
            break;
        case 'light':
            $classes[] = 'bg-gray-50';
            $classes[] = 'text-gray-900';
            break;
        case 'auto':
        default:
            $classes[] = 'bg-gray-50';
            $classes[] = 'dark:bg-gray-900';
            $classes[] = 'text-gray-900';
            $classes[] = 'dark:text-white';
            break;
    }
    
    return implode(' ', $classes);
}

/**
 * Get theme-aware CSS classes for components
 * @param string $component Component type (card, button, input, etc.)
 * @return string CSS classes for the component
 */
function getThemeComponentClasses($component) {
    $classes = [
        'card' => 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm',
        'card-header' => 'border-b border-gray-200 dark:border-gray-700',
        'button-primary' => 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white',
        'button-secondary' => 'bg-gray-600 hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 text-white',
        'input' => 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500',
        'text-primary' => 'text-gray-900 dark:text-white',
        'text-secondary' => 'text-gray-600 dark:text-gray-300',
        'text-muted' => 'text-gray-500 dark:text-gray-400',
        'nav' => 'bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700',
        'nav-link' => 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white',
        'table-header' => 'bg-gray-50 dark:bg-gray-700',
        'table-row' => 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700',
        'table-cell' => 'text-gray-900 dark:text-white',
        'table-cell-secondary' => 'text-gray-600 dark:text-gray-300',
        'badge-admin' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
        'badge-moderator' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
        'badge-user' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
        'badge-success' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
        'badge-error' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
        'badge-warning' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
        'badge-info' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
        'alert-success' => 'bg-green-100 dark:bg-green-900 border-green-400 dark:border-green-600 text-green-700 dark:text-green-300',
        'alert-error' => 'bg-red-100 dark:bg-red-900 border-red-400 dark:border-red-600 text-red-700 dark:text-red-300',
        'alert-warning' => 'bg-yellow-100 dark:bg-yellow-900 border-yellow-400 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300',
        'alert-info' => 'bg-blue-100 dark:bg-blue-900 border-blue-400 dark:border-blue-600 text-blue-700 dark:text-blue-300',
        'link' => 'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',
        'link-danger' => 'text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300',
        'divider' => 'border-gray-200 dark:border-gray-700'
    ];

    return $classes[$component] ?? '';
}

/**
 * Check if dark mode is preferred by system
 * @return bool True if system prefers dark mode
 */
function isSystemDarkMode() {
    // This would typically be handled by JavaScript
    // PHP can't detect system preference directly
    return false;
}

/**
 * Get theme statistics for admin
 * @return array Theme usage statistics
 */
function getThemeStatistics() {
    try {
        $users = loadUsers();
        $stats = [
            'light' => 0,
            'dark' => 0,
            'auto' => 0,
            'total' => count($users)
        ];
        
        foreach ($users as $user) {
            $theme = $user['theme_preference'] ?? 'auto';
            if (isset($stats[$theme])) {
                $stats[$theme]++;
            }
        }
        
        return $stats;
    } catch (Exception $e) {
        logError("Failed to get theme statistics: " . $e->getMessage());
        return ['light' => 0, 'dark' => 0, 'auto' => 0, 'total' => 0];
    }
}

/**
 * Handle theme change AJAX request
 */
function handleThemeChangeRequest() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_theme') {
        header('Content-Type: application/json');
        
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            echo json_encode(['success' => false, 'message' => 'Invalid security token']);
            exit();
        }
        
        $theme = $_POST['theme'] ?? '';
        if (setUserTheme($theme)) {
            echo json_encode(['success' => true, 'theme' => $theme]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update theme']);
        }
        exit();
    }
}

// Handle AJAX theme change requests
if (isset($_POST['action']) && $_POST['action'] === 'change_theme') {
    handleThemeChangeRequest();
}
?>
