<?php
/**
 * Role-Based Access Control System
 * Handles user roles and permissions
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/logger.php';
require_once __DIR__ . '/settings.php';

/**
 * Available user roles and their permissions
 */
function getRoleDefinitions() {
    return [
        'admin' => [
            'name' => 'Administrator',
            'description' => 'Full system access and user management',
            'permissions' => [
                'user.create',
                'user.read',
                'user.update',
                'user.delete',
                'user.manage_roles',
                'settings.read',
                'settings.update',
                'logs.read',
                'logs.clear',
                'system.maintenance',
                'system.backup',
                'admin.access'
            ]
        ],
        'moderator' => [
            'name' => 'Moderator',
            'description' => 'Limited administrative access',
            'permissions' => [
                'user.read',
                'user.update',
                'logs.read',
                'admin.access'
            ]
        ],
        'user' => [
            'name' => 'User',
            'description' => 'Standard user access',
            'permissions' => [
                'profile.read',
                'profile.update',
                'dashboard.access'
            ]
        ],
        'guest' => [
            'name' => 'Guest',
            'description' => 'Limited read-only access',
            'permissions' => [
                'public.read'
            ]
        ]
    ];
}

/**
 * Get permissions for a specific role
 * @param string $role Role name
 * @return array Array of permissions
 */
function getRolePermissions($role) {
    $roles = getRoleDefinitions();
    return $roles[$role]['permissions'] ?? [];
}

/**
 * Check if a role has a specific permission
 * @param string $role Role name
 * @param string $permission Permission to check
 * @return bool True if role has permission
 */
function roleHasPermission($role, $permission) {
    $permissions = getRolePermissions($role);
    return in_array($permission, $permissions);
}

/**
 * Check if current user has a specific permission
 * @param string $permission Permission to check
 * @return bool True if user has permission
 */
function userHasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $userRole = $_SESSION['user_role'] ?? 'user';
    return roleHasPermission($userRole, $permission);
}

/**
 * Require a specific permission (redirect if not authorized)
 * @param string $permission Required permission
 * @param string $redirectUrl URL to redirect to if unauthorized
 */
function requirePermission($permission, $redirectUrl = 'dashboard.php') {
    if (!userHasPermission($permission)) {
        logSecurityEvent('unauthorized_access', "User attempted to access resource requiring permission: {$permission}", [
            'required_permission' => $permission,
            'user_role' => $_SESSION['user_role'] ?? 'guest'
        ]);
        
        setFlashMessage('Access denied. You do not have permission to access this resource.', 'error');
        header("Location: {$redirectUrl}");
        exit();
    }
}

/**
 * Get all available roles
 * @return array Array of role names
 */
function getAvailableRoles() {
    return array_keys(getRoleDefinitions());
}

/**
 * Get role display name
 * @param string $role Role name
 * @return string Role display name
 */
function getRoleDisplayName($role) {
    $roles = getRoleDefinitions();
    return $roles[$role]['name'] ?? ucfirst($role);
}

/**
 * Get role description
 * @param string $role Role name
 * @return string Role description
 */
function getRoleDescription($role) {
    $roles = getRoleDefinitions();
    return $roles[$role]['description'] ?? '';
}

/**
 * Validate if a role exists
 * @param string $role Role name to validate
 * @return bool True if role exists
 */
function isValidRole($role) {
    return array_key_exists($role, getRoleDefinitions());
}

/**
 * Update user role
 * @param string $userId User ID
 * @param string $newRole New role to assign
 * @return bool Success status
 */
function updateUserRole($userId, $newRole) {
    try {
        // Validate role
        if (!isValidRole($newRole)) {
            logError("Attempted to assign invalid role: {$newRole}", 'warning');
            return false;
        }
        
        // Check permission
        if (!userHasPermission('user.manage_roles')) {
            logSecurityEvent('unauthorized_role_change', "User attempted to change role without permission", [
                'target_user_id' => $userId,
                'attempted_role' => $newRole
            ]);
            return false;
        }
        
        // Load users
        $users = loadUsers();
        $userFound = false;
        
        foreach ($users as $index => $user) {
            if ($user['id'] === $userId) {
                $oldRole = $user['role'] ?? 'user';
                $users[$index]['role'] = $newRole;
                $users[$index]['role_updated_at'] = date('Y-m-d H:i:s');
                $users[$index]['role_updated_by'] = $_SESSION['user_id'] ?? 'system';
                $userFound = true;
                
                // Log the role change
                logActivity('user.role_changed', "User role changed from {$oldRole} to {$newRole}", [
                    'target_user_id' => $userId,
                    'target_username' => $user['username'],
                    'old_role' => $oldRole,
                    'new_role' => $newRole,
                    'changed_by' => $_SESSION['username'] ?? 'system'
                ]);
                
                break;
            }
        }
        
        if (!$userFound) {
            logError("Attempted to update role for non-existent user: {$userId}", 'warning');
            return false;
        }
        
        // Save users
        return saveUsers($users);
        
    } catch (Exception $e) {
        logError("Failed to update user role: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Get users by role
 * @param string $role Role to filter by
 * @return array Array of users with the specified role
 */
function getUsersByRole($role) {
    try {
        $users = loadUsers();
        return array_filter($users, function($user) use ($role) {
            return ($user['role'] ?? 'user') === $role;
        });
    } catch (Exception $e) {
        logError("Failed to get users by role: " . $e->getMessage(), 'error');
        return [];
    }
}

/**
 * Count users by role
 * @return array Array with role counts
 */
function getUserRoleCounts() {
    try {
        $users = loadUsers();
        $counts = [];
        
        // Initialize counts for all roles
        foreach (getAvailableRoles() as $role) {
            $counts[$role] = 0;
        }
        
        // Count users
        foreach ($users as $user) {
            $userRole = $user['role'] ?? 'user';
            if (isset($counts[$userRole])) {
                $counts[$userRole]++;
            }
        }
        
        return $counts;
    } catch (Exception $e) {
        logError("Failed to count users by role: " . $e->getMessage(), 'error');
        return [];
    }
}

/**
 * Check if user can perform action on target user
 * @param string $action Action to perform
 * @param string $targetUserId Target user ID
 * @return bool True if action is allowed
 */
function canPerformUserAction($action, $targetUserId) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $currentUserId = $_SESSION['user_id'];
    $currentUserRole = $_SESSION['user_role'] ?? 'user';
    
    // Users can always manage their own profile
    if ($targetUserId === $currentUserId && in_array($action, ['profile.read', 'profile.update'])) {
        return true;
    }
    
    // Check if user has the required permission
    if (!userHasPermission($action)) {
        return false;
    }
    
    // Additional checks for sensitive actions
    if ($action === 'user.delete' || $action === 'user.manage_roles') {
        // Get target user
        $targetUser = getUserById($targetUserId);
        if (!$targetUser) {
            return false;
        }
        
        $targetUserRole = $targetUser['role'] ?? 'user';
        
        // Admins can't delete/modify other admins unless they are super admin
        if ($targetUserRole === 'admin' && $currentUserRole !== 'admin') {
            return false;
        }
        
        // Users can't delete/modify themselves in certain cases
        if ($targetUserId === $currentUserId && $action === 'user.delete') {
            return false;
        }
    }
    
    return true;
}

/**
 * Get user's effective permissions
 * @param string $userId User ID (optional, defaults to current user)
 * @return array Array of permissions
 */
function getUserPermissions($userId = null) {
    if ($userId) {
        $user = getUserById($userId);
        $role = $user['role'] ?? 'user';
    } else {
        $role = $_SESSION['user_role'] ?? 'user';
    }
    
    return getRolePermissions($role);
}

/**
 * Create role-based navigation menu
 * @return array Navigation menu items based on user role
 */
function getRoleBasedNavigation() {
    $navigation = [
        [
            'title' => 'Dashboard',
            'url' => 'dashboard.php',
            'icon' => '🏠',
            'permission' => 'dashboard.access'
        ]
    ];
    
    // Admin navigation
    if (userHasPermission('admin.access')) {
        $navigation[] = [
            'title' => 'User Management',
            'url' => 'admin/users.php',
            'icon' => '👥',
            'permission' => 'user.read'
        ];
        
        $navigation[] = [
            'title' => 'Settings',
            'url' => 'admin/settings.php',
            'icon' => '⚙️',
            'permission' => 'settings.read'
        ];
        
        $navigation[] = [
            'title' => 'Logs',
            'url' => 'admin/logs.php',
            'icon' => '📋',
            'permission' => 'logs.read'
        ];
    }
    
    // Filter navigation based on permissions
    return array_filter($navigation, function($item) {
        return userHasPermission($item['permission']);
    });
}
?>
