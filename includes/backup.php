<?php
/**
 * Backup and Export System
 * Handles data backup, export, and restore functionality
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/logger.php';

/**
 * Create a full backup of all application data
 * @param array $options Backup options
 * @return array Backup result with file path and metadata
 */
function createBackup($options = []) {
    try {
        $includeSettings = $options['include_settings'] ?? true;
        $includeUsers = $options['include_users'] ?? true;
        $includeLogs = $options['include_logs'] ?? true;
        $format = $options['format'] ?? 'zip'; // zip or json
        
        $timestamp = date('Y-m-d_H-i-s');
        $backupName = 'ai_dashboard_backup_' . $timestamp;
        
        // Create backup directory if it doesn't exist
        $backupDir = __DIR__ . '/../backups';
        if (!file_exists($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $backupData = [
            'metadata' => [
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => $_SESSION['username'] ?? 'system',
                'app_version' => APP_VERSION,
                'php_version' => PHP_VERSION,
                'backup_type' => 'full',
                'includes' => [
                    'settings' => $includeSettings,
                    'users' => $includeUsers,
                    'logs' => $includeLogs
                ]
            ]
        ];
        
        // Include application settings
        if ($includeSettings && file_exists(APP_SETTINGS_FILE)) {
            $backupData['settings'] = json_decode(file_get_contents(APP_SETTINGS_FILE), true);
        }
        
        // Include users data
        if ($includeUsers && file_exists(USERS_FILE)) {
            $users = json_decode(file_get_contents(USERS_FILE), true);
            // Remove sensitive data from backup
            foreach ($users as &$user) {
                unset($user['password']); // Don't backup passwords for security
            }
            $backupData['users'] = $users;
        }
        
        // Include logs
        if ($includeLogs) {
            if (file_exists(ACTIVITY_LOG_FILE)) {
                $backupData['activity_logs'] = json_decode(file_get_contents(ACTIVITY_LOG_FILE), true);
            }
            if (file_exists(ERROR_LOG_FILE)) {
                $backupData['error_logs'] = json_decode(file_get_contents(ERROR_LOG_FILE), true);
            }
        }
        
        if ($format === 'zip') {
            return createZipBackup($backupData, $backupDir, $backupName);
        } else {
            return createJsonBackup($backupData, $backupDir, $backupName);
        }
        
    } catch (Exception $e) {
        logError("Backup creation failed: " . $e->getMessage());
        return ['success' => false, 'message' => 'Backup creation failed: ' . $e->getMessage()];
    }
}

/**
 * Create ZIP backup
 * @param array $backupData Backup data
 * @param string $backupDir Backup directory
 * @param string $backupName Backup name
 * @return array Backup result
 */
function createZipBackup($backupData, $backupDir, $backupName) {
    if (!class_exists('ZipArchive')) {
        return createJsonBackup($backupData, $backupDir, $backupName);
    }
    
    $zipFile = $backupDir . '/' . $backupName . '.zip';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception("Cannot create ZIP file: $zipFile");
    }
    
    // Add JSON data file
    $zip->addFromString('backup_data.json', json_encode($backupData, JSON_PRETTY_PRINT));
    
    // Add original files if they exist
    $filesToBackup = [
        'users.json' => USERS_FILE,
        'app_settings.json' => APP_SETTINGS_FILE,
        'activity_logs.json' => ACTIVITY_LOG_FILE,
        'error_logs.json' => ERROR_LOG_FILE
    ];
    
    foreach ($filesToBackup as $archiveName => $filePath) {
        if (file_exists($filePath)) {
            $zip->addFile($filePath, $archiveName);
        }
    }
    
    // Add README
    $readme = generateBackupReadme($backupData['metadata']);
    $zip->addFromString('README.txt', $readme);
    
    $zip->close();
    
    $fileSize = filesize($zipFile);
    
    // Log backup creation
    logActivity('backup.created', 'Data backup created', [
        'backup_file' => basename($zipFile),
        'backup_size' => $fileSize,
        'format' => 'zip',
        'includes' => $backupData['metadata']['includes']
    ]);
    
    return [
        'success' => true,
        'file_path' => $zipFile,
        'file_name' => basename($zipFile),
        'file_size' => $fileSize,
        'format' => 'zip',
        'metadata' => $backupData['metadata']
    ];
}

/**
 * Create JSON backup
 * @param array $backupData Backup data
 * @param string $backupDir Backup directory
 * @param string $backupName Backup name
 * @return array Backup result
 */
function createJsonBackup($backupData, $backupDir, $backupName) {
    $jsonFile = $backupDir . '/' . $backupName . '.json';
    
    $jsonContent = json_encode($backupData, JSON_PRETTY_PRINT);
    if (file_put_contents($jsonFile, $jsonContent) === false) {
        throw new Exception("Cannot write backup file: $jsonFile");
    }
    
    $fileSize = filesize($jsonFile);
    
    // Log backup creation
    logActivity('backup.created', 'Data backup created', [
        'backup_file' => basename($jsonFile),
        'backup_size' => $fileSize,
        'format' => 'json',
        'includes' => $backupData['metadata']['includes']
    ]);
    
    return [
        'success' => true,
        'file_path' => $jsonFile,
        'file_name' => basename($jsonFile),
        'file_size' => $fileSize,
        'format' => 'json',
        'metadata' => $backupData['metadata']
    ];
}

/**
 * Generate README content for backup
 * @param array $metadata Backup metadata
 * @return string README content
 */
function generateBackupReadme($metadata) {
    $readme = "AI Dashboard Backup\n";
    $readme .= "==================\n\n";
    $readme .= "Created: " . $metadata['created_at'] . "\n";
    $readme .= "Created by: " . $metadata['created_by'] . "\n";
    $readme .= "App Version: " . $metadata['app_version'] . "\n";
    $readme .= "PHP Version: " . $metadata['php_version'] . "\n\n";
    
    $readme .= "Included Data:\n";
    foreach ($metadata['includes'] as $type => $included) {
        $readme .= "- " . ucfirst($type) . ": " . ($included ? "Yes" : "No") . "\n";
    }
    
    $readme .= "\nFiles:\n";
    $readme .= "- backup_data.json: Complete backup data in JSON format\n";
    $readme .= "- users.json: User accounts (passwords excluded for security)\n";
    $readme .= "- app_settings.json: Application configuration\n";
    $readme .= "- activity_logs.json: User activity logs\n";
    $readme .= "- error_logs.json: System error logs\n\n";
    
    $readme .= "Note: Passwords are not included in backups for security reasons.\n";
    $readme .= "Users will need to reset their passwords after restore.\n";
    
    return $readme;
}

/**
 * Get list of available backups
 * @return array List of backup files with metadata
 */
function getBackupList() {
    try {
        $backupDir = __DIR__ . '/../backups';
        $backups = [];
        
        if (!file_exists($backupDir)) {
            return $backups;
        }
        
        $files = glob($backupDir . '/ai_dashboard_backup_*.{zip,json}', GLOB_BRACE);
        
        foreach ($files as $file) {
            $fileName = basename($file);
            $fileSize = filesize($file);
            $fileTime = filemtime($file);
            $extension = pathinfo($file, PATHINFO_EXTENSION);
            
            // Try to extract timestamp from filename
            if (preg_match('/ai_dashboard_backup_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})/', $fileName, $matches)) {
                $timestamp = str_replace('_', ' ', $matches[1]);
                $timestamp = str_replace('-', ':', substr($timestamp, 0, 10) . ' ' . substr($timestamp, 11));
            } else {
                $timestamp = date('Y-m-d H:i:s', $fileTime);
            }
            
            $backups[] = [
                'file_name' => $fileName,
                'file_path' => $file,
                'file_size' => $fileSize,
                'file_size_formatted' => formatFileSize($fileSize),
                'created_at' => $timestamp,
                'format' => $extension,
                'age' => time() - $fileTime
            ];
        }
        
        // Sort by creation time (newest first)
        usort($backups, function($a, $b) {
            return $b['age'] <=> $a['age'];
        });
        
        return $backups;
    } catch (Exception $e) {
        logError("Failed to get backup list: " . $e->getMessage());
        return [];
    }
}

/**
 * Delete a backup file
 * @param string $fileName Backup file name
 * @return bool Success status
 */
function deleteBackup($fileName) {
    try {
        $backupDir = __DIR__ . '/../backups';
        $filePath = $backupDir . '/' . basename($fileName); // Ensure no path traversal
        
        if (!file_exists($filePath)) {
            return false;
        }
        
        // Verify it's a backup file
        if (!preg_match('/^ai_dashboard_backup_.*\.(zip|json)$/', basename($filePath))) {
            return false;
        }
        
        if (unlink($filePath)) {
            logActivity('backup.deleted', 'Backup file deleted', [
                'backup_file' => $fileName,
                'deleted_by' => $_SESSION['username'] ?? 'system'
            ]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        logError("Failed to delete backup: " . $e->getMessage());
        return false;
    }
}

/**
 * Download a backup file
 * @param string $fileName Backup file name
 */
function downloadBackup($fileName) {
    try {
        $backupDir = __DIR__ . '/../backups';
        $filePath = $backupDir . '/' . basename($fileName);
        
        if (!file_exists($filePath)) {
            throw new Exception("Backup file not found");
        }
        
        // Verify it's a backup file
        if (!preg_match('/^ai_dashboard_backup_.*\.(zip|json)$/', basename($filePath))) {
            throw new Exception("Invalid backup file");
        }
        
        $fileSize = filesize($filePath);
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        // Set headers for download
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . $fileSize);
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: 0');
        
        // Log download
        logActivity('backup.downloaded', 'Backup file downloaded', [
            'backup_file' => $fileName,
            'file_size' => $fileSize,
            'downloaded_by' => $_SESSION['username'] ?? 'system'
        ]);
        
        // Output file
        readfile($filePath);
        exit();
        
    } catch (Exception $e) {
        logError("Failed to download backup: " . $e->getMessage());
        header('HTTP/1.1 404 Not Found');
        echo "Backup file not found";
        exit();
    }
}

/**
 * Format file size in human readable format
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Clean up old backup files
 * @param int $maxAge Maximum age in days
 * @param int $maxCount Maximum number of backups to keep
 * @return int Number of files deleted
 */
function cleanupOldBackups($maxAge = 30, $maxCount = 10) {
    try {
        $backups = getBackupList();
        $deleted = 0;
        
        // Delete by age
        foreach ($backups as $backup) {
            if ($backup['age'] > ($maxAge * 24 * 3600)) {
                if (deleteBackup($backup['file_name'])) {
                    $deleted++;
                }
            }
        }
        
        // Delete excess backups (keep only maxCount newest)
        $backups = getBackupList(); // Refresh list
        if (count($backups) > $maxCount) {
            $excessBackups = array_slice($backups, $maxCount);
            foreach ($excessBackups as $backup) {
                if (deleteBackup($backup['file_name'])) {
                    $deleted++;
                }
            }
        }
        
        if ($deleted > 0) {
            logActivity('backup.cleanup', "Cleaned up {$deleted} old backup files", [
                'deleted_count' => $deleted,
                'max_age_days' => $maxAge,
                'max_count' => $maxCount
            ]);
        }
        
        return $deleted;
    } catch (Exception $e) {
        logError("Failed to cleanup old backups: " . $e->getMessage());
        return 0;
    }
}
?>
