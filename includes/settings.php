<?php
/**
 * Application Settings Management
 * Handles loading and saving application-wide settings
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/logger.php';

// Global settings cache
$_SETTINGS_CACHE = null;

/**
 * Load application settings from file
 * @return array Application settings
 */
function loadSettings() {
    global $_SETTINGS_CACHE;

    // Check if settings file has been modified since cache was created
    $cacheValid = false;
    if ($_SETTINGS_CACHE !== null && isset($_SETTINGS_CACHE['_cache_time'])) {
        $fileModTime = file_exists(APP_SETTINGS_FILE) ? filemtime(APP_SETTINGS_FILE) : 0;
        $cacheValid = $_SETTINGS_CACHE['_cache_time'] >= $fileModTime;
    }

    // Return cached settings if available and still valid
    if ($_SETTINGS_CACHE !== null && $cacheValid) {
        // Remove cache metadata before returning
        $settings = $_SETTINGS_CACHE;
        unset($settings['_cache_time']);
        return $settings;
    }
    
    try {
        if (!file_exists(APP_SETTINGS_FILE)) {
            return getDefaultSettings();
        }
        
        $content = file_get_contents(APP_SETTINGS_FILE);
        $settings = json_decode($content, true);
        
        if (!is_array($settings)) {
            logError("Invalid settings file format", 'warning');
            return getDefaultSettings();
        }
        
        // Merge with defaults to ensure all keys exist
        $settings = array_merge(getDefaultSettings(), $settings);
        
        // Cache the settings with timestamp
        $_SETTINGS_CACHE = $settings;
        $_SETTINGS_CACHE['_cache_time'] = filemtime(APP_SETTINGS_FILE);

        return $settings;
    } catch (Exception $e) {
        logError("Failed to load settings: " . $e->getMessage(), 'error');
        return getDefaultSettings();
    }
}

/**
 * Get default application settings
 * @return array Default settings
 */
function getDefaultSettings() {
    return [
        'app_name' => 'AI Dashboard',
        'timezone' => 'UTC',
        'maintenance_mode' => false,
        'registration_enabled' => true,
        'max_login_attempts' => 5,
        'session_timeout' => 3600,
        'password_min_length' => 8,
        'require_email_verification' => false,
        'default_user_role' => 'user',
        'admin_email' => '',
        'site_description' => 'Modern AI Dashboard Application',
        'theme' => 'default',
        'enable_logging' => true,
        'log_retention_days' => 30,
        'max_file_upload_size' => '10MB',
        'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'enable_two_factor' => false,
        'backup_enabled' => false,
        'backup_frequency' => 'daily',
        'show_footer_links' => false,
        'footer_links' => [
            ['text' => 'Privacy Policy', 'url' => '#'],
            ['text' => 'Terms of Service', 'url' => '#'],
            ['text' => 'Contact Us', 'url' => '#']
        ],
        'homepage_type' => 'default',
        'custom_homepage_html' => ''
    ];
}

/**
 * Save application settings to file
 * @param array $settings Settings to save
 * @return bool Success status
 */
function saveSettings($settings) {
    global $_SETTINGS_CACHE;

    try {
        // Validate settings
        $validatedSettings = validateSettings($settings);

        // Save to file
        $result = file_put_contents(APP_SETTINGS_FILE, json_encode($validatedSettings, JSON_PRETTY_PRINT));
        
        if ($result !== false) {
            // Update cache with timestamp
            $_SETTINGS_CACHE = $validatedSettings;
            $_SETTINGS_CACHE['_cache_time'] = filemtime(APP_SETTINGS_FILE);

            // Log the change
            logActivity('settings.updated', 'Application settings updated', [
                'updated_by' => $_SESSION['username'] ?? 'system',
                'settings_count' => count($validatedSettings)
            ]);

            return true;
        }
        
        return false;
    } catch (Exception $e) {
        logError("Failed to save settings: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Validate settings before saving
 * @param array $settings Settings to validate
 * @return array Validated settings
 */
function validateSettings($settings) {
    $defaults = getDefaultSettings();
    $validated = [];

    // First, process all default settings
    foreach ($defaults as $key => $defaultValue) {
        if (isset($settings[$key])) {
            $validated[$key] = validateSettingValue($key, $settings[$key], $defaultValue);
        } else {
            $validated[$key] = $defaultValue;
        }
    }

    // Then, process any additional settings that aren't in defaults
    foreach ($settings as $key => $value) {
        if (!isset($defaults[$key])) {
            $validated[$key] = validateSettingValue($key, $value, $value);
        }
    }

    return $validated;
}

/**
 * Validate individual setting value
 * @param string $key Setting key
 * @param mixed $value Setting value
 * @param mixed $default Default value
 * @return mixed Validated value
 */
function validateSettingValue($key, $value, $default) {
    switch ($key) {
        case 'app_name':
        case 'timezone':
        case 'admin_email':
        case 'site_description':
        case 'theme':
        case 'default_user_role':
        case 'max_file_upload_size':
            return is_string($value) ? trim($value) : $default;
            
        case 'maintenance_mode':
        case 'registration_enabled':
        case 'require_email_verification':
        case 'enable_logging':
        case 'enable_two_factor':
        case 'backup_enabled':
        case 'show_footer_links':
            return is_bool($value) ? $value : (bool)$value;
            
        case 'max_login_attempts':
        case 'session_timeout':
        case 'password_min_length':
        case 'log_retention_days':
            $intValue = (int)$value;
            return $intValue > 0 ? $intValue : $default;
            
        case 'allowed_file_types':
            return is_array($value) ? $value : $default;

        case 'footer_links':
            if (is_array($value)) {
                // Validate each footer link
                $validatedLinks = [];
                foreach ($value as $link) {
                    if (is_array($link) && isset($link['text']) && isset($link['url'])) {
                        $validatedLinks[] = [
                            'text' => trim($link['text']),
                            'url' => trim($link['url'])
                        ];
                    }
                }
                return $validatedLinks;
            }
            return $default;
            
        case 'backup_frequency':
            $validFrequencies = ['daily', 'weekly', 'monthly'];
            return in_array($value, $validFrequencies) ? $value : $default;

        case 'homepage_type':
            $validTypes = ['default', 'custom'];
            return in_array($value, $validTypes) ? $value : $default;

        case 'custom_homepage_html':
            return is_string($value) ? $value : $default;

        default:
            return $value;
    }
}

/**
 * Get a specific setting value
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value
 */
function getSetting($key, $default = null) {
    $settings = loadSettings();
    return $settings[$key] ?? $default;
}

/**
 * Update a specific setting
 * @param string $key Setting key
 * @param mixed $value Setting value
 * @return bool Success status
 */
function updateSetting($key, $value) {
    $settings = loadSettings();
    $settings[$key] = $value;
    return saveSettings($settings);
}

/**
 * Check if maintenance mode is enabled
 * @return bool
 */
function isMaintenanceMode() {
    return getSetting('maintenance_mode', false);
}

/**
 * Check if registration is enabled
 * @return bool
 */
function isRegistrationEnabled() {
    return getSetting('registration_enabled', true);
}

/**
 * Get application name from settings
 * @return string
 */
function getAppName() {
    return getSetting('app_name', 'AI Dashboard');
}

/**
 * Get application timezone
 * @return string
 */
function getAppTimezone() {
    return getSetting('timezone', 'UTC');
}

/**
 * Set application timezone
 */
function setAppTimezone() {
    $timezone = getAppTimezone();
    if ($timezone && $timezone !== 'UTC') {
        try {
            date_default_timezone_set($timezone);
        } catch (Exception $e) {
            logError("Failed to set timezone: " . $e->getMessage(), 'warning');
            date_default_timezone_set('UTC');
        }
    }
}

/**
 * Reset settings to defaults
 * @return bool Success status
 */
function resetSettingsToDefaults() {
    try {
        $defaults = getDefaultSettings();
        $result = saveSettings($defaults);
        
        if ($result) {
            logActivity('settings.reset', 'Settings reset to defaults', [
                'reset_by' => $_SESSION['username'] ?? 'system'
            ]);
        }
        
        return $result;
    } catch (Exception $e) {
        logError("Failed to reset settings: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Export settings as JSON
 * @return string JSON string of settings
 */
function exportSettings() {
    try {
        $settings = loadSettings();
        return json_encode($settings, JSON_PRETTY_PRINT);
    } catch (Exception $e) {
        logError("Failed to export settings: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Import settings from JSON
 * @param string $json JSON string of settings
 * @return bool Success status
 */
function importSettings($json) {
    try {
        $settings = json_decode($json, true);
        
        if (!is_array($settings)) {
            return false;
        }
        
        $result = saveSettings($settings);
        
        if ($result) {
            logActivity('settings.imported', 'Settings imported from JSON', [
                'imported_by' => $_SESSION['username'] ?? 'system',
                'settings_count' => count($settings)
            ]);
        }
        
        return $result;
    } catch (Exception $e) {
        logError("Failed to import settings: " . $e->getMessage(), 'error');
        return false;
    }
}

/**
 * Clear settings cache
 * Forces reload from file on next access
 */
function clearSettingsCache() {
    global $_SETTINGS_CACHE;
    $_SETTINGS_CACHE = null;
}

/**
 * Get cache status for debugging
 * @return array Cache information
 */
function getSettingsCacheInfo() {
    global $_SETTINGS_CACHE;

    if ($_SETTINGS_CACHE === null) {
        return ['cached' => false, 'cache_time' => null];
    }

    return [
        'cached' => true,
        'cache_time' => $_SETTINGS_CACHE['_cache_time'] ?? null,
        'file_mod_time' => file_exists(APP_SETTINGS_FILE) ? filemtime(APP_SETTINGS_FILE) : null
    ];
}

// Set timezone when this file is loaded
setAppTimezone();
?>
