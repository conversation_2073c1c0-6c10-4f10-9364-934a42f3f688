<?php
/**
 * Custom Error Handler
 * Handles uncaught exceptions and PHP errors
 */

require_once __DIR__ . '/logger.php';

/**
 * Custom exception handler
 * @param Throwable $exception
 */
function customExceptionHandler($exception) {
    $errorMessage = sprintf(
        "Uncaught %s: %s in %s:%d",
        get_class($exception),
        $exception->getMessage(),
        $exception->getFile(),
        $exception->getLine()
    );
    
    $context = [
        'exception_class' => get_class($exception),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'code' => $exception->getCode()
    ];
    
    // Log the error
    logError($errorMessage, 'error', $context);
    
    // In production, show a generic error page
    if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
        http_response_code(500);
        include __DIR__ . '/../templates/error_page.php';
        exit();
    } else {
        // In debug mode, show detailed error
        echo "<h1>Uncaught Exception</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($exception->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($exception->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $exception->getLine() . "</p>";
        echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
    }
}

/**
 * Custom error handler
 * @param int $severity
 * @param string $message
 * @param string $file
 * @param int $line
 * @return bool
 */
function customErrorHandler($severity, $message, $file, $line) {
    // Don't handle errors that are suppressed with @
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorTypes = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];
    
    $errorType = $errorTypes[$severity] ?? 'Unknown Error';
    $errorMessage = sprintf("%s: %s in %s:%d", $errorType, $message, $file, $line);
    
    $context = [
        'severity' => $severity,
        'error_type' => $errorType,
        'file' => $file,
        'line' => $line
    ];
    
    // Determine log level based on severity
    $logLevel = 'notice';
    if ($severity & (E_ERROR | E_CORE_ERROR | E_COMPILE_ERROR | E_USER_ERROR | E_RECOVERABLE_ERROR)) {
        $logLevel = 'error';
    } elseif ($severity & (E_WARNING | E_CORE_WARNING | E_COMPILE_WARNING | E_USER_WARNING)) {
        $logLevel = 'warning';
    }
    
    // Log the error
    logError($errorMessage, $logLevel, $context);
    
    // For fatal errors, show error page
    if ($severity & (E_ERROR | E_CORE_ERROR | E_COMPILE_ERROR | E_USER_ERROR)) {
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            http_response_code(500);
            include __DIR__ . '/../templates/error_page.php';
            exit();
        }
    }
    
    // Don't execute PHP's internal error handler
    return true;
}

/**
 * Shutdown handler to catch fatal errors
 */
function shutdownHandler() {
    $error = error_get_last();
    
    if ($error && ($error['type'] & (E_ERROR | E_CORE_ERROR | E_COMPILE_ERROR | E_PARSE))) {
        $errorMessage = sprintf(
            "Fatal Error: %s in %s:%d",
            $error['message'],
            $error['file'],
            $error['line']
        );
        
        $context = [
            'type' => $error['type'],
            'file' => $error['file'],
            'line' => $error['line'],
            'fatal' => true
        ];
        
        logError($errorMessage, 'error', $context);
        
        // Show error page for fatal errors
        if (!defined('DEBUG_MODE') || !DEBUG_MODE) {
            if (!headers_sent()) {
                http_response_code(500);
            }
            include __DIR__ . '/../templates/error_page.php';
        }
    }
}

/**
 * Initialize error handling
 */
function initializeErrorHandling() {
    // Set custom handlers
    set_exception_handler('customExceptionHandler');
    set_error_handler('customErrorHandler');
    register_shutdown_function('shutdownHandler');
    
    // Configure error reporting
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
    } else {
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);
        ini_set('display_errors', 0);
        ini_set('log_errors', 1);
    }
}

/**
 * Log application startup
 */
function logApplicationStart() {
    logActivity('system.application_start', 'Application started', [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ]);
}

// Initialize error handling when this file is included
initializeErrorHandling();
?>
