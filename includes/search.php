<?php
/**
 * Search Functionality
 * Provides search capabilities for users, logs, and other data
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/logger.php';

/**
 * Search users by username, email, or other fields
 * @param string $query Search query
 * @param array $options Search options
 * @return array Filtered users array
 */
function searchUsers($query, $options = []) {
    try {
        $users = loadUsers();
        
        if (empty($query)) {
            return $users;
        }
        
        $query = strtolower(trim($query));
        $fuzzy = $options['fuzzy'] ?? true;
        $fields = $options['fields'] ?? ['username', 'role'];
        
        $results = [];
        
        foreach ($users as $user) {
            $match = false;
            
            foreach ($fields as $field) {
                if (isset($user[$field])) {
                    $fieldValue = strtolower($user[$field]);
                    
                    // Exact match
                    if (strpos($fieldValue, $query) !== false) {
                        $match = true;
                        break;
                    }
                    
                    // Fuzzy match if enabled
                    if ($fuzzy && fuzzyMatch($query, $fieldValue)) {
                        $match = true;
                        break;
                    }
                }
            }
            
            if ($match) {
                $results[] = $user;
            }
        }
        
        return $results;
    } catch (Exception $e) {
        logError("Failed to search users: " . $e->getMessage());
        return [];
    }
}

/**
 * Search activity logs
 * @param string $query Search query
 * @param array $options Search options
 * @return array Filtered logs array
 */
function searchActivityLogs($query, $options = []) {
    try {
        $limit = $options['limit'] ?? 100;
        $logs = getActivityLogs($limit);
        
        if (empty($query)) {
            return $logs;
        }
        
        $query = strtolower(trim($query));
        $fuzzy = $options['fuzzy'] ?? true;
        $fields = $options['fields'] ?? ['action', 'description', 'username'];
        
        $results = [];
        
        foreach ($logs as $log) {
            $match = false;
            
            foreach ($fields as $field) {
                if (isset($log[$field])) {
                    $fieldValue = strtolower($log[$field]);
                    
                    // Exact match
                    if (strpos($fieldValue, $query) !== false) {
                        $match = true;
                        break;
                    }
                    
                    // Fuzzy match if enabled
                    if ($fuzzy && fuzzyMatch($query, $fieldValue)) {
                        $match = true;
                        break;
                    }
                }
            }
            
            // Search in data field if it's an array
            if (!$match && isset($log['data']) && is_array($log['data'])) {
                $dataString = strtolower(json_encode($log['data']));
                if (strpos($dataString, $query) !== false) {
                    $match = true;
                }
            }
            
            if ($match) {
                $results[] = $log;
            }
        }
        
        return $results;
    } catch (Exception $e) {
        logError("Failed to search activity logs: " . $e->getMessage());
        return [];
    }
}

/**
 * Search error logs
 * @param string $query Search query
 * @param array $options Search options
 * @return array Filtered logs array
 */
function searchErrorLogs($query, $options = []) {
    try {
        $limit = $options['limit'] ?? 100;
        $logs = getErrorLogs($limit);
        
        if (empty($query)) {
            return $logs;
        }
        
        $query = strtolower(trim($query));
        $fuzzy = $options['fuzzy'] ?? true;
        $fields = $options['fields'] ?? ['message', 'level', 'username'];
        
        $results = [];
        
        foreach ($logs as $log) {
            $match = false;
            
            foreach ($fields as $field) {
                if (isset($log[$field])) {
                    $fieldValue = strtolower($log[$field]);
                    
                    // Exact match
                    if (strpos($fieldValue, $query) !== false) {
                        $match = true;
                        break;
                    }
                    
                    // Fuzzy match if enabled
                    if ($fuzzy && fuzzyMatch($query, $fieldValue)) {
                        $match = true;
                        break;
                    }
                }
            }
            
            // Search in context field if it's an array
            if (!$match && isset($log['context']) && is_array($log['context'])) {
                $contextString = strtolower(json_encode($log['context']));
                if (strpos($contextString, $query) !== false) {
                    $match = true;
                }
            }
            
            if ($match) {
                $results[] = $log;
            }
        }
        
        return $results;
    } catch (Exception $e) {
        logError("Failed to search error logs: " . $e->getMessage());
        return [];
    }
}

/**
 * Fuzzy string matching using Levenshtein distance
 * @param string $needle Search term
 * @param string $haystack Text to search in
 * @param int $threshold Maximum distance threshold
 * @return bool True if fuzzy match found
 */
function fuzzyMatch($needle, $haystack, $threshold = 2) {
    $needleLen = strlen($needle);
    $haystackLen = strlen($haystack);
    
    // If needle is too short, require exact substring match
    if ($needleLen < 3) {
        return strpos($haystack, $needle) !== false;
    }
    
    // Check for substring match first (faster)
    if (strpos($haystack, $needle) !== false) {
        return true;
    }
    
    // For longer strings, use sliding window approach
    for ($i = 0; $i <= $haystackLen - $needleLen; $i++) {
        $substring = substr($haystack, $i, $needleLen);
        $distance = levenshtein($needle, $substring);
        
        if ($distance <= $threshold) {
            return true;
        }
    }
    
    return false;
}

/**
 * Highlight search terms in text
 * @param string $text Text to highlight
 * @param string $query Search query
 * @param string $highlightClass CSS class for highlighting
 * @return string Text with highlighted search terms
 */
function highlightSearchTerms($text, $query, $highlightClass = 'bg-yellow-200 dark:bg-yellow-800') {
    if (empty($query) || empty($text)) {
        return htmlspecialchars($text);
    }
    
    $query = trim($query);
    $escapedText = htmlspecialchars($text);
    
    // Split query into words
    $words = preg_split('/\s+/', $query);
    
    foreach ($words as $word) {
        if (strlen($word) >= 2) {
            $pattern = '/(' . preg_quote($word, '/') . ')/i';
            $replacement = '<span class="' . $highlightClass . '">$1</span>';
            $escapedText = preg_replace($pattern, $replacement, $escapedText);
        }
    }
    
    return $escapedText;
}

/**
 * Get search suggestions based on query
 * @param string $query Partial search query
 * @param string $type Type of search (users, logs)
 * @param int $limit Maximum suggestions to return
 * @return array Array of suggestions
 */
function getSearchSuggestions($query, $type = 'users', $limit = 5) {
    try {
        $suggestions = [];
        $query = strtolower(trim($query));
        
        if (strlen($query) < 2) {
            return $suggestions;
        }
        
        switch ($type) {
            case 'users':
                $users = loadUsers();
                foreach ($users as $user) {
                    if (strpos(strtolower($user['username']), $query) === 0) {
                        $suggestions[] = $user['username'];
                    }
                    if (count($suggestions) >= $limit) break;
                }
                break;
                
            case 'logs':
                $logs = getActivityLogs(50);
                $actions = [];
                foreach ($logs as $log) {
                    $action = $log['action'] ?? '';
                    if (!in_array($action, $actions) && strpos(strtolower($action), $query) === 0) {
                        $actions[] = $action;
                        $suggestions[] = $action;
                    }
                    if (count($suggestions) >= $limit) break;
                }
                break;
        }
        
        return array_unique($suggestions);
    } catch (Exception $e) {
        logError("Failed to get search suggestions: " . $e->getMessage());
        return [];
    }
}

/**
 * Generate search form HTML
 * @param string $currentQuery Current search query
 * @param string $placeholder Placeholder text
 * @param string $action Form action URL
 * @param array $options Additional options
 * @return string HTML for search form
 */
function renderSearchForm($currentQuery = '', $placeholder = 'Search...', $action = '', $options = []) {
    $inputName = $options['input_name'] ?? 'search';
    $showClear = $options['show_clear'] ?? true;
    $autoComplete = $options['autocomplete'] ?? true;
    
    $html = '<div class="relative">';
    $html .= '<form method="GET" action="' . htmlspecialchars($action) . '" class="flex items-center">';
    
    // Preserve other GET parameters
    foreach ($_GET as $key => $value) {
        if ($key !== $inputName) {
            $html .= '<input type="hidden" name="' . htmlspecialchars($key) . '" value="' . htmlspecialchars($value) . '">';
        }
    }
    
    $html .= '<div class="relative flex-1">';
    $html .= '<input type="text" name="' . $inputName . '" value="' . htmlspecialchars($currentQuery) . '" ';
    $html .= 'placeholder="' . htmlspecialchars($placeholder) . '" ';
    $html .= 'class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg ';
    $html .= 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white ';
    $html .= 'placeholder-gray-500 dark:placeholder-gray-400 ';
    $html .= 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"';
    
    if ($autoComplete) {
        $html .= ' autocomplete="off"';
    }
    
    $html .= '>';
    
    // Search icon
    $html .= '<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">';
    $html .= '<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
    $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>';
    $html .= '</svg>';
    $html .= '</div>';
    
    // Clear button
    if ($showClear && !empty($currentQuery)) {
        $html .= '<button type="button" onclick="clearSearch()" class="absolute inset-y-0 right-0 pr-3 flex items-center">';
        $html .= '<svg class="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
        $html .= '</svg>';
        $html .= '</button>';
    }
    
    $html .= '</div>';
    $html .= '<button type="submit" class="ml-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition duration-200">';
    $html .= 'Search';
    $html .= '</button>';
    $html .= '</form>';
    $html .= '</div>';
    
    // JavaScript for clear function
    $html .= '<script>';
    $html .= 'function clearSearch() {';
    $html .= '  const input = document.querySelector(\'input[name="' . $inputName . '"]\');';
    $html .= '  if (input) {';
    $html .= '    input.value = "";';
    $html .= '    input.form.submit();';
    $html .= '  }';
    $html .= '}';
    $html .= '</script>';
    
    return $html;
}

/**
 * Log search activity
 * @param string $query Search query
 * @param string $type Search type
 * @param int $resultCount Number of results found
 */
function logSearchActivity($query, $type, $resultCount) {
    logActivity('search.performed', "Search performed in {$type}", [
        'query' => $query,
        'type' => $type,
        'result_count' => $resultCount,
        'query_length' => strlen($query)
    ]);
}
?>
