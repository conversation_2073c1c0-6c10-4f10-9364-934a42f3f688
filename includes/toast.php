<?php
/**
 * Toast Notification System
 * Modern replacement for flash messages with better UX
 */

require_once __DIR__ . '/../config.php';

/**
 * Add a toast notification to the queue
 * @param string $message Toast message
 * @param string $type Toast type (success, error, warning, info)
 * @param int $duration Duration in milliseconds (0 = persistent)
 * @param array $options Additional options
 */
function addToast($message, $type = 'info', $duration = 5000, $options = []) {
    if (!isset($_SESSION['toasts'])) {
        $_SESSION['toasts'] = [];
    }
    
    $toast = [
        'id' => uniqid('toast_'),
        'message' => $message,
        'type' => $type,
        'duration' => $duration,
        'timestamp' => time(),
        'options' => array_merge([
            'closable' => true,
            'icon' => true,
            'position' => 'top-right'
        ], $options)
    ];
    
    $_SESSION['toasts'][] = $toast;
}

/**
 * Get all pending toasts and clear the queue
 * @return array Array of toast notifications
 */
function getToasts() {
    $toasts = $_SESSION['toasts'] ?? [];
    $_SESSION['toasts'] = []; // Clear after getting
    return $toasts;
}

/**
 * Add a success toast
 * @param string $message Success message
 * @param int $duration Duration in milliseconds
 */
function toastSuccess($message, $duration = 4000) {
    addToast($message, 'success', $duration);
}

/**
 * Add an error toast
 * @param string $message Error message
 * @param int $duration Duration in milliseconds (0 = persistent)
 */
function toastError($message, $duration = 0) {
    addToast($message, 'error', $duration);
}

/**
 * Add a warning toast
 * @param string $message Warning message
 * @param int $duration Duration in milliseconds
 */
function toastWarning($message, $duration = 6000) {
    addToast($message, 'warning', $duration);
}

/**
 * Add an info toast
 * @param string $message Info message
 * @param int $duration Duration in milliseconds
 */
function toastInfo($message, $duration = 5000) {
    addToast($message, 'info', $duration);
}

/**
 * Convert flash message to toast (for backward compatibility)
 * @param string $message Flash message
 * @param string $type Flash message type
 */
function flashToToast($message, $type) {
    $durations = [
        'success' => 4000,
        'error' => 0, // Persistent for errors
        'warning' => 6000,
        'info' => 5000
    ];
    
    addToast($message, $type, $durations[$type] ?? 5000);
}

/**
 * Get toast icon based on type
 * @param string $type Toast type
 * @return string SVG icon HTML
 */
function getToastIcon($type) {
    $icons = [
        'success' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        'error' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        'warning' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        'info' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    ];
    
    return $icons[$type] ?? $icons['info'];
}

/**
 * Get toast CSS classes based on type
 * @param string $type Toast type
 * @return array CSS classes for toast components
 */
function getToastClasses($type) {
    $classes = [
        'success' => [
            'container' => 'bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700',
            'icon' => 'text-green-400 dark:text-green-300',
            'text' => 'text-green-800 dark:text-green-200',
            'close' => 'text-green-500 dark:text-green-400 hover:text-green-600 dark:hover:text-green-300'
        ],
        'error' => [
            'container' => 'bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700',
            'icon' => 'text-red-400 dark:text-red-300',
            'text' => 'text-red-800 dark:text-red-200',
            'close' => 'text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300'
        ],
        'warning' => [
            'container' => 'bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700',
            'icon' => 'text-yellow-400 dark:text-yellow-300',
            'text' => 'text-yellow-800 dark:text-yellow-200',
            'close' => 'text-yellow-500 dark:text-yellow-400 hover:text-yellow-600 dark:hover:text-yellow-300'
        ],
        'info' => [
            'container' => 'bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700',
            'icon' => 'text-blue-400 dark:text-blue-300',
            'text' => 'text-blue-800 dark:text-blue-200',
            'close' => 'text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300'
        ]
    ];
    
    return $classes[$type] ?? $classes['info'];
}

/**
 * Render toast container HTML
 * @return string HTML for toast container
 */
function renderToastContainer() {
    return '
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
        <!-- Toasts will be dynamically inserted here -->
    </div>';
}

/**
 * Generate JavaScript for toast functionality
 * @return string JavaScript code for toast system
 */
function getToastJavaScript() {
    $toasts = getToasts();
    $toastsJson = json_encode($toasts);
    
    return "
    <script>
    // Toast system initialization
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize toast container if it doesn't exist
        if (!document.getElementById('toastContainer')) {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full';
            document.body.appendChild(container);
        }
        
        // Load pending toasts
        const pendingToasts = {$toastsJson};
        pendingToasts.forEach(function(toast) {
            showToast(toast.message, toast.type, toast.duration, toast.options);
        });
    });
    
    // Show toast function
    function showToast(message, type = 'info', duration = 5000, options = {}) {
        const container = document.getElementById('toastContainer');
        if (!container) return;
        
        const toastId = 'toast_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const toast = document.createElement('div');
        
        const typeClasses = {
            success: {
                container: 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700',
                icon: 'text-green-400 dark:text-green-300',
                text: 'text-green-800 dark:text-green-200',
                close: 'text-green-500 dark:text-green-400 hover:text-green-600 dark:hover:text-green-300'
            },
            error: {
                container: 'bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700',
                icon: 'text-red-400 dark:text-red-300',
                text: 'text-red-800 dark:text-red-200',
                close: 'text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300'
            },
            warning: {
                container: 'bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700',
                icon: 'text-yellow-400 dark:text-yellow-300',
                text: 'text-yellow-800 dark:text-yellow-200',
                close: 'text-yellow-500 dark:text-yellow-400 hover:text-yellow-600 dark:hover:text-yellow-300'
            },
            info: {
                container: 'bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700',
                icon: 'text-blue-400 dark:text-blue-300',
                text: 'text-blue-800 dark:text-blue-200',
                close: 'text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300'
            }
        };
        
        const classes = typeClasses[type] || typeClasses.info;
        
        toast.id = toastId;
        toast.className = `border rounded-lg p-4 shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0 \${classes.container}`;
        
        const icons = {
            success: '<path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>',
            error: '<path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>',
            warning: '<path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>',
            info: '<path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clip-rule=\"evenodd\"></path>'
        };
        
        toast.innerHTML = `
            <div class=\"flex items-start\">
                <div class=\"flex-shrink-0\">
                    <svg class=\"w-5 h-5 \${classes.icon}\" fill=\"currentColor\" viewBox=\"0 0 20 20\">
                        \${icons[type] || icons.info}
                    </svg>
                </div>
                <div class=\"ml-3 flex-1\">
                    <p class=\"text-sm font-medium \${classes.text}\">\${message}</p>
                </div>
                \${options.closable !== false ? `
                <div class=\"ml-4 flex-shrink-0\">
                    <button class=\"inline-flex \${classes.close} focus:outline-none focus:ring-2 focus:ring-offset-2\" onclick=\"hideToast('\${toastId}')\">
                        <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">
                            <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>
                        </svg>
                    </button>
                </div>` : ''}
            </div>
        `;
        
        container.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
            toast.classList.add('translate-x-0', 'opacity-100');
        }, 10);
        
        // Auto-hide if duration is set
        if (duration > 0) {
            setTimeout(() => {
                hideToast(toastId);
            }, duration);
        }
    }
    
    // Hide toast function
    function hideToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }
    
    // Global toast functions
    window.toastSuccess = function(message, duration = 4000) {
        showToast(message, 'success', duration);
    };
    
    window.toastError = function(message, duration = 0) {
        showToast(message, 'error', duration);
    };
    
    window.toastWarning = function(message, duration = 6000) {
        showToast(message, 'warning', duration);
    };
    
    window.toastInfo = function(message, duration = 5000) {
        showToast(message, 'info', duration);
    };
    </script>";
}
?>
