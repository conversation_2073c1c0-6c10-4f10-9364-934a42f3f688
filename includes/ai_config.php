<?php
/**
 * AI Configuration and Model Management
 * Provides centralized AI model configuration and API management
 */

/**
 * Available AI Models
 * Organized by provider for easy management
 */
function getAvailableAIModels() {
    return [
        // Claude Models (Anthropic)
        'claude-3-opus-20240229'     => 'Claude 3 Opus (Anthropic)',
        'claude-3-sonnet-20240229'   => 'Claude 3 Sonnet (Anthropic)',
        'claude-3-haiku-20240307'    => '<PERSON> 3 Haiku (Anthropic)',
        'claude-3-5-sonnet-20241022' => 'Claude 3.5 Sonnet (2024-10-22) (Anthropic)',
        'claude-3-5-haiku-20241022'  => '<PERSON> 3.5 Haiku (2024-10-22) (Anthropic)',
        'claude-3-7-sonnet-20250224' => 'Claude 3.7 Sonnet (2025-02-24) (Anthropic)',
        'claude-opus-4-20250514'     => '<PERSON> 4 (2025-05-14) (Anthropic)',
        'claude-sonnet-4-20250514'   => '<PERSON> Sonnet 4 (2025-05-14) (Anthropic)',

        // Google Gemini Models
        'gemini-1.5-flash'               => 'Gemini 1.5 Flash (Google)',
        'gemini-1.5-pro'                 => 'Gemini 1.5 Pro (Google)',
        'gemini-2.0-flash'               => 'Gemini 2.0 Flash (Google)',
        'gemini-2.0-flash-lite'          => 'Gemini 2.0 Flash Lite (Google)',
        'gemini-2.5-flash-preview-05-20' => 'Gemini 2.5 Flash Preview (2025-05-20) (Google)',
        'gemini-2.5-pro-preview-05-06'   => 'Gemini 2.5 Pro Preview (2025-05-06) (Google)',

        // OpenAI Models
        'gpt-3.5-turbo' => 'GPT-3.5 Turbo (OpenAI)',
        'gpt-4-turbo'   => 'GPT-4 Turbo (OpenAI)',
        'gpt-4o'        => 'GPT-4o (OpenAI)',
        'gpt-4o-mini'   => 'GPT-4o Mini (OpenAI)',
        'gpt-4.1'       => 'GPT-4.1 (Experimental) (OpenAI)',
        'gpt-4.1-mini'  => 'GPT-4.1 Mini (Experimental) (OpenAI)',
        'gpt-4.1-nano'  => 'GPT-4.1 Nano (Experimental) (OpenAI)',
        'o1'            => 'o1 (Experimental) (OpenAI)',
        'o1-mini'       => 'o1 Mini (Experimental) (OpenAI)',
    ];
}

/**
 * Get AI providers and their models
 */
function getAIProviders() {
    return [
        'anthropic' => [
            'name' => 'Anthropic (Claude)',
            'models' => [
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229',
                'claude-3-haiku-20240307',
                'claude-3-5-sonnet-20241022',
                'claude-3-5-haiku-20241022',
                'claude-3-7-sonnet-20250224',
                'claude-opus-4-20250514',
                'claude-sonnet-4-20250514'
            ]
        ],
        'google' => [
            'name' => 'Google (Gemini)',
            'models' => [
                'gemini-1.5-flash',
                'gemini-1.5-pro',
                'gemini-2.0-flash',
                'gemini-2.0-flash-lite',
                'gemini-2.5-flash-preview-05-20',
                'gemini-2.5-pro-preview-05-06'
            ]
        ],
        'openai' => [
            'name' => 'OpenAI (GPT)',
            'models' => [
                'gpt-3.5-turbo',
                'gpt-4-turbo',
                'gpt-4o',
                'gpt-4o-mini',
                'gpt-4.1',
                'gpt-4.1-mini',
                'gpt-4.1-nano',
                'o1',
                'o1-mini'
            ]
        ]
    ];
}

/**
 * Get provider for a specific model
 */
function getModelProvider($model) {
    $providers = getAIProviders();
    
    foreach ($providers as $providerId => $provider) {
        if (in_array($model, $provider['models'])) {
            return $providerId;
        }
    }
    
    return null;
}

/**
 * Get user's AI settings
 */
function getUserAISettings($userId) {
    $users = loadUsers();
    
    foreach ($users as $user) {
        if ($user['id'] === $userId) {
            return [
                'active_model' => $user['ai_active_model'] ?? null,
                'anthropic_api_key' => $user['ai_anthropic_key'] ?? '',
                'google_api_key' => $user['ai_google_key'] ?? '',
                'openai_api_key' => $user['ai_openai_key'] ?? ''
            ];
        }
    }
    
    return [
        'active_model' => null,
        'anthropic_api_key' => '',
        'google_api_key' => '',
        'openai_api_key' => ''
    ];
}

/**
 * Update user's AI settings
 */
function updateUserAISettings($userId, $settings) {
    $users = loadUsers();
    
    foreach ($users as &$user) {
        if ($user['id'] === $userId) {
            $user['ai_active_model'] = $settings['active_model'] ?? null;
            $user['ai_anthropic_key'] = $settings['anthropic_api_key'] ?? '';
            $user['ai_google_key'] = $settings['google_api_key'] ?? '';
            $user['ai_openai_key'] = $settings['openai_api_key'] ?? '';
            break;
        }
    }
    
    return saveUsers($users);
}

/**
 * Check if user has AI configured
 */
function userHasAIConfigured($userId) {
    $settings = getUserAISettings($userId);
    
    if (!$settings['active_model']) {
        return false;
    }
    
    $provider = getModelProvider($settings['active_model']);
    
    switch ($provider) {
        case 'anthropic':
            return !empty($settings['anthropic_api_key']);
        case 'google':
            return !empty($settings['google_api_key']);
        case 'openai':
            return !empty($settings['openai_api_key']);
        default:
            return false;
    }
}

/**
 * Get active AI configuration for user
 */
function getActiveAIConfig($userId) {
    $settings = getUserAISettings($userId);
    
    if (!$settings['active_model']) {
        return null;
    }
    
    $provider = getModelProvider($settings['active_model']);
    $apiKey = '';
    
    switch ($provider) {
        case 'anthropic':
            $apiKey = $settings['anthropic_api_key'];
            break;
        case 'google':
            $apiKey = $settings['google_api_key'];
            break;
        case 'openai':
            $apiKey = $settings['openai_api_key'];
            break;
    }
    
    if (empty($apiKey)) {
        return null;
    }
    
    return [
        'model' => $settings['active_model'],
        'provider' => $provider,
        'api_key' => $apiKey,
        'display_name' => getAvailableAIModels()[$settings['active_model']] ?? $settings['active_model']
    ];
}
?>
