<?php
/**
 * Centralized Logging System
 * Handles error logging and activity/audit logging
 */

require_once __DIR__ . '/../config.php';

/**
 * Log an error to the error log file
 * @param string $message Error message
 * @param string $level Error level (error, warning, notice)
 * @param array $context Additional context data
 * @return bool Success status
 */
function logError($message, $level = 'error', $context = []) {
    try {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'user_id' => $_SESSION['user_id'] ?? null,
            'username' => $_SESSION['username'] ?? 'anonymous',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
        ];
        
        return writeLogEntry(ERROR_LOG_FILE, $logEntry);
    } catch (Exception $e) {
        // Fallback to PHP error log if our logging fails
        error_log("Logger failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Log user activity to the activity log file
 * @param string $action Action performed
 * @param string $description Detailed description
 * @param array $data Additional data
 * @return bool Success status
 */
function logActivity($action, $description = '', $data = []) {
    try {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'description' => $description,
            'data' => $data,
            'user_id' => $_SESSION['user_id'] ?? null,
            'username' => $_SESSION['username'] ?? 'anonymous',
            'user_role' => $_SESSION['user_role'] ?? 'guest',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'session_id' => session_id()
        ];
        
        return writeLogEntry(ACTIVITY_LOG_FILE, $logEntry);
    } catch (Exception $e) {
        logError("Activity logging failed: " . $e->getMessage(), 'error', ['original_action' => $action]);
        return false;
    }
}

/**
 * Write a log entry to a specific log file
 * @param string $logFile Path to log file
 * @param array $entry Log entry data
 * @return bool Success status
 */
function writeLogEntry($logFile, $entry) {
    try {
        // Read existing logs
        $logs = [];
        if (file_exists($logFile)) {
            $content = file_get_contents($logFile);
            $logs = json_decode($content, true) ?: [];
        }
        
        // Add new entry
        $logs[] = $entry;
        
        // Keep only last 1000 entries to prevent file from growing too large
        if (count($logs) > 1000) {
            $logs = array_slice($logs, -1000);
        }
        
        // Write back to file
        return file_put_contents($logFile, json_encode($logs, JSON_PRETTY_PRINT)) !== false;
    } catch (Exception $e) {
        error_log("Failed to write log entry: " . $e->getMessage());
        return false;
    }
}

/**
 * Get recent log entries
 * @param string $logFile Path to log file
 * @param int $limit Number of entries to return
 * @param int $offset Offset for pagination
 * @return array Log entries
 */
function getLogEntries($logFile, $limit = 50, $offset = 0) {
    try {
        if (!file_exists($logFile)) {
            return [];
        }
        
        $content = file_get_contents($logFile);
        $logs = json_decode($content, true) ?: [];
        
        // Sort by timestamp (newest first)
        usort($logs, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        return array_slice($logs, $offset, $limit);
    } catch (Exception $e) {
        logError("Failed to read log entries: " . $e->getMessage());
        return [];
    }
}

/**
 * Get error log entries
 * @param int $limit Number of entries to return
 * @param int $offset Offset for pagination
 * @return array Error log entries
 */
function getErrorLogs($limit = 50, $offset = 0) {
    return getLogEntries(ERROR_LOG_FILE, $limit, $offset);
}

/**
 * Get activity log entries
 * @param int $limit Number of entries to return
 * @param int $offset Offset for pagination
 * @return array Activity log entries
 */
function getActivityLogs($limit = 50, $offset = 0) {
    return getLogEntries(ACTIVITY_LOG_FILE, $limit, $offset);
}

/**
 * Get activity logs for a specific user
 * @param string $userId User ID to filter by
 * @param int $limit Number of entries to return
 * @return array User activity logs
 */
function getUserActivityLogs($userId, $limit = 20) {
    try {
        $allLogs = getActivityLogs(500); // Get more entries to filter
        $userLogs = array_filter($allLogs, function($log) use ($userId) {
            return $log['user_id'] === $userId;
        });
        
        return array_slice($userLogs, 0, $limit);
    } catch (Exception $e) {
        logError("Failed to get user activity logs: " . $e->getMessage());
        return [];
    }
}

/**
 * Clear old log entries (older than specified days)
 * @param int $days Number of days to keep
 * @return bool Success status
 */
function clearOldLogs($days = 30) {
    try {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        $logFiles = [ERROR_LOG_FILE, ACTIVITY_LOG_FILE];
        
        foreach ($logFiles as $logFile) {
            if (!file_exists($logFile)) continue;
            
            $content = file_get_contents($logFile);
            $logs = json_decode($content, true) ?: [];
            
            // Filter out old entries
            $filteredLogs = array_filter($logs, function($log) use ($cutoffDate) {
                return $log['timestamp'] >= $cutoffDate;
            });
            
            // Reindex array
            $filteredLogs = array_values($filteredLogs);
            
            file_put_contents($logFile, json_encode($filteredLogs, JSON_PRETTY_PRINT));
        }
        
        logActivity('system.logs_cleared', "Cleared logs older than {$days} days");
        return true;
    } catch (Exception $e) {
        logError("Failed to clear old logs: " . $e->getMessage());
        return false;
    }
}

/**
 * Get log statistics
 * @return array Log statistics
 */
function getLogStatistics() {
    try {
        $errorLogs = getErrorLogs(1000);
        $activityLogs = getActivityLogs(1000);
        
        $stats = [
            'total_errors' => count($errorLogs),
            'total_activities' => count($activityLogs),
            'recent_errors' => count(array_filter($errorLogs, function($log) {
                return strtotime($log['timestamp']) > strtotime('-24 hours');
            })),
            'recent_activities' => count(array_filter($activityLogs, function($log) {
                return strtotime($log['timestamp']) > strtotime('-24 hours');
            })),
            'unique_users_today' => count(array_unique(array_column(
                array_filter($activityLogs, function($log) {
                    return strtotime($log['timestamp']) > strtotime('-24 hours');
                }), 'user_id'
            ))),
            'last_error' => !empty($errorLogs) ? $errorLogs[0]['timestamp'] : null,
            'last_activity' => !empty($activityLogs) ? $activityLogs[0]['timestamp'] : null
        ];
        
        return $stats;
    } catch (Exception $e) {
        logError("Failed to get log statistics: " . $e->getMessage());
        return [];
    }
}

/**
 * Log security event (failed login, suspicious activity, etc.)
 * @param string $event Security event type
 * @param string $description Event description
 * @param array $data Additional data
 * @return bool Success status
 */
function logSecurityEvent($event, $description, $data = []) {
    $securityData = array_merge($data, [
        'security_event' => true,
        'event_type' => $event,
        'severity' => 'high'
    ]);
    
    // Log as both error and activity
    logError("Security Event: {$event} - {$description}", 'warning', $securityData);
    return logActivity("security.{$event}", $description, $securityData);
}
?>
