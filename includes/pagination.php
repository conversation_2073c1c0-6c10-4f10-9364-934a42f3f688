<?php
/**
 * Pagination Helper Functions
 * Provides reusable pagination functionality for any dataset
 */

require_once __DIR__ . '/../config.php';

/**
 * Paginate an array of data
 * @param array $data Data to paginate
 * @param int $page Current page number (1-based)
 * @param int $perPage Items per page
 * @return array Paginated data and pagination info
 */
function paginateData($data, $page = 1, $perPage = 20) {
    $totalItems = count($data);
    $totalPages = ceil($totalItems / $perPage);
    $page = max(1, min($page, $totalPages)); // Ensure page is within bounds
    
    $offset = ($page - 1) * $perPage;
    $paginatedData = array_slice($data, $offset, $perPage);
    
    return [
        'data' => $paginatedData,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $perPage,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'has_previous' => $page > 1,
            'has_next' => $page < $totalPages,
            'previous_page' => $page > 1 ? $page - 1 : null,
            'next_page' => $page < $totalPages ? $page + 1 : null,
            'start_item' => $totalItems > 0 ? $offset + 1 : 0,
            'end_item' => min($offset + $perPage, $totalItems)
        ]
    ];
}

/**
 * Generate pagination HTML
 * @param array $pagination Pagination info from paginateData()
 * @param string $baseUrl Base URL for pagination links
 * @param array $params Additional URL parameters to preserve
 * @param array $options Display options
 * @return string HTML for pagination
 */
function renderPagination($pagination, $baseUrl = '', $params = [], $options = []) {
    if ($pagination['total_pages'] <= 1) {
        return '';
    }
    
    $showNumbers = $options['show_numbers'] ?? true;
    $showInfo = $options['show_info'] ?? true;
    $maxLinks = $options['max_links'] ?? 7;
    $size = $options['size'] ?? 'normal'; // normal, small, large
    
    $currentPage = $pagination['current_page'];
    $totalPages = $pagination['total_pages'];
    
    // Build base URL with parameters
    $urlParams = array_merge($_GET, $params);
    unset($urlParams['page']); // Remove page param, we'll add it manually
    
    $queryString = !empty($urlParams) ? '&' . http_build_query($urlParams) : '';
    $baseUrl = $baseUrl ?: $_SERVER['PHP_SELF'];
    
    $html = '<div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">';
    
    // Info section
    if ($showInfo) {
        $html .= '<div class="text-sm text-gray-700 dark:text-gray-300">';
        $html .= 'Showing ' . $pagination['start_item'] . ' to ' . $pagination['end_item'];
        $html .= ' of ' . $pagination['total_items'] . ' results';
        $html .= '</div>';
    }
    
    // Navigation section
    $html .= '<div class="flex items-center space-x-1">';
    
    // Previous button
    if ($pagination['has_previous']) {
        $prevUrl = $baseUrl . '?page=' . $pagination['previous_page'] . $queryString;
        $html .= '<a href="' . htmlspecialchars($prevUrl) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-l-md hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300">';
        $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
        $html .= '</svg>';
        $html .= '</a>';
    } else {
        $html .= '<span class="px-3 py-2 text-sm font-medium text-gray-300 dark:text-gray-600 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-l-md cursor-not-allowed">';
        $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
        $html .= '</svg>';
        $html .= '</span>';
    }
    
    // Page numbers
    if ($showNumbers) {
        $startPage = max(1, $currentPage - floor($maxLinks / 2));
        $endPage = min($totalPages, $startPage + $maxLinks - 1);
        
        // Adjust start if we're near the end
        if ($endPage - $startPage + 1 < $maxLinks) {
            $startPage = max(1, $endPage - $maxLinks + 1);
        }
        
        // First page + ellipsis
        if ($startPage > 1) {
            $firstUrl = $baseUrl . '?page=1' . $queryString;
            $html .= '<a href="' . htmlspecialchars($firstUrl) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300">1</a>';
            
            if ($startPage > 2) {
                $html .= '<span class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-600">...</span>';
            }
        }
        
        // Page numbers
        for ($i = $startPage; $i <= $endPage; $i++) {
            if ($i == $currentPage) {
                $html .= '<span class="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 dark:bg-blue-900 border-t border-b border-blue-300 dark:border-blue-600">' . $i . '</span>';
            } else {
                $pageUrl = $baseUrl . '?page=' . $i . $queryString;
                $html .= '<a href="' . htmlspecialchars($pageUrl) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300">' . $i . '</a>';
            }
        }
        
        // Last page + ellipsis
        if ($endPage < $totalPages) {
            if ($endPage < $totalPages - 1) {
                $html .= '<span class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-600">...</span>';
            }
            
            $lastUrl = $baseUrl . '?page=' . $totalPages . $queryString;
            $html .= '<a href="' . htmlspecialchars($lastUrl) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border-t border-b border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300">' . $totalPages . '</a>';
        }
    }
    
    // Next button
    if ($pagination['has_next']) {
        $nextUrl = $baseUrl . '?page=' . $pagination['next_page'] . $queryString;
        $html .= '<a href="' . htmlspecialchars($nextUrl) . '" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-r-md hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300">';
        $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
        $html .= '</svg>';
        $html .= '</a>';
    } else {
        $html .= '<span class="px-3 py-2 text-sm font-medium text-gray-300 dark:text-gray-600 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-r-md cursor-not-allowed">';
        $html .= '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
        $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
        $html .= '</svg>';
        $html .= '</span>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate simple pagination (just prev/next)
 * @param array $pagination Pagination info
 * @param string $baseUrl Base URL
 * @param array $params URL parameters
 * @return string HTML for simple pagination
 */
function renderSimplePagination($pagination, $baseUrl = '', $params = []) {
    if ($pagination['total_pages'] <= 1) {
        return '';
    }
    
    $urlParams = array_merge($_GET, $params);
    unset($urlParams['page']);
    
    $queryString = !empty($urlParams) ? '&' . http_build_query($urlParams) : '';
    $baseUrl = $baseUrl ?: $_SERVER['PHP_SELF'];
    
    $html = '<div class="flex justify-between items-center">';
    
    // Previous button
    if ($pagination['has_previous']) {
        $prevUrl = $baseUrl . '?page=' . $pagination['previous_page'] . $queryString;
        $html .= '<a href="' . htmlspecialchars($prevUrl) . '" class="btn-secondary">← Previous</a>';
    } else {
        $html .= '<span class="btn-secondary opacity-50 cursor-not-allowed">← Previous</span>';
    }
    
    // Page info
    $html .= '<span class="text-sm text-gray-600 dark:text-gray-400">';
    $html .= 'Page ' . $pagination['current_page'] . ' of ' . $pagination['total_pages'];
    $html .= '</span>';
    
    // Next button
    if ($pagination['has_next']) {
        $nextUrl = $baseUrl . '?page=' . $pagination['next_page'] . $queryString;
        $html .= '<a href="' . htmlspecialchars($nextUrl) . '" class="btn-secondary">Next →</a>';
    } else {
        $html .= '<span class="btn-secondary opacity-50 cursor-not-allowed">Next →</span>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Generate per-page selector
 * @param int $currentPerPage Current items per page
 * @param array $options Available per-page options
 * @param string $baseUrl Base URL
 * @param array $params URL parameters
 * @return string HTML for per-page selector
 */
function renderPerPageSelector($currentPerPage = 20, $options = [10, 20, 50, 100], $baseUrl = '', $params = []) {
    $urlParams = array_merge($_GET, $params);
    unset($urlParams['per_page'], $urlParams['page']);
    
    $queryString = !empty($urlParams) ? '&' . http_build_query($urlParams) : '';
    $baseUrl = $baseUrl ?: $_SERVER['PHP_SELF'];
    
    $html = '<div class="flex items-center space-x-2">';
    $html .= '<label class="text-sm text-gray-700 dark:text-gray-300">Show:</label>';
    $html .= '<select onchange="changePerPage(this.value)" class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">';
    
    foreach ($options as $option) {
        $selected = $option == $currentPerPage ? 'selected' : '';
        $html .= '<option value="' . $option . '" ' . $selected . '>' . $option . '</option>';
    }
    
    $html .= '</select>';
    $html .= '<span class="text-sm text-gray-700 dark:text-gray-300">per page</span>';
    $html .= '</div>';
    
    $html .= '<script>';
    $html .= 'function changePerPage(perPage) {';
    $html .= '  const url = "' . $baseUrl . '?per_page=" + perPage + "&page=1' . $queryString . '";';
    $html .= '  window.location.href = url;';
    $html .= '}';
    $html .= '</script>';
    
    return $html;
}

/**
 * Get pagination parameters from request
 * @param int $defaultPerPage Default items per page
 * @param int $maxPerPage Maximum items per page
 * @return array Pagination parameters
 */
function getPaginationParams($defaultPerPage = 20, $maxPerPage = 100) {
    $page = max(1, (int)($_GET['page'] ?? 1));
    $perPage = max(1, min($maxPerPage, (int)($_GET['per_page'] ?? $defaultPerPage)));
    
    return [
        'page' => $page,
        'per_page' => $perPage
    ];
}

/**
 * Generate pagination info text
 * @param array $pagination Pagination info
 * @return string Pagination info text
 */
function getPaginationInfo($pagination) {
    if ($pagination['total_items'] == 0) {
        return 'No items found';
    }
    
    if ($pagination['total_items'] == 1) {
        return '1 item';
    }
    
    if ($pagination['total_pages'] == 1) {
        return $pagination['total_items'] . ' items';
    }
    
    return 'Showing ' . $pagination['start_item'] . '-' . $pagination['end_item'] . 
           ' of ' . $pagination['total_items'] . ' items';
}

/**
 * Check if pagination is needed
 * @param int $totalItems Total number of items
 * @param int $perPage Items per page
 * @return bool True if pagination is needed
 */
function needsPagination($totalItems, $perPage) {
    return $totalItems > $perPage;
}
?>
