<?php
/**
 * Update System Core
 * Handles both dashboard boilerplate and custom app updates
 * 
 * Features:
 * - Dashboard boilerplate updates
 * - Custom app update API
 * - Safe update process with backups
 * - Version management
 * - Rollback functionality
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../auth.php';
require_once __DIR__ . '/logger.php';

class UpdateSystem {
    private $updateDir;
    private $backupDir;
    private $configFile;
    private $customApps;
    
    public function __construct() {
        $this->updateDir = __DIR__ . '/../updates';
        $this->backupDir = __DIR__ . '/../backups/updates';
        $this->configFile = __DIR__ . '/../config/update_config.json';
        $this->customApps = [];
        
        // Ensure directories exist
        $this->ensureDirectories();
        $this->loadConfiguration();
        $this->discoverApps();
    }
    
    /**
     * Ensure required directories exist
     */
    private function ensureDirectories() {
        $dirs = [$this->updateDir, $this->backupDir, dirname($this->configFile)];
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * Load update configuration
     */
    private function loadConfiguration() {
        if (file_exists($this->configFile)) {
            $config = json_decode(file_get_contents($this->configFile), true);
            $this->customApps = $config['custom_apps'] ?? [];
        } else {
            $this->saveConfiguration();
        }
    }
    
    /**
     * Save update configuration
     */
    private function saveConfiguration() {
        $config = [
            'dashboard' => [
                'current_version' => $this->getCurrentDashboardVersion(),
                'last_update' => date('Y-m-d H:i:s'),
                'update_channel' => 'stable',
                'auto_backup' => true,
                'update_server_url' => null // Set this to your update server URL
            ],
            'custom_apps' => $this->customApps,
            'settings' => [
                'backup_retention_days' => 30,
                'max_backup_size_mb' => 500,
                'require_admin_approval' => true,
                'maintenance_mode_during_update' => true,
                'demo_mode' => false // Set to true to show demo updates
            ]
        ];

        file_put_contents($this->configFile, json_encode($config, JSON_PRETTY_PRINT));
    }

    /**
     * Discover apps in the app directory
     */
    private function discoverApps() {
        $appDir = __DIR__ . '/../app';

        if (!is_dir($appDir)) {
            return;
        }

        $apps = scandir($appDir);
        foreach ($apps as $app) {
            if ($app === '.' || $app === '..' || !is_dir($appDir . '/' . $app)) {
                continue;
            }

            $configFile = $appDir . '/' . $app . '/config.json';
            $appFile = $appDir . '/' . $app . '/includes/app.php';

            if (file_exists($configFile) && file_exists($appFile)) {
                $this->registerAppFromDirectory($app, $configFile, $appFile);
            }
        }
    }

    /**
     * Register app from directory
     */
    private function registerAppFromDirectory($appDir, $configFile, $appFile) {
        try {
            $config = json_decode(file_get_contents($configFile), true);

            if (!$config || !isset($config['app']['id'])) {
                return;
            }

            $appId = $config['app']['id'];

            // Skip if already registered
            if (isset($this->customApps[$appId])) {
                return;
            }

            // Include the app file to register it
            require_once $appFile;

            logActivity('update.app_discovered', 'App discovered and registered', [
                'app_id' => $appId,
                'app_name' => $config['app']['name'] ?? $appId,
                'app_dir' => $appDir
            ]);

        } catch (Exception $e) {
            error_log("Failed to register app from directory {$appDir}: " . $e->getMessage());
        }
    }

    /**
     * Get update configuration
     */
    private function getUpdateConfig() {
        if (file_exists($this->configFile)) {
            return json_decode(file_get_contents($this->configFile), true);
        }

        // Return default config if file doesn't exist
        return [
            'dashboard' => [
                'current_version' => $this->getCurrentDashboardVersion(),
                'update_server_url' => null
            ],
            'settings' => [
                'demo_mode' => false
            ]
        ];
    }

    /**
     * Fetch update information from server
     */
    private function fetchUpdateFromServer($serverUrl) {
        $currentVersion = $this->getCurrentDashboardVersion();
        $requestUrl = $serverUrl . '?current_version=' . urlencode($currentVersion);

        // Use cURL to make the request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Dashboard-Update-Client/1.0');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error || $httpCode !== 200) {
            // Log the error
            error_log("Update check failed: HTTP $httpCode, Error: $error");

            // Return current version (no update available)
            return [
                'version' => $currentVersion,
                'release_notes' => 'Update check failed',
                'download_url' => '',
                'size' => '0 MB',
                'critical' => false
            ];
        }

        $updateData = json_decode($response, true);

        if (!$updateData || !isset($updateData['version'])) {
            return [
                'version' => $currentVersion,
                'release_notes' => 'Invalid update response',
                'download_url' => '',
                'size' => '0 MB',
                'critical' => false
            ];
        }

        return $updateData;
    }
    
    /**
     * Get current dashboard version
     */
    public function getCurrentDashboardVersion() {
        $versionFile = __DIR__ . '/../VERSION';
        if (file_exists($versionFile)) {
            return trim(file_get_contents($versionFile));
        }
        return '1.0.0'; // Default version
    }
    
    /**
     * Register a custom app for updates
     */
    public function registerCustomApp($appId, $config) {
        $this->customApps[$appId] = [
            'name' => $config['name'],
            'version' => $config['version'],
            'update_url' => $config['update_url'] ?? null,
            'update_handler' => $config['update_handler'] ?? null,
            'dependencies' => $config['dependencies'] ?? [],
            'last_update' => date('Y-m-d H:i:s'),
            'enabled' => true
        ];
        
        $this->saveConfiguration();
        
        logActivity('update.app_registered', 'Custom app registered for updates', [
            'app_id' => $appId,
            'app_name' => $config['name'],
            'version' => $config['version']
        ]);
    }
    
    /**
     * Check for dashboard updates
     */
    public function checkDashboardUpdates() {
        // This would typically check a remote server for updates
        // For now, we'll simulate checking for updates
        
        $currentVersion = $this->getCurrentDashboardVersion();
        $updateInfo = $this->fetchDashboardUpdateInfo();
        
        if (version_compare($updateInfo['version'], $currentVersion, '>')) {
            return [
                'available' => true,
                'current_version' => $currentVersion,
                'new_version' => $updateInfo['version'],
                'release_notes' => $updateInfo['release_notes'],
                'download_url' => $updateInfo['download_url'],
                'size' => $updateInfo['size'],
                'critical' => $updateInfo['critical'] ?? false
            ];
        }
        
        return ['available' => false, 'current_version' => $currentVersion];
    }
    
    /**
     * Fetch dashboard update information
     */
    private function fetchDashboardUpdateInfo() {
        // Load configuration to check if we should use demo mode
        $config = $this->getUpdateConfig();

        // If demo mode is enabled, return mock data
        if ($config['settings']['demo_mode'] ?? false) {
            return [
                'version' => '1.1.0',
                'release_notes' => 'Bug fixes and performance improvements (DEMO)',
                'download_url' => 'https://updates.example.com/dashboard/v1.1.0.zip',
                'size' => '2.5 MB',
                'critical' => false
            ];
        }

        // Production mode: Check real update server
        $updateServerUrl = $config['dashboard']['update_server_url'] ?? null;

        if (!$updateServerUrl) {
            // No update server configured, return no updates
            return [
                'version' => $this->getCurrentDashboardVersion(),
                'release_notes' => 'No update server configured',
                'download_url' => '',
                'size' => '0 MB',
                'critical' => false
            ];
        }

        // Make HTTP request to update server
        return $this->fetchUpdateFromServer($updateServerUrl);
    }
    
    /**
     * Check for custom app updates
     */
    public function checkCustomAppUpdates($appId = null) {
        $updates = [];
        $appsToCheck = $appId ? [$appId => $this->customApps[$appId]] : $this->customApps;
        
        foreach ($appsToCheck as $id => $app) {
            if (!$app['enabled']) continue;
            
            $updateInfo = $this->checkSingleAppUpdate($id, $app);
            if ($updateInfo['available']) {
                $updates[$id] = $updateInfo;
            }
        }
        
        return $updates;
    }
    
    /**
     * Check update for a single custom app
     */
    private function checkSingleAppUpdate($appId, $appConfig) {
        // Call the app's update handler if available
        if (isset($appConfig['update_handler']) && is_callable($appConfig['update_handler'])) {
            return call_user_func($appConfig['update_handler']);
        }
        
        // Or check via URL if provided
        if (isset($appConfig['update_url'])) {
            return $this->fetchAppUpdateFromUrl($appConfig['update_url'], $appConfig['version']);
        }
        
        return ['available' => false, 'current_version' => $appConfig['version']];
    }
    
    /**
     * Fetch app update from URL
     */
    private function fetchAppUpdateFromUrl($url, $currentVersion) {
        // Implementation would make HTTP request to check for updates
        // For demo, return mock data
        return [
            'available' => false,
            'current_version' => $currentVersion
        ];
    }
    
    /**
     * Create backup before update
     */
    public function createBackup($type = 'dashboard', $appId = null) {
        $timestamp = date('Y-m-d_H-i-s');
        $backupName = $type . '_' . ($appId ?? 'core') . '_' . $timestamp;
        $backupPath = $this->backupDir . '/' . $backupName;
        
        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }
        
        if ($type === 'dashboard') {
            return $this->createDashboardBackup($backupPath);
        } else {
            return $this->createAppBackup($backupPath, $appId);
        }
    }
    
    /**
     * Create dashboard backup
     */
    private function createDashboardBackup($backupPath) {
        $excludeDirs = ['backups', 'updates', 'logs', 'tmp'];
        $rootDir = dirname(__DIR__);
        
        // Copy core files
        $this->recursiveCopy($rootDir, $backupPath, $excludeDirs);
        
        // Create backup info file
        $backupInfo = [
            'type' => 'dashboard',
            'version' => $this->getCurrentDashboardVersion(),
            'created' => date('Y-m-d H:i:s'),
            'size' => $this->getDirectorySize($backupPath)
        ];
        
        file_put_contents($backupPath . '/backup_info.json', json_encode($backupInfo, JSON_PRETTY_PRINT));
        
        logActivity('update.backup_created', 'Dashboard backup created', [
            'backup_path' => basename($backupPath),
            'size' => $backupInfo['size']
        ]);

        // Run automatic cleanup after creating backup
        $this->cleanupOldBackups();

        return $backupPath;
    }
    
    /**
     * Recursive copy function
     */
    private function recursiveCopy($src, $dst, $excludeDirs = []) {
        $dir = opendir($src);
        if (!is_dir($dst)) {
            mkdir($dst, 0755, true);
        }
        
        while (($file = readdir($dir)) !== false) {
            if ($file != '.' && $file != '..') {
                $srcPath = $src . '/' . $file;
                $dstPath = $dst . '/' . $file;
                
                if (is_dir($srcPath)) {
                    if (!in_array($file, $excludeDirs)) {
                        $this->recursiveCopy($srcPath, $dstPath, $excludeDirs);
                    }
                } else {
                    copy($srcPath, $dstPath);
                }
            }
        }
        closedir($dir);
    }
    
    /**
     * Get directory size
     */
    private function getDirectorySize($dir) {
        $size = 0;
        foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir)) as $file) {
            $size += $file->getSize();
        }
        return $size;
    }
    
    /**
     * Get all available backups
     */
    public function getAvailableBackups() {
        $backups = [];
        if (!is_dir($this->backupDir)) {
            return $backups;
        }
        
        $dirs = scandir($this->backupDir);
        foreach ($dirs as $dir) {
            if ($dir != '.' && $dir != '..' && is_dir($this->backupDir . '/' . $dir)) {
                $infoFile = $this->backupDir . '/' . $dir . '/backup_info.json';
                if (file_exists($infoFile)) {
                    $info = json_decode(file_get_contents($infoFile), true);
                    $info['name'] = $dir;
                    $backups[] = $info;
                }
            }
        }
        
        // Sort by creation date (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['created']) - strtotime($a['created']);
        });
        
        return $backups;
    }

    /**
     * Clean up old backups based on retention settings
     */
    public function cleanupOldBackups() {
        $config = $this->getUpdateConfig();
        $retentionDays = $config['settings']['backup_retention_days'] ?? 30;
        $maxBackupSizeMB = $config['settings']['max_backup_size_mb'] ?? 500;

        $backups = $this->getAvailableBackups();
        $deletedCount = 0;
        $freedSpace = 0;

        foreach ($backups as $backup) {
            $backupPath = $this->backupDir . '/' . $backup['name'];
            $backupAge = time() - strtotime($backup['created']);
            $ageDays = $backupAge / (24 * 60 * 60);

            // Delete if older than retention period
            if ($ageDays > $retentionDays) {
                $size = $backup['size'];
                if ($this->deleteBackup($backup['name'])) {
                    $deletedCount++;
                    $freedSpace += $size;
                }
            }
        }

        // Also check total backup size and delete oldest if over limit
        $totalSize = array_sum(array_column($backups, 'size'));
        $maxSizeBytes = $maxBackupSizeMB * 1024 * 1024;

        if ($totalSize > $maxSizeBytes) {
            // Sort by creation date (oldest first)
            usort($backups, function($a, $b) {
                return strtotime($a['created']) - strtotime($b['created']);
            });

            foreach ($backups as $backup) {
                if ($totalSize <= $maxSizeBytes) break;

                $backupPath = $this->backupDir . '/' . $backup['name'];
                if (is_dir($backupPath)) {
                    $size = $backup['size'];
                    if ($this->deleteBackup($backup['name'])) {
                        $deletedCount++;
                        $freedSpace += $size;
                        $totalSize -= $size;
                    }
                }
            }
        }

        if ($deletedCount > 0) {
            logActivity('update.backup_cleanup', 'Automatic backup cleanup completed', [
                'deleted_count' => $deletedCount,
                'freed_space_mb' => round($freedSpace / 1024 / 1024, 2),
                'retention_days' => $retentionDays
            ]);
        }

        return [
            'deleted_count' => $deletedCount,
            'freed_space' => $freedSpace
        ];
    }

    /**
     * Delete a specific backup
     */
    public function deleteBackup($backupName) {
        $backupPath = $this->backupDir . '/' . $backupName;

        if (!is_dir($backupPath)) {
            return false;
        }

        // Security check: ensure backup name doesn't contain path traversal
        if (strpos($backupName, '..') !== false || strpos($backupName, '/') !== false) {
            return false;
        }

        try {
            $this->recursiveDelete($backupPath);

            logActivity('update.backup_deleted', 'Backup deleted', [
                'backup_name' => $backupName,
                'admin' => $_SESSION['username'] ?? 'system'
            ]);

            return true;
        } catch (Exception $e) {
            error_log("Failed to delete backup $backupName: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recursive delete function
     */
    private function recursiveDelete($dir) {
        if (!is_dir($dir)) {
            return unlink($dir);
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->recursiveDelete($path) : unlink($path);
        }

        return rmdir($dir);
    }

    /**
     * Get backup statistics
     */
    public function getBackupStats() {
        $backups = $this->getAvailableBackups();
        $totalSize = array_sum(array_column($backups, 'size'));
        $config = $this->getUpdateConfig();

        return [
            'total_backups' => count($backups),
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'oldest_backup' => !empty($backups) ? end($backups)['created'] : null,
            'newest_backup' => !empty($backups) ? $backups[0]['created'] : null,
            'retention_days' => $config['settings']['backup_retention_days'] ?? 30,
            'max_size_mb' => $config['settings']['max_backup_size_mb'] ?? 500
        ];
    }
}

// Custom App Update API Interface
interface CustomAppUpdateInterface {
    public function checkForUpdates();
    public function downloadUpdate($updateInfo);
    public function applyUpdate($updatePath);
    public function rollbackUpdate($backupPath);
    public function getVersion();
}
