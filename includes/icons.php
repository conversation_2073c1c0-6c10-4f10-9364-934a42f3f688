<?php
/**
 * Icon Management System
 * Provides centralized icon handling with fallback support
 */

// Icon configuration
define('ICONS_DIR', __DIR__ . '/../assets/images/icons');
define('ICONS_URL', 'assets/images/icons');

/**
 * Icon registry with emoji fallbacks
 */
function getIconRegistry() {
    return [
        // Main Application Icons
        'app-logo' => ['file' => 'app-logo.svg', 'fallback' => '🤖', 'alt' => 'AI Dashboard Logo'],
        'favicon' => ['file' => 'favicon.svg', 'fallback' => '🤖', 'alt' => 'AI Dashboard'],
        
        // User & Account Icons
        'user-profile' => ['file' => 'user-profile.svg', 'fallback' => '👤', 'alt' => 'User Profile'],
        'user-group' => ['file' => 'user-group.svg', 'fallback' => '👥', 'alt' => 'User Management'],
        'welcome-wave' => ['file' => 'welcome-wave.svg', 'fallback' => '👋', 'alt' => 'Welcome'],
        'admin-key' => ['file' => 'admin-key.svg', 'fallback' => '🔑', 'alt' => 'Admin Access'],
        
        // System & Settings Icons
        'settings-gear' => ['file' => 'settings-gear.svg', 'fallback' => '⚙️', 'alt' => 'Settings'],
        'logs-clipboard' => ['file' => 'logs-clipboard.svg', 'fallback' => '📋', 'alt' => 'System Logs'],
        'backup-disk' => ['file' => 'backup-disk.svg', 'fallback' => '💾', 'alt' => 'Data Backup'],
        
        // Data & Activity Icons
        'activity-chart' => ['file' => 'activity-chart.svg', 'fallback' => '📊', 'alt' => 'Activity'],
        'calendar-date' => ['file' => 'calendar-date.svg', 'fallback' => '📅', 'alt' => 'Date'],
        'clock-time' => ['file' => 'clock-time.svg', 'fallback' => '🕒', 'alt' => 'Time'],
        'document-note' => ['file' => 'document-note.svg', 'fallback' => '📝', 'alt' => 'Document'],
        
        // Security Icons
        'security-lock' => ['file' => 'security-lock.svg', 'fallback' => '🔐', 'alt' => 'Security'],
        'privacy-shield' => ['file' => 'privacy-shield.svg', 'fallback' => '🔒', 'alt' => 'Privacy'],
        'door-logout' => ['file' => 'door-logout.svg', 'fallback' => '🚪', 'alt' => 'Logout'],
        
        // Feature Icons
        'speed-bolt' => ['file' => 'speed-bolt.svg', 'fallback' => '⚡', 'alt' => 'Fast Performance'],
        'mobile-phone' => ['file' => 'mobile-phone.svg', 'fallback' => '📱', 'alt' => 'Mobile Responsive'],
        'architecture' => ['file' => 'architecture.svg', 'fallback' => '🏗️', 'alt' => 'Architecture'],
        'design-palette' => ['file' => 'design-palette.svg', 'fallback' => '🎨', 'alt' => 'Design'],
        'rocket-deploy' => ['file' => 'rocket-deploy.svg', 'fallback' => '🚀', 'alt' => 'Deployment'],
        
        // Communication Icons
        'chat-support' => ['file' => 'chat-support.svg', 'fallback' => '💬', 'alt' => 'Support'],
        'connection-link' => ['file' => 'connection-link.svg', 'fallback' => '🔗', 'alt' => 'Connection'],
        
        // Status & Action Icons
        'success-check' => ['file' => 'success-check.svg', 'fallback' => '✅', 'alt' => 'Success'],
        'error-x' => ['file' => 'error-x.svg', 'fallback' => '❌', 'alt' => 'Error'],
        'warning-alert' => ['file' => 'warning-alert.svg', 'fallback' => '⚠️', 'alt' => 'Warning'],
        'remove-x' => ['file' => 'remove-x.svg', 'fallback' => '✕', 'alt' => 'Remove'],

        // Admin & Management Icons
        'admin-crown' => ['file' => 'admin-crown.svg', 'fallback' => '👑', 'alt' => 'Admin'],
        'moderator-shield' => ['file' => 'moderator-shield.svg', 'fallback' => '🛡️', 'alt' => 'Moderator'],
        'security-shield' => ['file' => 'security-shield.svg', 'fallback' => '🛡️', 'alt' => 'Security'],

        // Update & System Icons
        'update-refresh' => ['file' => 'update-refresh.svg', 'fallback' => '🔄', 'alt' => 'Update'],
        'backup-disk' => ['file' => 'backup-disk.svg', 'fallback' => '💾', 'alt' => 'Backup'],
        'package-box' => ['file' => 'package-box.svg', 'fallback' => '📦', 'alt' => 'Package'],
        'export-download' => ['file' => 'export-download.svg', 'fallback' => '📥', 'alt' => 'Export'],

        // Interface Icons
        'target-aim' => ['file' => 'target-aim.svg', 'fallback' => '🎯', 'alt' => 'Target'],
        'folder-manage' => ['file' => 'folder-manage.svg', 'fallback' => '📁', 'alt' => 'Folder'],
        'cleanup-broom' => ['file' => 'cleanup-broom.svg', 'fallback' => '🧹', 'alt' => 'Cleanup'],
        'rollback-arrow' => ['file' => 'rollback-arrow.svg', 'fallback' => '⏪', 'alt' => 'Rollback'],
        'clipboard-copy' => ['file' => 'clipboard-copy.svg', 'fallback' => '📋', 'alt' => 'Copy'],
        'info-circle' => ['file' => 'info-circle.svg', 'fallback' => 'ℹ️', 'alt' => 'Information'],
        'view-eye' => ['file' => 'view-eye.svg', 'fallback' => '👁️', 'alt' => 'View/Preview'],

        // App-specific Icons
        'app-sample' => ['file' => 'app-sample.svg', 'fallback' => '📱', 'alt' => 'Sample App'],
        'app-grid' => ['file' => 'app-grid.svg', 'fallback' => '⚏', 'alt' => 'App Grid'],
        'file-storage' => ['file' => 'file-storage.svg', 'fallback' => '📁', 'alt' => 'File Storage'],
        'ai-robot' => ['file' => 'ai-robot.svg', 'fallback' => '🤖', 'alt' => 'AI Robot'],
        'theme-toggle' => ['file' => 'theme-toggle.svg', 'fallback' => '🎨', 'alt' => 'Theme Toggle'],
    ];
}

/**
 * Get icon HTML
 * @param string $iconName Icon identifier
 * @param string $size CSS size class (text-xl, text-2xl, etc.)
 * @param array $attributes Additional HTML attributes
 * @return string Icon HTML
 */
function getIcon($iconName, $size = 'text-2xl', $attributes = []) {
    $registry = getIconRegistry();
    
    if (!isset($registry[$iconName])) {
        return '<span class="' . $size . '">❓</span>'; // Unknown icon fallback
    }
    
    $icon = $registry[$iconName];
    $iconPath = ICONS_DIR . '/' . $icon['file'];
    $iconUrl = getIconUrl($icon['file']);
    
    // Check if custom icon file exists
    if (file_exists($iconPath)) {
        return renderSvgIcon($iconUrl, $icon['alt'], $size, $attributes);
    } else {
        return renderEmojiIcon($icon['fallback'], $size, $attributes);
    }
}

/**
 * Get icon URL with proper path handling
 * @param string $filename Icon filename
 * @return string Icon URL
 */
function getIconUrl($filename) {
    $requestUri = $_SERVER['REQUEST_URI'];

    // Determine the correct path based on current location
    if (strpos($requestUri, '/admin/') !== false) {
        // In admin directory
        $basePath = '../' . ICONS_URL;
    } elseif (strpos($requestUri, '/app/') !== false) {
        // In app directory - need to go up two levels
        $basePath = '../../' . ICONS_URL;
    } else {
        // In root directory
        $basePath = ICONS_URL;
    }

    return $basePath . '/' . $filename;
}

/**
 * Render SVG icon
 * @param string $url Icon URL
 * @param string $alt Alt text
 * @param string $size CSS size class
 * @param array $attributes Additional attributes
 * @return string SVG icon HTML
 */
function renderSvgIcon($url, $alt, $size, $attributes = []) {
    $sizeMap = [
        'text-sm' => 'w-4 h-4',
        'text-base' => 'w-5 h-5',
        'text-lg' => 'w-6 h-6',
        'text-xl' => 'w-7 h-7',
        'text-2xl' => 'w-8 h-8',
        'text-3xl' => 'w-10 h-10',
        'text-4xl' => 'w-12 h-12',
        'text-5xl' => 'w-16 h-16',
        'text-6xl' => 'w-20 h-20'
    ];
    
    $sizeClass = $sizeMap[$size] ?? 'w-8 h-8';
    $defaultClasses = 'inline-block icon-text-align ' . $sizeClass;
    
    // Merge attributes
    $attrs = array_merge([
        'class' => $defaultClasses,
        'alt' => $alt,
        'title' => $alt
    ], $attributes);
    
    $attrString = '';
    foreach ($attrs as $key => $value) {
        $attrString .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
    }
    
    return '<img src="' . $url . '"' . $attrString . '>';
}

/**
 * Render emoji icon (fallback)
 * @param string $emoji Emoji character
 * @param string $size CSS size class
 * @param array $attributes Additional attributes
 * @return string Emoji icon HTML
 */
function renderEmojiIcon($emoji, $size, $attributes = []) {
    $defaultClasses = $size;
    $attrs = array_merge(['class' => $defaultClasses], $attributes);
    
    $attrString = '';
    foreach ($attrs as $key => $value) {
        $attrString .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
    }
    
    return '<span' . $attrString . '>' . $emoji . '</span>';
}

/**
 * Check if icon file exists
 * @param string $iconName Icon identifier
 * @return bool True if custom icon exists
 */
function iconExists($iconName) {
    $registry = getIconRegistry();
    if (!isset($registry[$iconName])) {
        return false;
    }
    
    $iconPath = ICONS_DIR . '/' . $registry[$iconName]['file'];
    return file_exists($iconPath);
}

/**
 * Get list of all available icons
 * @return array List of icon names and their status
 */
function getIconList() {
    $registry = getIconRegistry();
    $icons = [];
    
    foreach ($registry as $name => $config) {
        $icons[$name] = [
            'name' => $name,
            'file' => $config['file'],
            'fallback' => $config['fallback'],
            'alt' => $config['alt'],
            'exists' => iconExists($name),
            'path' => ICONS_DIR . '/' . $config['file']
        ];
    }
    
    return $icons;
}

/**
 * Generate favicon HTML
 * @return string Favicon HTML
 */
function getFaviconHtml() {
    $iconPath = ICONS_DIR . '/favicon.svg';
    
    if (file_exists($iconPath)) {
        $iconUrl = getIconUrl('favicon.svg');
        return '<link rel="icon" type="image/svg+xml" href="' . $iconUrl . '">';
    } else {
        // Emoji fallback favicon
        return '<link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 100 100\'><text y=\'.9em\' font-size=\'90\'>🤖</text></svg>">';
    }
}
?>
