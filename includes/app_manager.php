<?php
/**
 * App Manager - Handles custom app discovery and management
 */

/**
 * Get all available custom apps
 */
function getAvailableApps() {
    $apps = [];
    $appDir = __DIR__ . '/../app';
    
    if (!is_dir($appDir)) {
        return $apps;
    }
    
    $directories = scandir($appDir);
    foreach ($directories as $dir) {
        if ($dir === '.' || $dir === '..' || !is_dir($appDir . '/' . $dir)) {
            continue;
        }
        
        $configFile = $appDir . '/' . $dir . '/config.json';
        if (file_exists($configFile)) {
            $config = json_decode(file_get_contents($configFile), true);
            
            if ($config && isset($config['app']['id'])) {
                $apps[] = [
                    'id' => $config['app']['id'],
                    'name' => $config['app']['name'] ?? $config['app']['id'],
                    'description' => $config['app']['description'] ?? '',
                    'version' => $config['app']['version'] ?? '1.0.0',
                    'path' => $dir,
                    'url' => 'app/' . $dir . '/index.php',
                    'menu_item' => $config['dashboard']['menu_item'] ?? null,
                    'requires_admin' => $config['dashboard']['requires_admin'] ?? false,
                    'visible' => $config['dashboard']['menu_item']['visible'] ?? true
                ];
            }
        }
    }
    
    // Sort by menu position
    usort($apps, function($a, $b) {
        $posA = $a['menu_item']['position'] ?? 999;
        $posB = $b['menu_item']['position'] ?? 999;
        return $posA - $posB;
    });
    
    return $apps;
}

/**
 * Get apps that should appear in the navigation menu
 */
function getMenuApps() {
    $apps = getAvailableApps();
    $menuApps = [];
    
    foreach ($apps as $app) {
        // Check if app should be visible in menu
        if (!$app['visible']) {
            continue;
        }
        
        // Check admin requirements
        if ($app['requires_admin'] && !isAdmin()) {
            continue;
        }
        
        $menuApps[] = $app;
    }
    
    return $menuApps;
}

/**
 * Get app by ID
 */
function getAppById($appId) {
    $apps = getAvailableApps();
    
    foreach ($apps as $app) {
        if ($app['id'] === $appId) {
            return $app;
        }
    }
    
    return null;
}

/**
 * Check if user has access to app
 */
function hasAppAccess($app) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // Check admin requirements
    if ($app['requires_admin'] && !isAdmin()) {
        return false;
    }
    
    return true;
}

/**
 * Generate app menu HTML
 */
function generateAppMenuHTML($isMobile = false) {
    $apps = getMenuApps();

    if (empty($apps)) {
        return '';
    }

    // Check if any app is set as homepage
    $settings = loadSettings();
    $homepageApp = $settings['dashboard_homepage_app'] ?? '';

    // Determine correct path prefix based on current location
    $requestUri = $_SERVER['REQUEST_URI'];
    $pathPrefix = '';
    if (strpos($requestUri, '/admin/') !== false) {
        $pathPrefix = '../';
    } elseif (strpos($requestUri, '/app/') !== false) {
        $pathPrefix = '../../';
    }

    $html = '';
    $baseClass = $isMobile
        ? 'block px-3 py-2 rounded-md text-base font-medium'
        : 'px-3 py-2 rounded-md text-sm font-medium';

    // Filter apps and modify display based on homepage setting
    $displayApps = [];
    $dashboardLink = null;

    foreach ($apps as $app) {
        if (!empty($homepageApp) && $app['path'] === $homepageApp) {
            // This app is the homepage, so show "Dashboard" link instead
            $dashboardLink = [
                'name' => 'Dashboard',
                'url' => $pathPrefix . 'dashboard.php?view=dashboard'
            ];
        } else {
            // Regular app link
            $displayApps[] = $app;
        }
    }

    // Add dashboard link if an app is set as homepage
    if ($dashboardLink) {
        $html .= '<a href="' . htmlspecialchars($dashboardLink['url']) . '" class="' . getThemeComponentClasses('nav-link') . ' ' . $baseClass . '">';
        $html .= htmlspecialchars($dashboardLink['name']);
        $html .= '</a>';
    }

    // Handle remaining apps
    if (!empty($displayApps)) {
        if (!$isMobile && count($displayApps) > 1) {
            // Desktop dropdown for multiple apps
            $html .= '<div class="relative">';
            $html .= '<button id="appsDropdown" class="' . getThemeComponentClasses('nav-link') . ' ' . $baseClass . ' flex items-center" onclick="toggleAppsDropdown()">';
            $html .= 'Apps';
            $html .= '<svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            $html .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
            $html .= '</svg>';
            $html .= '</button>';

            $html .= '<div id="appsDropdownMenu" class="hidden absolute right-0 mt-2 w-48 ' . getThemeComponentClasses('dropdown') . ' rounded-md shadow-lg z-50">';
            $html .= '<div class="py-1">';

            foreach ($displayApps as $app) {
                $appUrl = $pathPrefix . $app['url'];
                $html .= '<a href="' . htmlspecialchars($appUrl) . '" class="' . getThemeComponentClasses('dropdown-item') . ' block px-4 py-2 text-sm">';
                $html .= htmlspecialchars($app['name']);
                $html .= '</a>';
            }

            $html .= '</div>';
            $html .= '</div>';
            $html .= '</div>';
        } else {
            // Single app link or mobile menu
            foreach ($displayApps as $app) {
                $appUrl = $pathPrefix . $app['url'];
                $html .= '<a href="' . htmlspecialchars($appUrl) . '" class="' . getThemeComponentClasses('nav-link') . ' ' . $baseClass . '">';
                $html .= htmlspecialchars($app['name']);
                $html .= '</a>';
            }
        }
    }

    return $html;
}
?>
