<?php
/**
 * AI API Client
 * Unified interface for communicating with different AI providers
 */

require_once 'ai_config.php';

/**
 * AI Client Class
 * Provides a unified interface for all AI providers
 */
class AIClient {
    private $config;
    
    public function __construct($userId) {
        $this->config = getActiveAIConfig($userId);
        
        if (!$this->config) {
            throw new Exception('No AI configuration found for user');
        }
    }
    
    /**
     * Send a message to the AI
     */
    public function sendMessage($message, $systemPrompt = null, $options = []) {
        switch ($this->config['provider']) {
            case 'anthropic':
                return $this->sendClaudeMessage($message, $systemPrompt, $options);
            case 'google':
                return $this->sendGeminiMessage($message, $systemPrompt, $options);
            case 'openai':
                return $this->sendOpenAIMessage($message, $systemPrompt, $options);
            default:
                throw new Exception('Unsupported AI provider: ' . $this->config['provider']);
        }
    }
    
    /**
     * Send message to <PERSON> (Anthropic)
     */
    private function sendClaudeMessage($message, $systemPrompt = null, $options = []) {
        $url = 'https://api.anthropic.com/v1/messages';
        
        $data = [
            'model' => $this->config['model'],
            'max_tokens' => $options['max_tokens'] ?? 8000,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $message
                ]
            ]
        ];
        
        if ($systemPrompt) {
            $data['system'] = $systemPrompt;
        }
        
        $headers = [
            'Content-Type: application/json',
            'x-api-key: ' . $this->config['api_key'],
            'anthropic-version: 2023-06-01'
        ];
        
        return $this->makeRequest($url, $data, $headers);
    }
    
    /**
     * Send message to Gemini (Google)
     */
    private function sendGeminiMessage($message, $systemPrompt = null, $options = []) {
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/' . $this->config['model'] . ':generateContent?key=' . $this->config['api_key'];
        
        $contents = [];
        
        if ($systemPrompt) {
            $contents[] = [
                'parts' => [['text' => $systemPrompt]],
                'role' => 'user'
            ];
        }
        
        $contents[] = [
            'parts' => [['text' => $message]],
            'role' => 'user'
        ];
        
        $data = [
            'contents' => $contents,
            'generationConfig' => [
                'maxOutputTokens' => $options['max_tokens'] ?? 8000,
                'temperature' => $options['temperature'] ?? 0.7
            ]
        ];
        
        $headers = [
            'Content-Type: application/json'
        ];
        
        return $this->makeRequest($url, $data, $headers);
    }
    
    /**
     * Send message to OpenAI
     */
    private function sendOpenAIMessage($message, $systemPrompt = null, $options = []) {
        $url = 'https://api.openai.com/v1/chat/completions';
        
        $messages = [];
        
        if ($systemPrompt) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemPrompt
            ];
        }
        
        $messages[] = [
            'role' => 'user',
            'content' => $message
        ];
        
        $data = [
            'model' => $this->config['model'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? 8000,
            'temperature' => $options['temperature'] ?? 0.7
        ];
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->config['api_key']
        ];
        
        return $this->makeRequest($url, $data, $headers);
    }
    
    /**
     * Make HTTP request to AI API
     */
    private function makeRequest($url, $data, $headers) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL error: ' . $error);
        }
        
        if ($httpCode !== 200) {
            throw new Exception('API request failed with status: ' . $httpCode . '. Response: ' . $response);
        }
        
        $result = json_decode($response, true);
        
        if (!$result) {
            throw new Exception('Invalid JSON response from AI API');
        }
        
        return $this->parseResponse($result);
    }
    
    /**
     * Parse response from different AI providers
     */
    private function parseResponse($response) {
        switch ($this->config['provider']) {
            case 'anthropic':
                return [
                    'success' => true,
                    'content' => $response['content'][0]['text'] ?? '',
                    'usage' => $response['usage'] ?? null,
                    'model' => $this->config['model'],
                    'provider' => 'anthropic'
                ];
                
            case 'google':
                return [
                    'success' => true,
                    'content' => $response['candidates'][0]['content']['parts'][0]['text'] ?? '',
                    'usage' => $response['usageMetadata'] ?? null,
                    'model' => $this->config['model'],
                    'provider' => 'google'
                ];
                
            case 'openai':
                return [
                    'success' => true,
                    'content' => $response['choices'][0]['message']['content'] ?? '',
                    'usage' => $response['usage'] ?? null,
                    'model' => $this->config['model'],
                    'provider' => 'openai'
                ];
                
            default:
                throw new Exception('Unknown provider response format');
        }
    }
    
    /**
     * Get current configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            $response = $this->sendMessage('Hello! Please respond with "Connection successful" to test the API.');
            return [
                'success' => true,
                'message' => 'API connection successful',
                'response' => $response['content']
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'API connection failed: ' . $e->getMessage()
            ];
        }
    }
}

/**
 * Helper function to create AI client for current user
 */
function createAIClient() {
    if (!isLoggedIn()) {
        throw new Exception('User must be logged in to use AI features');
    }
    
    return new AIClient($_SESSION['user_id']);
}

/**
 * Helper function to check if current user has AI configured
 */
function currentUserHasAI() {
    if (!isLoggedIn()) {
        return false;
    }
    
    return userHasAIConfigured($_SESSION['user_id']);
}
?>
