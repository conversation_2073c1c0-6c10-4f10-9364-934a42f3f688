<?php
/**
 * Maintenance Mode Page
 * Displayed when the application is in maintenance mode
 */

require_once 'config.php';
require_once 'includes/theme.php';

// Allow admins to bypass maintenance mode
if (isLoggedIn() && isAdmin()) {
    header('Location: dashboard.php');
    exit();
}

// Set page title
$pageTitle = 'Maintenance Mode';
include 'templates/header.php';
?>

<div class="max-w-2xl mx-auto text-center">
    <!-- Maintenance Icon -->
    <div class="text-8xl mb-8">🔧</div>
    
    <!-- Maintenance Message -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> p-8 rounded-lg shadow">
        <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?> mb-4">
            We'll be back soon!
        </h1>
        
        <p class="text-lg <?php echo getThemeComponentClasses('text-secondary'); ?> mb-6">
            <?php echo APP_NAME; ?> is currently undergoing scheduled maintenance. 
            We're working hard to improve your experience and will be back online shortly.
        </p>
        
        <div class="<?php echo getThemeComponentClasses('card-header'); ?> p-4 rounded-lg mb-6">
            <h3 class="font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                What's happening?
            </h3>
            <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                Our team is performing system updates and improvements to provide you with a better experience.
            </p>
        </div>
        
        <div class="space-y-4">
            <p class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">
                Expected downtime: Minimal
            </p>
            
            <p class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">
                Thank you for your patience!
            </p>
        </div>
        
        <!-- Refresh Button -->
        <div class="mt-8">
            <button
                onclick="window.location.reload()"
                class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-3 rounded-lg font-medium transition duration-200"
            >
                <?php echo getIcon('update-refresh', 'text-base'); ?> Check Again
            </button>
        </div>
    </div>
    
    <!-- Admin Login Link -->
    <?php if (!isLoggedIn()): ?>
        <div class="mt-8">
            <p class="text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">
                Administrator? 
                <a href="login.php" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                    Sign in here
                </a>
            </p>
        </div>
    <?php endif; ?>
</div>

<!-- Auto-refresh every 30 seconds -->
<script>
setTimeout(function() {
    window.location.reload();
}, 30000);
</script>

<?php include 'templates/footer.php'; ?>
