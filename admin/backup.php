<?php
/**
 * Backup Management Interface
 * Admin interface for creating, managing, and downloading backups
 */

require_once '../config.php';
require_once '../auth.php';
require_once '../includes/roles.php';
require_once '../includes/backup.php';
require_once '../includes/toast.php';

// Require admin access
requireAdmin();

// Handle actions
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        toastError('Invalid security token.');
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_backup':
                $options = [
                    'include_settings' => isset($_POST['include_settings']),
                    'include_users' => isset($_POST['include_users']),
                    'include_logs' => isset($_POST['include_logs']),
                    'format' => $_POST['format'] ?? 'zip'
                ];
                
                $result = createBackup($options);
                
                if ($result['success']) {
                    toastSuccess('Backup created successfully: ' . $result['file_name']);
                } else {
                    toastError('Failed to create backup: ' . $result['message']);
                }
                break;
                
            case 'delete_backup':
                $fileName = $_POST['file_name'] ?? '';
                if (deleteBackup($fileName)) {
                    toastSuccess('Backup deleted successfully.');
                } else {
                    toastError('Failed to delete backup.');
                }
                break;
                
            case 'cleanup_backups':
                $maxAge = (int)($_POST['max_age'] ?? 30);
                $maxCount = (int)($_POST['max_count'] ?? 10);
                $deleted = cleanupOldBackups($maxAge, $maxCount);
                
                if ($deleted > 0) {
                    toastSuccess("Cleaned up {$deleted} old backup files.");
                } else {
                    toastInfo('No old backups to clean up.');
                }
                break;
        }
        
        // Redirect to prevent form resubmission
        header('Location: backup.php');
        exit();
    }
}

// Handle download requests
if (isset($_GET['download'])) {
    $fileName = $_GET['download'];
    downloadBackup($fileName);
}

// Get backup list
$backups = getBackupList();

// Set page title
$pageTitle = 'Backup Management';

// Include header
include '../templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Backup Management</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Create and manage data backups</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <a href="users.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 text-center">User Management</a>
            <a href="settings.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 text-center">Settings</a>
        </div>
    </div>
    
    <!-- Create Backup Section -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Create New Backup</h2>
        </div>
        
        <form method="POST" class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="create_backup">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-4">What to Include</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" id="include_settings" name="include_settings" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="include_settings" class="ml-2 block text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                                Application Settings
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="include_users" name="include_users" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="include_users" class="ml-2 block text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                                User Accounts <span class="<?php echo getThemeComponentClasses('text-muted'); ?>">(passwords excluded)</span>
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="include_logs" name="include_logs" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="include_logs" class="ml-2 block text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                                Activity & Error Logs
                            </label>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-4">Backup Format</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="radio" id="format_zip" name="format" value="zip" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                            <label for="format_zip" class="ml-2 block text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                                ZIP Archive <span class="<?php echo getThemeComponentClasses('text-muted'); ?>">(recommended)</span>
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="radio" id="format_json" name="format" value="json" 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                            <label for="format_json" class="ml-2 block text-sm <?php echo getThemeComponentClasses('text-primary'); ?>">
                                JSON File <span class="<?php echo getThemeComponentClasses('text-muted'); ?>">(if ZIP not available)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6">
                <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200">
                    📦 Create Backup
                </button>
            </div>
        </form>
    </div>
    
    <!-- Existing Backups -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?> flex justify-between items-center">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Existing Backups</h2>
            <div class="flex space-x-2">
                <button onclick="showCleanupModal()" class="text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition duration-200">
                    🧹 Cleanup
                </button>
            </div>
        </div>
        
        <?php if (empty($backups)): ?>
            <div class="p-8 text-center <?php echo getThemeComponentClasses('text-muted'); ?>">
                <div class="text-4xl mb-4">📦</div>
                <h3 class="text-lg font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">No Backups Found</h3>
                <p>Create your first backup using the form above.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">File</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Size</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Format</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($backups as $backup): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-2xl mr-3">
                                            <?php echo $backup['format'] === 'zip' ? '🗜️' : '📄'; ?>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">
                                                <?php echo htmlspecialchars($backup['file_name']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                                    <?php echo date('M j, Y g:i A', strtotime($backup['created_at'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                                    <?php echo $backup['file_size_formatted']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php echo $backup['format'] === 'zip' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'; ?>">
                                        <?php echo strtoupper($backup['format']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <a href="?download=<?php echo urlencode($backup['file_name']); ?>" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                                        Download
                                    </a>
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this backup?')">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="action" value="delete_backup">
                                        <input type="hidden" name="file_name" value="<?php echo htmlspecialchars($backup['file_name']); ?>">
                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                                            Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Backup Information -->
    <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
        <div class="flex items-start">
            <div class="mr-4"><?php echo getIcon('info-circle', 'text-3xl'); ?></div>
            <div>
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-2">Backup Information</h3>
                <div class="text-blue-700 dark:text-blue-300 space-y-2 text-sm">
                    <p><strong>Security:</strong> User passwords are never included in backups for security reasons.</p>
                    <p><strong>Format:</strong> ZIP archives include both structured JSON data and original files.</p>
                    <p><strong>Restoration:</strong> Backups can be used to restore data in case of system failure.</p>
                    <p><strong>Storage:</strong> Backup files are stored in the <code>backups/</code> directory.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div id="cleanupModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Cleanup Old Backups</h3>
            </div>
            
            <form method="POST" class="p-6">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="cleanup_backups">
                
                <div class="space-y-4">
                    <div>
                        <label for="max_age" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Delete backups older than (days)
                        </label>
                        <input type="number" id="max_age" name="max_age" value="30" min="1" max="365" 
                               class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg">
                    </div>
                    
                    <div>
                        <label for="max_count" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                            Keep only this many newest backups
                        </label>
                        <input type="number" id="max_count" name="max_count" value="10" min="1" max="100" 
                               class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg">
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="hideCleanupModal()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg">
                        Cancel
                    </button>
                    <button type="submit" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition duration-200">
                        Cleanup
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showCleanupModal() {
    document.getElementById('cleanupModal').classList.remove('hidden');
}

function hideCleanupModal() {
    document.getElementById('cleanupModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('cleanupModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideCleanupModal();
    }
});
</script>

<?php 
// Include toast JavaScript
echo getToastJavaScript();
include '../templates/footer.php'; 
?>
