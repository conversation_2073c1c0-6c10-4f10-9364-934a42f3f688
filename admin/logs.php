<?php
/**
 * Log Viewer Page
 * Admin interface for viewing system logs
 */

require_once '../config.php';
require_once '../auth.php';
require_once '../includes/roles.php';

// Require admin access
requireAdmin();

// Get filter parameters
$logType = $_GET['type'] ?? 'activity';
$limit = (int)($_GET['limit'] ?? 50);
$page = (int)($_GET['page'] ?? 1);
$offset = ($page - 1) * $limit;

// Load logs based on type
if ($logType === 'error') {
    $logs = getErrorLogs($limit, $offset);
    $pageTitle = 'Error Logs';
} else {
    $logs = getActivityLogs($limit, $offset);
    $pageTitle = 'Activity Logs';
}

// Get log statistics
$logStats = getLogStatistics();

// Include header
include '../templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">System Logs</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Monitor system activity and errors</p>
        </div>
        <div class="flex space-x-3">
            <a href="users.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">User Management</a>
            <a href="settings.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">Settings</a>
        </div>
    </div>
    
    <!-- Log Statistics -->
    <div class="grid md:grid-cols-4 gap-6">
        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('activity-chart', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Total Activities</h3>
                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo $logStats['total_activities'] ?? 0; ?></p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('error-x', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Total Errors</h3>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400"><?php echo $logStats['total_errors'] ?? 0; ?></p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('clock-time', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Recent Activities</h3>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo $logStats['recent_activities'] ?? 0; ?></p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('user-group', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Active Users Today</h3>
                    <p class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?php echo $logStats['unique_users_today'] ?? 0; ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Log Filters -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow p-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
                <div>
                    <label for="logType" class="block text-sm font-medium text-gray-900 dark:text-white mb-1">Log Type</label>
                    <select id="logType" onchange="updateLogType()" class="<?php echo getThemeComponentClasses('input'); ?> px-3 py-2 rounded-lg">
                        <option value="activity" <?php echo $logType === 'activity' ? 'selected' : ''; ?>>Activity Logs</option>
                        <option value="error" <?php echo $logType === 'error' ? 'selected' : ''; ?>>Error Logs</option>
                    </select>
                </div>
                
                <div>
                    <label for="logLimit" class="block text-sm font-medium text-gray-900 dark:text-white mb-1">Show</label>
                    <select id="logLimit" onchange="updateLimit()" class="<?php echo getThemeComponentClasses('input'); ?> px-3 py-2 rounded-lg">
                        <option value="25" <?php echo $limit === 25 ? 'selected' : ''; ?>>25 entries</option>
                        <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50 entries</option>
                        <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100 entries</option>
                    </select>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <button onclick="location.reload()" class="btn-secondary">
                    <?php echo getIcon('update-refresh', 'text-base'); ?> Refresh
                </button>
                <button onclick="exportLogs()" class="btn-secondary">
                    <?php echo getIcon('export-download', 'text-base'); ?> Export
                </button>
            </div>
        </div>
    </div>
    
    <!-- Logs Table -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                <?php echo $logType === 'error' ? 'Error Logs' : 'Activity Logs'; ?>
            </h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Timestamp</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            <?php echo $logType === 'error' ? 'Level' : 'Action'; ?>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            <?php echo $logType === 'error' ? 'Message' : 'Description'; ?>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Details</th>
                    </tr>
                </thead>
                <tbody class="<?php echo getThemeComponentClasses('card'); ?> divide-y <?php echo getThemeComponentClasses('divider'); ?>">
                    <?php if (empty($logs)): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                                No logs found
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($logs as $log): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?php echo date('M j, Y g:i:s A', strtotime($log['timestamp'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($logType === 'error'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                            <?php
                                            $level = $log['level'] ?? 'info';
                                            echo $level === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                                 ($level === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300');
                                            ?>">
                                            <?php echo htmlspecialchars(ucfirst($level)); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                                            <?php echo htmlspecialchars($log['action'] ?? 'Unknown'); ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                    <div class="max-w-xs truncate">
                                        <?php 
                                        $message = $logType === 'error' ? ($log['message'] ?? '') : ($log['description'] ?? '');
                                        echo htmlspecialchars($message);
                                        ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?php 
                                    $username = $log['username'] ?? 'Unknown';
                                    $role = $log['user_role'] ?? '';
                                    echo htmlspecialchars($username);
                                    if ($role && $logType === 'activity') {
                                        echo " <span class='text-xs text-gray-500 dark:text-gray-400'>({$role})</span>";
                                    }
                                    ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?php echo htmlspecialchars($log['ip_address'] ?? 'Unknown'); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <button
                                        onclick="showLogDetails(<?php echo htmlspecialchars(json_encode($log)); ?>)"
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                    >
                                        View
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if (count($logs) === $limit): ?>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
                <div class="text-sm text-gray-600 dark:text-gray-300">
                    Showing <?php echo $offset + 1; ?> to <?php echo $offset + count($logs); ?> entries
                </div>
                <div class="flex space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?type=<?php echo $logType; ?>&limit=<?php echo $limit; ?>&page=<?php echo $page - 1; ?>" 
                           class="btn-secondary">Previous</a>
                    <?php endif; ?>
                    <a href="?type=<?php echo $logType; ?>&limit=<?php echo $limit; ?>&page=<?php echo $page + 1; ?>" 
                       class="btn-secondary">Next</a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Log Details Modal -->
<div id="logModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow-xl max-w-2xl w-full max-h-96 flex flex-col">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?> flex-shrink-0">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Log Details</h3>
            </div>
            <div class="p-6 flex-1 overflow-y-auto">
                <pre id="logDetails" class="text-sm bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white p-4 rounded overflow-x-auto"></pre>
            </div>
            <div class="px-6 py-4 border-t <?php echo getThemeComponentClasses('divider'); ?> text-right flex-shrink-0">
                <button onclick="hideLogDetails()" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function updateLogType() {
    const type = document.getElementById('logType').value;
    const url = new URL(window.location);
    url.searchParams.set('type', type);
    url.searchParams.set('page', '1');
    window.location = url;
}

function updateLimit() {
    const limit = document.getElementById('logLimit').value;
    const url = new URL(window.location);
    url.searchParams.set('limit', limit);
    url.searchParams.set('page', '1');
    window.location = url;
}

function showLogDetails(log) {
    document.getElementById('logDetails').textContent = JSON.stringify(log, null, 2);
    document.getElementById('logModal').classList.remove('hidden');
}

function hideLogDetails() {
    document.getElementById('logModal').classList.add('hidden');
}

function exportLogs() {
    const type = '<?php echo $logType; ?>';
    const url = `export_logs.php?type=${type}`;
    window.open(url, '_blank');
}

// Close modal when clicking outside
document.getElementById('logModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideLogDetails();
    }
});
</script>

<?php include '../templates/footer.php'; ?>
