<?php
/**
 * Update Management Interface
 * Admin interface for managing dashboard and custom app updates
 */

require_once '../config.php';
require_once '../includes/update_system.php';

// Security: Require admin authentication
requireAuth();
requireAdmin();

$updateSystem = new UpdateSystem();

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
    
    switch ($_POST['action']) {
        case 'check_dashboard_updates':
            $updates = $updateSystem->checkDashboardUpdates();
            echo json_encode(['success' => true, 'updates' => $updates]);
            exit;
            
        case 'check_app_updates':
            $appId = $_POST['app_id'] ?? null;
            $updates = $updateSystem->checkCustomAppUpdates($appId);
            echo json_encode(['success' => true, 'updates' => $updates]);
            exit;
            
        case 'create_backup':
            $type = $_POST['type'] ?? 'dashboard';
            $appId = $_POST['app_id'] ?? null;
            
            try {
                $backupPath = $updateSystem->createBackup($type, $appId);
                echo json_encode([
                    'success' => true, 
                    'message' => 'Backup created successfully',
                    'backup_path' => basename($backupPath)
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Backup failed: ' . $e->getMessage()]);
            }
            exit;
            
        case 'get_backups':
            $backups = $updateSystem->getAvailableBackups();
            echo json_encode(['success' => true, 'backups' => $backups]);
            exit;

        case 'toggle_demo_mode':
            $demoMode = $_POST['demo_mode'] === 'true';

            // Load current config
            $configFile = __DIR__ . '/../config/update_config.json';
            $config = [];
            if (file_exists($configFile)) {
                $config = json_decode(file_get_contents($configFile), true);
            }

            // Update demo mode setting
            $config['settings']['demo_mode'] = $demoMode;

            // Save config
            if (!is_dir(dirname($configFile))) {
                mkdir(dirname($configFile), 0755, true);
            }
            file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));

            logActivity('update.demo_mode_changed', 'Demo mode toggled', [
                'demo_mode' => $demoMode,
                'admin' => $_SESSION['username']
            ]);

            echo json_encode([
                'success' => true,
                'message' => 'Demo mode ' . ($demoMode ? 'enabled' : 'disabled'),
                'demo_mode' => $demoMode
            ]);
            exit;

        case 'delete_backup':
            $backupName = $_POST['backup_name'] ?? '';

            if (empty($backupName)) {
                echo json_encode(['success' => false, 'message' => 'Backup name required']);
                exit;
            }

            try {
                if ($updateSystem->deleteBackup($backupName)) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Backup deleted successfully'
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Failed to delete backup']);
                }
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Error deleting backup: ' . $e->getMessage()]);
            }
            exit;

        case 'cleanup_backups':
            try {
                $result = $updateSystem->cleanupOldBackups();
                echo json_encode([
                    'success' => true,
                    'message' => "Cleanup complete! Deleted {$result['deleted_count']} backups, freed " .
                                round($result['freed_space'] / 1024 / 1024, 2) . " MB",
                    'deleted_count' => $result['deleted_count'],
                    'freed_space_mb' => round($result['freed_space'] / 1024 / 1024, 2)
                ]);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Cleanup failed: ' . $e->getMessage()]);
            }
            exit;

        case 'get_backup_stats':
            $stats = $updateSystem->getBackupStats();
            echo json_encode(['success' => true, 'stats' => $stats]);
            exit;

        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            exit;
    }
}

// Get current status
$dashboardVersion = $updateSystem->getCurrentDashboardVersion();
$dashboardUpdates = $updateSystem->checkDashboardUpdates();
$customAppUpdates = $updateSystem->checkCustomAppUpdates();
$availableBackups = $updateSystem->getAvailableBackups();

// Get current configuration
$configFile = __DIR__ . '/../config/update_config.json';
$currentConfig = [];
if (file_exists($configFile)) {
    $currentConfig = json_decode(file_get_contents($configFile), true);
}
$demoMode = $currentConfig['settings']['demo_mode'] ?? false;

// Get backup statistics
$backupStats = $updateSystem->getBackupStats();

$pageTitle = 'Update Management';
include '../templates/header.php';
?>

<div class="max-w-6xl mx-auto p-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">Update Management</h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">Manage dashboard and custom app updates</p>
        </div>
        <div class="flex space-x-3">
            <a href="settings.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">Back to Settings</a>
            <button 
                onclick="checkAllUpdates()"
                class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200"
            >
                Check for Updates
            </button>
        </div>
    </div>
    
    <!-- Status Messages -->
    <div id="statusMessage" class="hidden mb-6"></div>

    <!-- Configuration Section -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Update Configuration</h2>
        </div>

        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-medium text-gray-900 dark:text-white mb-1">Demo Mode</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">
                        Enable demo mode to show mock updates for testing purposes.
                        <?php if ($demoMode): ?>
                            <span class="text-orange-600 dark:text-orange-400 font-medium">(Currently showing demo updates)</span>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-600 dark:text-gray-300">
                        <?php echo $demoMode ? 'Enabled' : 'Disabled'; ?>
                    </span>
                    <button
                        onclick="toggleDemoMode()"
                        class="<?php echo $demoMode ? 'bg-orange-600 hover:bg-orange-700' : 'bg-gray-600 hover:bg-gray-700'; ?> text-white px-4 py-2 rounded-lg font-medium transition duration-200"
                        id="demoModeBtn"
                    >
                        <?php echo $demoMode ? 'Disable Demo' : 'Enable Demo'; ?>
                    </button>
                </div>
            </div>

            <?php if (!$demoMode): ?>
                <div class="mt-4 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                    <h4 class="font-medium text-green-900 dark:text-green-100 mb-2">✅ Production Mode Active</h4>
                    <p class="text-sm text-green-800 dark:text-green-200">
                        The system will check for real updates from your configured update server.
                        To configure an update server, set the <code>update_server_url</code> in your update configuration.
                    </p>
                </div>
            <?php else: ?>
                <div class="mt-4 p-4 bg-orange-50 dark:bg-orange-900 rounded-lg">
                    <h4 class="font-medium text-orange-900 dark:text-orange-100 mb-2">⚠️ Demo Mode Active</h4>
                    <p class="text-sm text-orange-800 dark:text-orange-200">
                        The system is showing mock updates for demonstration purposes.
                        Disable demo mode for production use.
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Dashboard Updates -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Dashboard Boilerplate</h2>
        </div>
        
        <div class="p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('package-box', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Current Version</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300"><?php echo htmlspecialchars($dashboardVersion); ?></div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="text-2xl"><?php echo $dashboardUpdates['available'] ? getIcon('update-refresh', 'text-2xl') : getIcon('success-check', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Update Status</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">
                                    <?php echo $dashboardUpdates['available'] ? 'Update Available' : 'Up to Date'; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('security-shield', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Auto Backup</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Enabled</div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($dashboardUpdates['available']): ?>
                        <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-4">
                            <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">Update Available: v<?php echo htmlspecialchars($dashboardUpdates['new_version']); ?></h4>
                            <p class="text-sm text-blue-800 dark:text-blue-200 mb-3"><?php echo htmlspecialchars($dashboardUpdates['release_notes']); ?></p>
                            <div class="text-xs text-blue-700 dark:text-blue-300">
                                Size: <?php echo htmlspecialchars($dashboardUpdates['size']); ?>
                                <?php if ($dashboardUpdates['critical']): ?>
                                    <span class="ml-2 px-2 py-1 bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 rounded">Critical</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="ml-6 space-y-2">
                    <button 
                        onclick="createBackup('dashboard')"
                        class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full"
                    >
                        Create Backup
                    </button>
                    
                    <?php if ($dashboardUpdates['available']): ?>
                        <button 
                            onclick="updateDashboard()"
                            class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200 w-full"
                        >
                            Update Dashboard
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Custom Apps Updates -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Custom Applications</h2>
        </div>
        
        <div class="p-6">
            <?php if (empty($customAppUpdates)): ?>
                <div class="text-center py-8">
                    <div class="mb-4"><?php echo getIcon('mobile-phone', 'text-4xl'); ?></div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Custom Apps Registered</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Register your custom applications to enable update management.</p>
                    <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg text-left">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">How to Register a Custom App:</h4>
                        <pre class="text-sm text-gray-700 dark:text-gray-300"><code>// In your custom app
$updateSystem = new UpdateSystem();
$updateSystem->registerCustomApp('my_app', [
    'name' => 'My Custom App',
    'version' => '1.0.0',
    'update_url' => 'https://api.myapp.com/updates',
    'update_handler' => 'myAppUpdateHandler'
]);</code></pre>
                    </div>
                </div>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($customAppUpdates as $appId => $updateInfo): ?>
                        <div class="border <?php echo getThemeComponentClasses('divider'); ?> rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-medium text-gray-900 dark:text-white"><?php echo htmlspecialchars($updateInfo['name'] ?? $appId); ?></h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">
                                        Current: v<?php echo htmlspecialchars($updateInfo['current_version']); ?>
                                        <?php if ($updateInfo['available']): ?>
                                            → v<?php echo htmlspecialchars($updateInfo['new_version']); ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="flex space-x-2">
                                    <button 
                                        onclick="createBackup('app', '<?php echo htmlspecialchars($appId); ?>')"
                                        class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-1 rounded text-sm"
                                    >
                                        Backup
                                    </button>
                                    <?php if ($updateInfo['available']): ?>
                                        <button 
                                            onclick="updateApp('<?php echo htmlspecialchars($appId); ?>')"
                                            class="<?php echo getThemeComponentClasses('button-primary'); ?> px-3 py-1 rounded text-sm"
                                        >
                                            Update
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Backup Management -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?> flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Backup Management</h2>
            <div class="flex space-x-2">
                <button
                    onclick="cleanupBackups()"
                    class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-1 rounded text-sm"
                    title="Clean up old backups based on retention settings"
                >
                    <?php echo getIcon('cleanup-broom', 'text-sm'); ?> Cleanup
                </button>
                <button
                    onclick="refreshBackups()"
                    class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-1 rounded text-sm"
                >
                    <?php echo getIcon('update-refresh', 'text-sm'); ?> Refresh
                </button>
            </div>
        </div>

        <!-- Backup Statistics -->
        <div class="px-6 py-4 border-b <?php echo getThemeComponentClasses('divider'); ?>">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo $backupStats['total_backups']; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Total Backups</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo $backupStats['total_size_mb']; ?> MB</div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Total Size</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600 dark:text-orange-400"><?php echo $backupStats['retention_days']; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Retention Days</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?php echo $backupStats['max_size_mb']; ?> MB</div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Size Limit</div>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <?php if (empty($availableBackups)): ?>
                <div class="text-center py-8">
                    <div class="mb-4"><?php echo getIcon('backup-disk', 'text-4xl'); ?></div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Backups Available</h3>
                    <p class="text-gray-600 dark:text-gray-300">Create your first backup to enable rollback functionality.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b <?php echo getThemeComponentClasses('divider'); ?>">
                                <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Backup</th>
                                <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Type</th>
                                <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Version</th>
                                <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Created</th>
                                <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Size</th>
                                <th class="text-left py-2 font-medium text-gray-900 dark:text-white">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($availableBackups as $backup): ?>
                                <tr class="border-b <?php echo getThemeComponentClasses('divider'); ?>">
                                    <td class="py-2 text-sm text-gray-900 dark:text-white"><?php echo htmlspecialchars($backup['name']); ?></td>
                                    <td class="py-2 text-sm text-gray-600 dark:text-gray-300"><?php echo ucfirst($backup['type']); ?></td>
                                    <td class="py-2 text-sm text-gray-600 dark:text-gray-300"><?php echo htmlspecialchars($backup['version']); ?></td>
                                    <td class="py-2 text-sm text-gray-600 dark:text-gray-300"><?php echo date('M j, Y H:i', strtotime($backup['created'])); ?></td>
                                    <td class="py-2 text-sm text-gray-600 dark:text-gray-300"><?php echo number_format($backup['size'] / 1024 / 1024, 1); ?> MB</td>
                                    <td class="py-2">
                                        <div class="flex space-x-2">
                                            <button
                                                onclick="restoreBackup('<?php echo htmlspecialchars($backup['name']); ?>')"
                                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                                            >
                                                Restore
                                            </button>
                                            <button
                                                onclick="deleteBackup('<?php echo htmlspecialchars($backup['name']); ?>')"
                                                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm"
                                                title="Delete this backup"
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function showStatus(message, type = 'info') {
    const statusDiv = document.getElementById('statusMessage');
    const typeClasses = {
        'info': 'bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200',
        'success': 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200',
        'error': 'bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200',
        'warning': 'bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200'
    };

    statusDiv.className = `mb-6 p-4 border rounded-lg ${typeClasses[type]}`;
    statusDiv.innerHTML = message;
    statusDiv.classList.remove('hidden');

    // Auto-hide after 5 seconds for success messages
    if (type === 'success') {
        setTimeout(() => {
            statusDiv.classList.add('hidden');
        }, 5000);
    }
}

function checkAllUpdates() {
    showStatus('🔄 Checking for updates...', 'info');

    // Check dashboard updates
    fetch('updates.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            'action': 'check_dashboard_updates',
            'csrf_token': '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Check custom app updates
            return fetch('updates.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: new URLSearchParams({
                    'action': 'check_app_updates',
                    'csrf_token': '<?php echo generateCSRFToken(); ?>'
                })
            });
        } else {
            throw new Error(data.message);
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus('✅ Update check completed. Refresh page to see results.', 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            throw new Error(data.message);
        }
    })
    .catch(error => {
        showStatus(`❌ Error checking updates: ${error.message}`, 'error');
    });
}

function createBackup(type, appId = null) {
    const typeText = type === 'dashboard' ? 'dashboard' : `app (${appId})`;
    showStatus(`🔄 Creating backup for ${typeText}...`, 'info');

    const params = {
        'action': 'create_backup',
        'type': type,
        'csrf_token': '<?php echo generateCSRFToken(); ?>'
    };

    if (appId) {
        params.app_id = appId;
    }

    fetch('updates.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(params)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus(`✅ Backup created successfully: ${data.backup_path}`, 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showStatus(`❌ Backup failed: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        showStatus(`❌ Error creating backup: ${error.message}`, 'error');
    });
}

function updateDashboard() {
    if (!confirm('Are you sure you want to update the dashboard? This will create a backup first.')) {
        return;
    }

    showStatus('🔄 Starting dashboard update process...', 'info');

    // First create a backup
    createBackup('dashboard');

    // In a real implementation, this would:
    // 1. Download the update
    // 2. Verify integrity
    // 3. Apply the update
    // 4. Verify success

    setTimeout(() => {
        showStatus('⚠️ Dashboard update functionality is not yet implemented. This is a demo interface.', 'warning');
    }, 3000);
}

function updateApp(appId) {
    if (!confirm(`Are you sure you want to update ${appId}? This will create a backup first.`)) {
        return;
    }

    showStatus(`🔄 Starting update process for ${appId}...`, 'info');

    // First create a backup
    createBackup('app', appId);

    // In a real implementation, this would call the app's update handler
    setTimeout(() => {
        showStatus(`⚠️ Custom app update functionality is not yet implemented. This is a demo interface.`, 'warning');
    }, 3000);
}

function restoreBackup(backupName) {
    if (!confirm(`Are you sure you want to restore from backup: ${backupName}? This will overwrite current files.`)) {
        return;
    }

    showStatus(`🔄 Restoring from backup: ${backupName}...`, 'info');

    // In a real implementation, this would:
    // 1. Verify backup integrity
    // 2. Stop services if needed
    // 3. Restore files
    // 4. Restart services
    // 5. Verify restoration

    setTimeout(() => {
        showStatus('⚠️ Backup restoration functionality is not yet implemented. This is a demo interface.', 'warning');
    }, 3000);
}

function toggleDemoMode() {
    const btn = document.getElementById('demoModeBtn');
    const currentMode = btn.textContent.includes('Disable');
    const newMode = !currentMode;

    btn.disabled = true;
    btn.innerHTML = '⏳ Updating...';

    showStatus(`🔄 ${newMode ? 'Enabling' : 'Disabling'} demo mode...`, 'info');

    fetch('updates.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            'action': 'toggle_demo_mode',
            'demo_mode': newMode.toString(),
            'csrf_token': '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus(`✅ ${data.message}. Refreshing page...`, 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showStatus(`❌ Error: ${data.message}`, 'error');
            btn.disabled = false;
            btn.innerHTML = currentMode ? 'Disable Demo' : 'Enable Demo';
        }
    })
    .catch(error => {
        showStatus(`❌ Error toggling demo mode: ${error.message}`, 'error');
        btn.disabled = false;
        btn.innerHTML = currentMode ? 'Disable Demo' : 'Enable Demo';
    });
}

function deleteBackup(backupName) {
    if (!confirm(`Are you sure you want to delete backup: ${backupName}? This action cannot be undone.`)) {
        return;
    }

    showStatus(`🔄 Deleting backup: ${backupName}...`, 'info');

    fetch('updates.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            'action': 'delete_backup',
            'backup_name': backupName,
            'csrf_token': '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus(`✅ Backup deleted successfully. Refreshing page...`, 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showStatus(`❌ Error deleting backup: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        showStatus(`❌ Error deleting backup: ${error.message}`, 'error');
    });
}

function cleanupBackups() {
    if (!confirm('Are you sure you want to clean up old backups? This will delete backups older than the retention period and those exceeding size limits.')) {
        return;
    }

    showStatus('🔄 Running backup cleanup...', 'info');

    fetch('updates.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            'action': 'cleanup_backups',
            'csrf_token': '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.deleted_count > 0) {
                showStatus(`✅ ${data.message} Refreshing page...`, 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                showStatus('✅ No backups needed cleanup. All backups are within retention limits.', 'success');
            }
        } else {
            showStatus(`❌ Cleanup failed: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        showStatus(`❌ Error during cleanup: ${error.message}`, 'error');
    });
}

function refreshBackups() {
    showStatus('🔄 Refreshing backup list...', 'info');
    setTimeout(() => location.reload(), 1000);
}
</script>

<?php include '../templates/footer.php'; ?>
