<?php
/**
 * Icon Management System
 * Secure admin interface for managing application icons
 *
 * Security: Admin access required
 */

require_once '../config.php';
require_once '../includes/icons.php';

// Security: Require admin authentication
requireAuth();
requireAdmin();

// Icon replacement mappings - Replace emojis with icon system calls
$iconReplacements = [
    // Basic emoji replacements
    '<span class="text-2xl">🤖</span>' => '<?php echo getIcon("app-logo", "text-2xl"); ?>',
    '<div class="text-6xl mb-6">🤖</div>' => '<div class="mb-6"><?php echo getIcon("app-logo", "text-6xl"); ?></div>',
    '<div class="text-3xl mr-4">👤</div>' => '<div class="mr-4"><?php echo getIcon("user-profile", "text-3xl"); ?></div>',
    '<div class="text-2xl mr-4">👤</div>' => '<div class="mr-4"><?php echo getIcon("user-profile", "text-2xl"); ?></div>',
    '<div class="text-2xl mr-4">👥</div>' => '<div class="mr-4"><?php echo getIcon("user-group", "text-2xl"); ?></div>',
    '<div class="text-3xl mr-4">👥</div>' => '<div class="mr-4"><?php echo getIcon("user-group", "text-3xl"); ?></div>',
    '<div class="text-3xl mr-4">👑</div>' => '<div class="mr-4"><?php echo getIcon("admin-crown", "text-3xl"); ?></div>',
    '<div class="text-3xl mr-4">🛡️</div>' => '<div class="mr-4"><?php echo getIcon("moderator-shield", "text-3xl"); ?></div>',

    // Simple text replacements
    '🔑 You have administrative privileges.' => '<?php echo getIcon("admin-key", "text-base"); ?> You have administrative privileges.',
    'Welcome back, <?php echo htmlspecialchars($currentUser["username"]); ?>! 👋' => 'Welcome back, <?php echo htmlspecialchars($currentUser["username"]); ?>! <?php echo getIcon("welcome-wave", "text-base"); ?>'
];

/**
 * Get list of PHP files to process
 */
function getPhpFiles() {
    $files = [];

    // Main files (exclude CLI script)
    $mainFiles = array_filter(glob('../*.php'), function($file) {
        return basename($file) !== 'replace_icons_cli.php';
    });
    $files = array_merge($files, $mainFiles);

    // Admin files (exclude this icons.php file)
    $adminFiles = array_filter(glob('*.php'), function($file) {
        return basename($file) !== 'icons.php';
    });
    $files = array_merge($files, $adminFiles);

    // Template files
    $templateFiles = glob('../templates/*.php');
    $files = array_merge($files, $templateFiles);

    // Include files (exclude icons.php)
    $includeFiles = array_filter(glob('../includes/*.php'), function($file) {
        return basename($file) !== 'icons.php';
    });
    $files = array_merge($files, $includeFiles);

    return $files;
}

/**
 * Replace icons in a file
 */
function replaceIconsInFile($filePath, $replacements) {
    if (!file_exists($filePath)) {
        return false;
    }

    $content = file_get_contents($filePath);
    $originalContent = $content;

    foreach ($replacements as $search => $replace) {
        $content = str_replace($search, $replace, $content);
    }

    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        return true;
    }

    return false;
}

// Handle AJAX requests for icon operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }

    switch ($_POST['action']) {
        case 'run_replacement':
            $files = getPhpFiles();
            $processedFiles = 0;
            $modifiedFiles = 0;

            foreach ($files as $file) {
                if (replaceIconsInFile($file, $iconReplacements)) {
                    $modifiedFiles++;
                }
                $processedFiles++;
            }

            logActivity('icons.replaced', 'Icon replacement executed', [
                'files_processed' => $processedFiles,
                'files_modified' => $modifiedFiles,
                'admin' => $_SESSION['username']
            ]);

            echo json_encode([
                'success' => true,
                'message' => "Icon replacement complete! Processed $processedFiles files, modified $modifiedFiles files.",
                'processed' => $processedFiles,
                'modified' => $modifiedFiles
            ]);
            exit;

        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            exit;
    }
}

// Web interface for icon management
$pageTitle = 'Icon Management';
include '../templates/header.php';

$iconList = getIconList();
?>

<div class="max-w-7xl mx-auto p-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>"><?php echo getIcon('design-palette', 'text-3xl'); ?> Icon Management</h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">Manage application icons</p>
        </div>
        <div class="flex space-x-3">
            <a href="settings.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">Back to Settings</a>
            <button
                id="runReplacementBtn"
                onclick="runIconReplacement()"
                class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200"
            >
                <?php echo getIcon('update-refresh', 'text-base'); ?> Run Icon Replacement
            </button>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="statusMessage" class="hidden mb-6"></div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <?php foreach ($iconList as $icon): ?>
            <div class="<?php echo getThemeComponentClasses('card'); ?> p-4 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium"><?php echo $icon['name']; ?></h3>
                    <span class="<?php echo $icon['exists'] ? 'text-green-600' : 'text-yellow-600'; ?>">
                        <?php echo $icon['exists'] ? getIcon('success-check', 'text-base') : getIcon('folder-manage', 'text-base'); ?>
                    </span>
                </div>

                <div class="flex items-center space-x-4 mb-2">
                    <div class="text-2xl">
                        <?php echo getIcon($icon['name'], 'text-2xl'); ?>
                    </div>
                    <div class="text-sm text-gray-600">
                        <?php echo $icon['exists'] ? 'Custom Icon' : 'Emoji Fallback'; ?>
                    </div>
                </div>

                <div class="text-xs text-gray-500">
                    <div>File: <?php echo $icon['file']; ?></div>
                    <div>Fallback: <?php echo $icon['fallback']; ?></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
        <h2 class="text-lg font-semibold mb-2"><?php echo getIcon('document-note', 'text-lg'); ?> Instructions</h2>
        <ol class="list-decimal list-inside space-y-1 text-sm">
            <li>Add your custom SVG icons to <code>assets/images/icons/</code></li>
            <li>Use the exact filenames shown above</li>
            <li>Icons will automatically replace emoji fallbacks</li>
            <li>SVG icons support theme colors via <code>currentColor</code></li>
            <li>Use the "Run Icon Replacement" button to convert existing emoji icons to getIcon() calls</li>
        </ol>
    </div>
</div>

<script>
// Icon HTML for JavaScript use
const refreshIcon = '<?php echo addslashes(getIcon("update-refresh", "text-base")); ?>';
const loadingIcon = '<?php echo addslashes(getIcon("clock-time", "text-base")); ?>';
const processingIcon = '<?php echo addslashes(getIcon("update-refresh", "text-base")); ?>';
const successIcon = '<?php echo addslashes(getIcon("success-check", "text-base")); ?>';
const errorIcon = '<?php echo addslashes(getIcon("error-x", "text-base")); ?>';
const folderIcon = '<?php echo addslashes(getIcon("folder-manage", "text-base")); ?>';
const editIcon = '<?php echo addslashes(getIcon("document-note", "text-base")); ?>';

function runIconReplacement() {
    const btn = document.getElementById('runReplacementBtn');
    const statusDiv = document.getElementById('statusMessage');

    // Disable button and show loading
    btn.disabled = true;
    btn.innerHTML = loadingIcon + ' Processing...';

    // Show status message
    statusDiv.className = 'mb-6 p-4 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg';
    statusDiv.innerHTML = '<p class="<?php echo getThemeComponentClasses('text-primary'); ?>">' + processingIcon + ' Running icon replacement...</p>';
    statusDiv.classList.remove('hidden');

    // Make AJAX request
    fetch('icons.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            'action': 'run_replacement',
            'csrf_token': '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusDiv.className = 'mb-6 p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg';
            statusDiv.innerHTML = `
                <h3 class="font-semibold <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">${successIcon} Icon Replacement Complete!</h3>
                <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">${data.message}</p>
                <div class="mt-2 text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                    <p>${folderIcon} Files processed: ${data.processed}</p>
                    <p>${editIcon} Files modified: ${data.modified}</p>
                </div>
            `;
        } else {
            statusDiv.className = 'mb-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg';
            statusDiv.innerHTML = `<p class="text-red-800 dark:text-red-200">${errorIcon} Error: ${data.message}</p>`;
        }
    })
    .catch(error => {
        statusDiv.className = 'mb-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg';
        statusDiv.innerHTML = '<p class="text-red-800 dark:text-red-200">' + errorIcon + ' Network error occurred</p>';
    })
    .finally(() => {
        // Re-enable button
        btn.disabled = false;
        btn.innerHTML = refreshIcon + ' Run Icon Replacement';
    });
}
</script>

<?php include '../templates/footer.php'; ?>