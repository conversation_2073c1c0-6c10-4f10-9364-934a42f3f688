<?php
/**
 * Application Settings Management Page
 * Admin interface for managing application settings
 */

require_once '../config.php';
require_once '../auth.php';
require_once '../includes/roles.php';
require_once '../includes/settings.php';

// Require admin access
requireAdmin();

// Handle form submission
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_settings':
                // Get current settings to preserve custom HTML content
                $currentSettings = loadSettings();

                $newSettings = [
                    'app_name' => sanitizeInput($_POST['app_name'] ?? ''),
                    'timezone' => sanitizeInput($_POST['timezone'] ?? ''),
                    'maintenance_mode' => isset($_POST['maintenance_mode']),
                    'registration_enabled' => isset($_POST['registration_enabled']),
                    'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
                    'session_timeout' => (int)($_POST['session_timeout'] ?? 3600),
                    'password_min_length' => (int)($_POST['password_min_length'] ?? 8),
                    'require_email_verification' => isset($_POST['require_email_verification']),
                    'default_user_role' => sanitizeInput($_POST['default_user_role'] ?? 'user'),
                    'admin_email' => sanitizeInput($_POST['admin_email'] ?? ''),
                    'site_description' => sanitizeInput($_POST['site_description'] ?? ''),
                    'homepage_type' => sanitizeInput($_POST['homepage_type'] ?? 'default'),
                    'dashboard_homepage_app' => sanitizeInput($_POST['dashboard_homepage_app'] ?? ''),
                    'log_retention_days' => (int)($_POST['log_retention_days'] ?? 30),
                    'enable_logging' => isset($_POST['enable_logging']),

                    // Preserve custom homepage HTML content
                    'custom_homepage_html' => $currentSettings['custom_homepage_html'] ?? ''
                ];

                if (saveSettings($newSettings)) {
                    $message = 'Settings updated successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update settings.';
                    $messageType = 'error';
                }
                break;
                
            case 'reset_settings':
                if (resetSettingsToDefaults()) {
                    $message = 'Settings reset to defaults successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to reset settings.';
                    $messageType = 'error';
                }
                break;
                
            case 'clear_logs':
                $days = (int)($_POST['clear_days'] ?? 30);
                if (clearOldLogs($days)) {
                    $message = "Logs older than {$days} days cleared successfully.";
                    $messageType = 'success';
                } else {
                    $message = 'Failed to clear logs.';
                    $messageType = 'error';
                }
                break;

            case 'save_custom_homepage':
                // Ensure we're sending JSON response
                header('Content-Type: application/json');

                try {
                    $customHtml = $_POST['custom_homepage_html'] ?? '';
                    $currentSettings = loadSettings();
                    $currentSettings['custom_homepage_html'] = $customHtml;

                    // Always set homepage type to custom when saving HTML (if HTML is not empty)
                    // This ensures the custom homepage is activated when HTML is saved
                    if (!empty($customHtml)) {
                        $currentSettings['homepage_type'] = 'custom';
                    }

                    if (saveSettings($currentSettings)) {
                        echo json_encode([
                            'success' => true,
                            'message' => 'Custom homepage HTML saved successfully.',
                            'character_count' => strlen($customHtml),
                            'homepage_type' => $currentSettings['homepage_type'],
                            'html_preserved' => true
                        ]);
                        exit;
                    } else {
                        echo json_encode([
                            'success' => false,
                            'message' => 'Failed to save custom homepage HTML.'
                        ]);
                        exit;
                    }
                } catch (Exception $e) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Error: ' . $e->getMessage()
                    ]);
                    exit;
                }
                break;



            case 'get_custom_homepage':
                $currentSettings = loadSettings();
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'html_content' => $currentSettings['custom_homepage_html'] ?? '',
                    'homepage_type' => $currentSettings['homepage_type'] ?? 'default'
                ]);
                exit;
                break;
        }
    }
}

// Load current settings
$settings = loadSettings();
$logStats = getLogStatistics();

// Available timezones
$timezones = [
    'UTC' => 'UTC',
    'America/New_York' => 'Eastern Time',
    'America/Chicago' => 'Central Time',
    'America/Denver' => 'Mountain Time',
    'America/Los_Angeles' => 'Pacific Time',
    'Europe/London' => 'London',
    'Europe/Paris' => 'Paris',
    'Asia/Tokyo' => 'Tokyo',
    'Asia/Shanghai' => 'Shanghai',
    'Australia/Sydney' => 'Sydney'
];

// Set page title
$pageTitle = 'Application Settings';

// Include header
include '../templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Application Settings</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Configure application behavior and security</p>
        </div>
        <div class="flex space-x-3">
            <a href="users.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">User Management</a>
            <a href="logs.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">View Logs</a>
        </div>
    </div>
    
    <!-- Display Messages -->
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>
    
    <!-- Settings Form -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">General Settings</h2>
        </div>
        
        <form id="mainSettingsForm" method="POST" class="p-6 space-y-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="action" value="update_settings">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Application Name -->
                <div>
                    <label for="app_name" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                        Application Name
                    </label>
                    <input 
                        type="text" 
                        id="app_name" 
                        name="app_name" 
                        class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                        value="<?php echo htmlspecialchars($settings['app_name']); ?>"
                        required
                    >
                </div>
                
                <!-- Timezone -->
                <div>
                    <label for="timezone" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                        Timezone
                    </label>
                    <select id="timezone" name="timezone" class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg">
                        <?php foreach ($timezones as $value => $label): ?>
                            <option value="<?php echo $value; ?>" <?php echo $settings['timezone'] === $value ? 'selected' : ''; ?>>
                                <?php echo $label; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Admin Email -->
                <div>
                    <label for="admin_email" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Admin Email
                    </label>
                    <input 
                        type="email" 
                        id="admin_email" 
                        name="admin_email" 
                        class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                        value="<?php echo htmlspecialchars($settings['admin_email']); ?>"
                    >
                </div>
                
                <!-- Default User Role -->
                <div>
                    <label for="default_user_role" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Default User Role
                    </label>
                    <select id="default_user_role" name="default_user_role" class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg">
                        <?php foreach (getAvailableRoles() as $role): ?>
                            <?php if ($role !== 'admin'): // Don't allow admin as default ?>
                                <option value="<?php echo $role; ?>" <?php echo $settings['default_user_role'] === $role ? 'selected' : ''; ?>>
                                    <?php echo getRoleDisplayName($role); ?>
                                </option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <!-- Site Description -->
            <div>
                <label for="site_description" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Site Description
                </label>
                <textarea
                    id="site_description"
                    name="site_description"
                    rows="3"
                    class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                ><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
            </div>

            <!-- Homepage Settings -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Homepage Settings</h3>

                <div class="space-y-4">
                    <!-- Homepage Type Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-900 dark:text-white mb-3">
                            Public Homepage Type
                        </label>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input
                                    type="radio"
                                    id="homepage_default"
                                    name="homepage_type"
                                    value="default"
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                                    <?php echo ($settings['homepage_type'] ?? 'default') === 'default' ? 'checked' : ''; ?>
                                    onchange="toggleHomepageEditor()"
                                >
                                <label for="homepage_default" class="ml-2 block text-sm text-gray-900 dark:text-white mb-0">
                                    Default Homepage (with login form and navigation)
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input
                                    type="radio"
                                    id="homepage_custom"
                                    name="homepage_type"
                                    value="custom"
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                                    <?php echo ($settings['homepage_type'] ?? 'default') === 'custom' ? 'checked' : ''; ?>
                                    onchange="toggleHomepageEditor()"
                                >
                                <label for="homepage_custom" class="ml-2 block text-sm text-gray-900 dark:text-white mb-0">
                                    Custom HTML Homepage
                                </label>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                            Choose between the default dashboard homepage or a completely custom HTML page
                        </p>
                    </div>

                    <!-- Custom Homepage Editor Controls -->
                    <div id="customHomepageControls" class="<?php echo ($settings['homepage_type'] ?? 'default') === 'custom' ? '' : 'hidden'; ?>">
                        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">Custom Homepage HTML</h4>
                                <div class="space-x-2">
                                    <button
                                        type="button"
                                        onclick="openHomepageEditor()"
                                        class="<?php echo getThemeComponentClasses('button-primary'); ?> px-3 py-1 text-sm rounded-lg font-medium transition duration-200"
                                    >
                                        <?php echo getIcon('document-note', 'text-sm'); ?> Edit HTML
                                    </button>
                                    <?php if (!empty($settings['custom_homepage_html'])): ?>
                                        <button
                                            type="button"
                                            onclick="previewCustomHomepage()"
                                            class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-1 text-sm rounded-lg font-medium transition duration-200"
                                        >
                                            <?php echo getIcon('view-eye', 'text-sm'); ?> Preview
                                        </button>
                                        <button
                                            type="button"
                                            onclick="clearCustomHomepage()"
                                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 text-sm rounded-lg font-medium transition duration-200"
                                        >
                                            <?php echo getIcon('remove-x', 'text-sm'); ?> Clear HTML
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">
                                <?php if (!empty($settings['custom_homepage_html'])): ?>
                                    <div class="flex items-center space-x-2">
                                        <?php echo getIcon('success-check', 'text-sm'); ?>
                                        <span>Custom HTML configured (<?php echo number_format(strlen($settings['custom_homepage_html'])); ?> characters)</span>
                                    </div>
                                <?php else: ?>
                                    <div class="flex items-center space-x-2">
                                        <?php echo getIcon('warning-alert', 'text-sm'); ?>
                                        <span>No custom HTML configured - click "Edit HTML" to add content</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- HTML Status Display (visible when HTML exists but default mode is selected) -->
                    <div class="mt-3" style="<?php echo (!empty($settings['custom_homepage_html']) && ($settings['homepage_type'] ?? 'default') === 'default') ? '' : 'display: none;'; ?>">
                        <div class="text-sm text-gray-600 dark:text-gray-300">
                            <?php if (!empty($settings['custom_homepage_html'])): ?>
                                <div class="flex items-center space-x-2">
                                    <?php echo getIcon('info-circle', 'text-sm'); ?>
                                    <span>Custom HTML saved (<?php echo number_format(strlen($settings['custom_homepage_html'])); ?> characters) - switch to "Custom HTML Homepage" to activate</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Dashboard Homepage Settings -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Dashboard Homepage Settings</h3>

                <div>
                    <label for="dashboard_homepage_app" class="block text-sm font-medium text-gray-900 dark:text-white mb-3">
                        Dashboard Homepage App
                    </label>
                    <select id="dashboard_homepage_app" name="dashboard_homepage_app" class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg">
                        <option value="" <?php echo empty($settings['dashboard_homepage_app']) ? 'selected' : ''; ?>>
                            Default Dashboard (Welcome page)
                        </option>
                        <?php
                        // Get available apps
                        $appsDir = '../app';
                        if (is_dir($appsDir)) {
                            $apps = array_filter(scandir($appsDir), function($item) use ($appsDir) {
                                return $item !== '.' && $item !== '..' && is_dir($appsDir . '/' . $item);
                            });

                            foreach ($apps as $app) {
                                $appConfigFile = $appsDir . '/' . $app . '/app.json';
                                $appName = $app;

                                if (file_exists($appConfigFile)) {
                                    $appConfig = json_decode(file_get_contents($appConfigFile), true);
                                    if (isset($appConfig['name'])) {
                                        $appName = $appConfig['name'];
                                    }
                                }

                                $selected = ($settings['dashboard_homepage_app'] ?? '') === $app ? 'selected' : '';
                                echo "<option value=\"{$app}\" {$selected}>{$appName}</option>";
                            }
                        }
                        ?>
                    </select>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        Choose which app should be the homepage when users log into the dashboard. If an app is selected, the main menu will show "Dashboard" instead of the app name.
                    </p>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Security Settings</h3>

                <div class="grid md:grid-cols-3 gap-6">
                    <div>
                        <label for="max_login_attempts" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Max Login Attempts
                        </label>
                        <input
                            type="number"
                            id="max_login_attempts"
                            name="max_login_attempts"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo $settings['max_login_attempts']; ?>"
                            min="1" max="20"
                        >
                    </div>

                    <div>
                        <label for="session_timeout" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Session Timeout (seconds)
                        </label>
                        <input
                            type="number"
                            id="session_timeout"
                            name="session_timeout"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo $settings['session_timeout']; ?>"
                            min="300" max="86400"
                        >
                    </div>

                    <div>
                        <label for="password_min_length" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                            Minimum Password Length
                        </label>
                        <input
                            type="number"
                            id="password_min_length"
                            name="password_min_length"
                            class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                            value="<?php echo $settings['password_min_length']; ?>"
                            min="4" max="50"
                        >
                    </div>
                </div>
            </div>
            
            <!-- Feature Toggles -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Feature Settings</h3>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="maintenance_mode"
                            name="maintenance_mode"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                            <?php echo $settings['maintenance_mode'] ? 'checked' : ''; ?>
                        >
                        <label for="maintenance_mode" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Maintenance Mode (disables public access)
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="registration_enabled"
                            name="registration_enabled"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                            <?php echo $settings['registration_enabled'] ? 'checked' : ''; ?>
                        >
                        <label for="registration_enabled" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Enable User Registration
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="require_email_verification"
                            name="require_email_verification"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                            <?php echo $settings['require_email_verification'] ? 'checked' : ''; ?>
                        >
                        <label for="require_email_verification" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Require Email Verification
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="enable_logging"
                            name="enable_logging"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                            <?php echo $settings['enable_logging'] ? 'checked' : ''; ?>
                        >
                        <label for="enable_logging" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Enable Activity Logging
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Logging Settings -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Logging Settings</h3>
                
                <div>
                    <label for="log_retention_days" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Log Retention (days)
                    </label>
                    <input 
                        type="number" 
                        id="log_retention_days" 
                        name="log_retention_days" 
                        class="<?php echo getThemeComponentClasses('input'); ?> max-w-xs px-3 py-2 rounded-lg"
                        value="<?php echo $settings['log_retention_days']; ?>"
                        min="1" max="365"
                    >
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Logs older than this will be automatically deleted</p>
                </div>
            </div>



            <!-- Submit Buttons -->
            <div class="border-t pt-6 flex justify-between">
                <button
                    type="button"
                    onclick="if(confirm('Reset all settings to defaults?')) { document.getElementById('resetForm').submit(); }"
                    class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                >
                    Reset to Defaults
                </button>

                <div class="space-x-3">
                    <button type="submit" class="btn-primary">
                        Save Settings
                    </button>
                    <button type="button" onclick="location.reload()" class="btn-secondary">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Log Management -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Log Management</h2>
        </div>
        
        <div class="p-6">
            <div class="grid md:grid-cols-3 gap-6 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo $logStats['total_activities'] ?? 0; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Total Activities</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400"><?php echo $logStats['total_errors'] ?? 0; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Total Errors</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo $logStats['recent_activities'] ?? 0; ?></div>
                    <div class="text-sm text-gray-600 dark:text-gray-300">Recent Activities (24h)</div>
                </div>
            </div>
            
            <form method="POST" class="flex items-end space-x-3">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="clear_logs">
                
                <div>
                    <label for="clear_days" class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Clear logs older than (days)
                    </label>
                    <input 
                        type="number" 
                        id="clear_days" 
                        name="clear_days" 
                        class="<?php echo getThemeComponentClasses('input'); ?> px-3 py-2 rounded-lg"
                        value="30"
                        min="1" max="365"
                        style="width: 120px;"
                    >
                </div>
                
                <button 
                    type="submit" 
                    onclick="return confirm('Are you sure you want to clear old logs?')"
                    class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                >
                    Clear Old Logs
                </button>
            </form>
        </div>
    </div>

    <!-- Update Management -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Update Management</h2>
        </div>

        <div class="p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">System Updates</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Manage dashboard boilerplate and custom application updates with automated backup and rollback functionality.
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('update-refresh', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Dashboard Updates</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Core boilerplate updates</div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('mobile-phone', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Custom App Updates</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Integrated application updates</div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('backup-disk', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Automatic Backups</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Safe update process</div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('rollback-arrow', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Rollback Support</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Easy restoration</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">🚀 Features</h4>
                        <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                            <li>• Dual-layer update system for dashboard and custom apps</li>
                            <li>• Automatic backup creation before updates</li>
                            <li>• Version compatibility checking</li>
                            <li>• One-click rollback functionality</li>
                            <li>• Update API for custom app integration</li>
                        </ul>
                    </div>
                </div>

                <div class="ml-6">
                    <a
                        href="updates.php"
                        class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-3 rounded-lg font-medium transition duration-200 inline-flex items-center space-x-2"
                    >
                        <?php echo getIcon('update-refresh', 'text-base'); ?>
                        <span>Manage Updates</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Icon Management -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Icon Management</h2>
        </div>

        <div class="p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Application Icons</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Manage and customize application icons. Replace emoji icons with custom SVG icons for a professional appearance.
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('design-palette', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Custom Icon System</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Replace emojis with SVG icons</div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('update-refresh', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Automated Replacement</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">One-click icon conversion</div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('target-aim', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Theme Compatible</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Works in light and dark modes</div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div><?php echo getIcon('folder-manage', 'text-2xl'); ?></div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Easy Management</div>
                                <div class="text-sm text-gray-600 dark:text-gray-300">Simple file-based system</div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-4">
                        <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">📋 Quick Start</h4>
                        <ol class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                            <li>1. Add your custom SVG icons to <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded">assets/images/icons/</code></li>
                            <li>2. Use the Icon Management interface to view available icons</li>
                            <li>3. Run the automated replacement to convert emojis to icons</li>
                            <li>4. Icons automatically work with your theme colors</li>
                        </ol>
                    </div>
                </div>

                <div class="ml-6">
                    <a
                        href="icons.php"
                        class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-3 rounded-lg font-medium transition duration-200 inline-flex items-center space-x-2"
                    >
                        <?php echo getIcon('design-palette', 'text-base'); ?>
                        <span>Manage Icons</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for reset -->
<form id="resetForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="reset_settings">
</form>

<!-- Homepage HTML Editor Modal -->
<div id="homepageEditorModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closeHomepageEditor()"></div>

        <!-- Modal panel -->
        <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform <?php echo getThemeComponentClasses('card'); ?> shadow-xl rounded-lg">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium <?php echo getThemeComponentClasses('text-primary'); ?>">
                    <?php echo getIcon('document-note', 'text-lg'); ?> Custom Homepage HTML Editor
                </h3>
                <button type="button" onclick="closeHomepageEditor()" class="<?php echo getThemeComponentClasses('nav-link'); ?> p-2 rounded-md">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm <?php echo getThemeComponentClasses('text-secondary'); ?>">
                    Enter your complete HTML code for the custom homepage. This will replace the default homepage entirely.
                    Include all necessary HTML structure, CSS, and JavaScript.
                </p>
            </div>

            <form id="homepageEditorForm" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="save_custom_homepage">

                <div class="mb-4">
                    <label for="custom_homepage_html" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                        HTML Content
                    </label>
                    <textarea
                        id="custom_homepage_html"
                        name="custom_homepage_html"
                        rows="20"
                        class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg font-mono text-sm"
                        placeholder="<!DOCTYPE html>
<html lang=&quot;en&quot;>
<head>
    <meta charset=&quot;UTF-8&quot;>
    <meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1.0&quot;>
    <title>Your Custom Homepage</title>
    <style>
        /* Your CSS here */
    </style>
</head>
<body>
    <!-- Your HTML content here -->
    <h1>Welcome to My Custom Homepage</h1>
    <p>This is a completely custom homepage.</p>
</body>
</html>"
                    ><?php echo htmlspecialchars($settings['custom_homepage_html'] ?? ''); ?></textarea>
                </div>

                <div class="flex justify-end">
                    <div class="flex space-x-3">
                        <button
                            type="button"
                            onclick="closeHomepageEditor()"
                            class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            class="<?php echo getThemeComponentClasses('button-primary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200"
                        >
                            <?php echo getIcon('success-check', 'text-sm'); ?> Save HTML
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>


// Homepage Editor Functions
function toggleHomepageEditor() {
    const customRadio = document.getElementById('homepage_custom');
    const controls = document.getElementById('customHomepageControls');

    if (customRadio.checked) {
        controls.classList.remove('hidden');
    } else {
        controls.classList.add('hidden');
    }

    // Update the always-visible status display based on current mode
    // Get character count from the current status display if available
    const currentStatus = document.querySelector('#customHomepageControls .text-sm.text-gray-600 span');
    let characterCount = 0;
    if (currentStatus && currentStatus.textContent.includes('characters')) {
        const match = currentStatus.textContent.match(/\(([0-9,]+) characters\)/);
        if (match) {
            characterCount = parseInt(match[1].replace(/,/g, ''));
        }
    }

    updateAlwaysVisibleStatus(characterCount);
}

function openHomepageEditor() {
    // Load current HTML content before opening modal
    fetch('settings.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=get_custom_homepage&csrf_token=<?php echo generateCSRFToken(); ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update textarea with current HTML content
            const textarea = document.getElementById('custom_homepage_html');
            if (textarea) {
                textarea.value = data.html_content || '';
            }
        }

        // Open modal
        const modal = document.getElementById('homepageEditorModal');
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    })
    .catch(error => {
        console.error('Error loading HTML content:', error);
        // Still open modal even if loading fails
        const modal = document.getElementById('homepageEditorModal');
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });
}

function closeHomepageEditor() {
    const modal = document.getElementById('homepageEditorModal');
    modal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function previewCustomHomepage() {
    // Open preview in new window
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '../preview_custom_homepage.php';
    form.target = '_blank';

    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '<?php echo generateCSRFToken(); ?>';

    form.appendChild(csrfInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function updateAlwaysVisibleStatus(characterCount) {
    // Update or hide the always-visible status based on current mode
    const customRadio = document.getElementById('homepage_custom');
    const defaultRadio = document.getElementById('homepage_default');
    const statusContainer = document.querySelector('.mt-3 .text-sm.text-gray-600');

    if (statusContainer) {
        if (characterCount > 0 && defaultRadio && defaultRadio.checked) {
            // Show status when HTML exists but default mode is selected
            statusContainer.innerHTML = `
                <div class="flex items-center space-x-2">
                    <?php echo addslashes(getIcon('info-circle', 'text-sm')); ?>
                    <span>Custom HTML saved (${characterCount.toLocaleString()} characters) - switch to "Custom HTML Homepage" to activate</span>
                </div>
            `;
            statusContainer.parentElement.style.display = 'block';
        } else {
            // Hide status when in custom mode or no HTML
            statusContainer.parentElement.style.display = 'none';
        }
    }
}

function autoSaveMainForm() {
    // Automatically save the main settings form to persist homepage type selection
    const mainForm = document.getElementById('mainSettingsForm');

    if (mainForm) {
        const formData = new FormData(mainForm);

        // Make sure we're sending the update_settings action
        formData.set('action', 'update_settings');

        fetch('settings.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            // Homepage settings auto-saved successfully
        })
        .catch(error => {
            console.error('Auto-save failed:', error);
        });
    }
}

function autoClickSaveButton() {
    // Simple and reliable: just click the main "Save Settings" button
    // This uses the exact same save logic that works when clicked manually
    const saveButton = document.querySelector('button[type="submit"].btn-primary');
    if (saveButton) {
        saveButton.click();
    }
}

function clearCustomHomepage() {
    if (!confirm('Are you sure you want to clear your custom homepage HTML? This action cannot be undone.')) {
        return;
    }

    const formData = new FormData();
    formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
    formData.append('action', 'save_custom_homepage');
    formData.append('custom_homepage_html', '');

    fetch('settings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the status display
            const statusDiv = document.querySelector('#customHomepageControls .text-sm.text-gray-600');
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <?php echo addslashes(getIcon('warning-alert', 'text-sm')); ?>
                        <span>No custom HTML configured - click "Edit HTML" to add content</span>
                    </div>
                `;
            }

            // Remove preview and clear buttons
            const previewBtn = document.querySelector('button[onclick="previewCustomHomepage()"]');
            const clearBtn = document.querySelector('button[onclick="clearCustomHomepage()"]');
            if (previewBtn) previewBtn.remove();
            if (clearBtn) clearBtn.remove();

            // Switch to default homepage if currently on custom
            const defaultRadio = document.getElementById('homepage_default');
            if (defaultRadio) {
                defaultRadio.checked = true;
                toggleHomepageEditor();
            }

            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'mb-6 p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg';
            alertDiv.innerHTML = `
                <h3 class="font-semibold text-green-900 dark:text-green-100 mb-2"><?php echo addslashes(getIcon('success-check', 'text-base')); ?> Custom HTML Cleared!</h3>
                <p class="text-green-800 dark:text-green-200">Your custom homepage HTML has been cleared and the default homepage is now active.</p>
            `;

            const container = document.querySelector('.max-w-6xl');
            container.insertBefore(alertDiv, container.firstChild);

            // Remove alert after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        } else {
            alert('Error clearing custom homepage: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while clearing the custom homepage.');
    });
}

// Handle homepage editor form submission
document.getElementById('homepageEditorForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('settings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Always auto-select custom radio button when HTML is saved
            const customRadio = document.getElementById('homepage_custom');
            if (customRadio && data.homepage_type === 'custom') {
                customRadio.checked = true;
                // Trigger the change event to show controls
                toggleHomepageEditor();

                // Update the always-visible status display
                updateAlwaysVisibleStatus(data.character_count);

                // Auto-save by clicking the main settings save button
                autoClickSaveButton();
            }

            // Update the status display - replace the entire status div content
            const statusDiv = document.querySelector('#customHomepageControls .text-sm.text-gray-600');
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <?php echo addslashes(getIcon('success-check', 'text-sm')); ?>
                        <span>Custom HTML configured (${data.character_count.toLocaleString()} characters)</span>
                    </div>
                `;
            }

            // Show preview button if it doesn't exist
            const previewBtn = document.querySelector('button[onclick="previewCustomHomepage()"]');
            if (!previewBtn) {
                const editBtn = document.querySelector('button[onclick="openHomepageEditor()"]');
                const newPreviewBtn = document.createElement('button');
                newPreviewBtn.type = 'button';
                newPreviewBtn.onclick = previewCustomHomepage;
                newPreviewBtn.className = '<?php echo getThemeComponentClasses('button-secondary'); ?> px-3 py-1 text-sm rounded-lg font-medium transition duration-200';
                newPreviewBtn.innerHTML = '<?php echo addslashes(getIcon('view-eye', 'text-sm')); ?> Preview';
                editBtn.parentNode.appendChild(newPreviewBtn);
            }

            // Update the textarea content to match what was saved
            const textarea = document.getElementById('custom_homepage_html');
            if (textarea) {
                textarea.value = formData.get('custom_homepage_html');
            }

            closeHomepageEditor();

            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'mb-6 p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg';

            const message = 'Your custom homepage HTML has been saved and the homepage type has been automatically set to "Custom HTML Homepage". Your custom page is now active!';

            alertDiv.innerHTML = `
                <h3 class="font-semibold text-green-900 dark:text-green-100 mb-2"><?php echo addslashes(getIcon('success-check', 'text-base')); ?> Custom Homepage Saved & Activated!</h3>
                <p class="text-green-800 dark:text-green-200">${message}</p>
            `;

            const container = document.querySelector('.max-w-6xl');
            container.insertBefore(alertDiv, container.firstChild);

            // Remove alert after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        } else {
            alert('Error saving custom homepage: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving the custom homepage.');
    });
});
</script>

<?php include '../templates/footer.php'; ?>
