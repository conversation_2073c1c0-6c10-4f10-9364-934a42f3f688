<?php
/**
 * User Management Page
 * Admin interface for managing users
 */

require_once '../config.php';
require_once '../auth.php';
require_once '../includes/roles.php';

// Require admin access
requireAdmin();

// Handle user actions
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_role':
                $userId = $_POST['user_id'] ?? '';
                $newRole = $_POST['new_role'] ?? '';
                
                if (updateUserRole($userId, $newRole)) {
                    $message = 'User role updated successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update user role.';
                    $messageType = 'error';
                }
                break;
                
            case 'unlock_user':
                $userId = $_POST['user_id'] ?? '';
                if (unlockUserAccount($userId)) {
                    $message = 'User account unlocked successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to unlock user account.';
                    $messageType = 'error';
                }
                break;
                
            case 'delete_user':
                $userId = $_POST['user_id'] ?? '';
                if (deleteUser($userId)) {
                    $message = 'User deleted successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to delete user.';
                    $messageType = 'error';
                }
                break;

            case 'promote_to_admin':
                $userId = $_POST['user_id'] ?? '';
                if (updateUserRole($userId, 'admin')) {
                    $message = 'User promoted to admin successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to promote user to admin.';
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Load users and statistics
$users = loadUsers();
$roleCounts = getUserRoleCounts();
$totalUsers = count($users);

// Set page title
$pageTitle = 'User Management';

// Include header
include '../templates/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">User Management</h1>
            <p class="text-gray-600 dark:text-gray-300 mt-1">Manage user accounts and roles</p>
        </div>
        <div class="flex space-x-3">
            <a href="settings.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">Settings</a>
            <a href="logs.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">View Logs</a>
        </div>
    </div>
    
    <!-- Display Messages -->
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>
    
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('user-group', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Total Users</h3>
                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?php echo $totalUsers; ?></p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('admin-crown', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Admins</h3>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400"><?php echo $roleCounts['admin'] ?? 0; ?></p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('moderator-shield', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Moderators</h3>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400"><?php echo $roleCounts['moderator'] ?? 0; ?></p>
                </div>
            </div>
        </div>

        <div class="<?php echo getThemeComponentClasses('card'); ?> p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="mr-4"><?php echo getIcon('user-profile', 'text-3xl'); ?></div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">Regular Users</h3>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400"><?php echo $roleCounts['user'] ?? 0; ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold <?php echo getThemeComponentClasses('text-primary'); ?>">All Users</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y <?php echo getThemeComponentClasses('divider'); ?>">
                <thead class="<?php echo getThemeComponentClasses('table-header'); ?>">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium <?php echo getThemeComponentClasses('text-muted'); ?> uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium <?php echo getThemeComponentClasses('text-muted'); ?> uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium <?php echo getThemeComponentClasses('text-muted'); ?> uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium <?php echo getThemeComponentClasses('text-muted'); ?> uppercase tracking-wider">Last Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium <?php echo getThemeComponentClasses('text-muted'); ?> uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="<?php echo getThemeComponentClasses('card'); ?> divide-y <?php echo getThemeComponentClasses('divider'); ?>">
                    <?php foreach ($users as $user): ?>
                        <?php
                        $isLocked = isset($user['locked_until']) && $user['locked_until'] && strtotime($user['locked_until']) > time();
                        $userRole = $user['role'] ?? 'user';
                        $isCurrentUser = $user['id'] === $_SESSION['user_id'];
                        ?>
                        <tr class="<?php echo getThemeComponentClasses('table-row'); ?>">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium <?php echo getThemeComponentClasses('table-cell'); ?>">
                                        <?php echo htmlspecialchars($user['username']); ?>
                                        <?php if ($isCurrentUser): ?>
                                            <span class="ml-2 text-xs <?php echo getThemeComponentClasses('badge-info'); ?> px-2 py-1 rounded">You</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="text-sm <?php echo getThemeComponentClasses('table-cell-secondary'); ?>">ID: <?php echo htmlspecialchars($user['id']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    <?php echo $userRole === 'admin' ? getThemeComponentClasses('badge-admin') :
                                               ($userRole === 'moderator' ? getThemeComponentClasses('badge-moderator') : getThemeComponentClasses('badge-user')); ?>">
                                    <?php echo htmlspecialchars(getRoleDisplayName($userRole)); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($isLocked): ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo getThemeComponentClasses('badge-error'); ?>">
                                        Locked
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo getThemeComponentClasses('badge-success'); ?>">
                                        Active
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm <?php echo getThemeComponentClasses('table-cell-secondary'); ?>">
                                <?php
                                if ($user['last_login']) {
                                    echo date('M j, Y g:i A', strtotime($user['last_login']));
                                } else {
                                    echo 'Never';
                                }
                                ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <?php if (!$isCurrentUser): ?>
                                    <!-- Role Change Form -->
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="action" value="update_role">
                                        <input type="hidden" name="user_id" value="<?php echo htmlspecialchars($user['id']); ?>">
                                        <select name="new_role" onchange="this.form.submit()" class="text-xs <?php echo getThemeComponentClasses('input'); ?> px-2 py-1">
                                            <?php foreach (getAvailableRoles() as $role): ?>
                                                <option value="<?php echo $role; ?>" <?php echo $userRole === $role ? 'selected' : ''; ?>>
                                                    <?php echo getRoleDisplayName($role); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </form>

                                    <?php if ($isLocked): ?>
                                        <!-- Unlock User -->
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="unlock_user">
                                            <input type="hidden" name="user_id" value="<?php echo htmlspecialchars($user['id']); ?>">
                                            <button type="submit" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">Unlock</button>
                                        </form>
                                    <?php endif; ?>

                                    <!-- Delete User -->
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="action" value="delete_user">
                                        <input type="hidden" name="user_id" value="<?php echo htmlspecialchars($user['id']); ?>">
                                        <button type="submit" class="<?php echo getThemeComponentClasses('link-danger'); ?>">Delete</button>
                                    </form>
                                <?php else: ?>
                                    <span class="<?php echo getThemeComponentClasses('text-muted'); ?>">Current User</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
/**
 * Helper function to unlock user account
 */
function unlockUserAccount($userId) {
    $users = loadUsers();
    foreach ($users as $index => $user) {
        if ($user['id'] === $userId) {
            $users[$index]['locked_until'] = null;
            $users[$index]['login_attempts'] = 0;
            
            logActivity('admin.user_unlocked', "User account unlocked by admin", [
                'target_user_id' => $userId,
                'target_username' => $user['username'],
                'unlocked_by' => $_SESSION['username']
            ]);
            
            return saveUsers($users);
        }
    }
    return false;
}

/**
 * Helper function to delete user
 */
function deleteUser($userId) {
    if ($userId === $_SESSION['user_id']) {
        return false; // Can't delete yourself
    }
    
    $users = loadUsers();
    foreach ($users as $index => $user) {
        if ($user['id'] === $userId) {
            logActivity('admin.user_deleted', "User deleted by admin", [
                'target_user_id' => $userId,
                'target_username' => $user['username'],
                'target_role' => $user['role'] ?? 'user',
                'deleted_by' => $_SESSION['username']
            ]);
            
            unset($users[$index]);
            return saveUsers(array_values($users));
        }
    }
    return false;
}

include '../templates/footer.php';
?>
