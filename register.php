<?php
/**
 * User Registration Page
 * Handles new user account creation
 */

require_once 'config.php';
require_once 'auth.php';

// Redirect if already logged in
redirectIfAuthenticated();

// Check if registration is enabled
if (!isRegistrationEnabled()) {
    setFlashMessage('Registration is currently disabled.', 'error');
    header('Location: login.php');
    exit();
}

// Initialize variables
$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        // Get form data
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate passwords match
        if ($password !== $confirmPassword) {
            $errors[] = 'Passwords do not match.';
        }
        
        // If no validation errors, attempt registration
        if (empty($errors)) {
            $result = registerUser($username, $password);
            
            if ($result['success']) {
                setFlashMessage('Registration successful! You can now log in.', 'success');
                header('Location: login.php');
                exit();
            } else {
                $errors[] = $result['message'];
            }
        }
    }
}

// Set page title
$pageTitle = 'Register';

// Include header
include 'templates/header.php';
?>

<div class="max-w-md mx-auto">
    <!-- Registration Card -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> p-8 rounded-lg shadow">
        <div class="text-center mb-8">
            <div class="text-4xl mb-4">🤖</div>
            <h1 class="text-2xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">Create Account</h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-2">Join <?php echo APP_NAME; ?> today</p>
        </div>
        
        <!-- Display Errors -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <!-- Registration Form -->
        <form method="POST" action="" id="registerForm" novalidate>
            <!-- CSRF Token -->
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <!-- Username Field -->
            <div class="mb-6">
                <label for="username" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                    Username
                </label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                    required
                    minlength="3"
                    maxlength="50"
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                    placeholder="Enter your username"
                >
                <p class="text-xs <?php echo getThemeComponentClasses('text-muted'); ?> mt-1">Must be at least 3 characters long</p>
            </div>
            
            <!-- Password Field -->
            <div class="mb-6">
                <label for="password" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                    Password
                </label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                    required
                    minlength="<?php echo PASSWORD_MIN_LENGTH; ?>"
                    placeholder="Enter your password"
                >
                <p class="text-xs <?php echo getThemeComponentClasses('text-muted'); ?> mt-1">Must be at least <?php echo PASSWORD_MIN_LENGTH; ?> characters long</p>

                <!-- Password Strength Indicator -->
                <div id="passwordStrength" class="mt-2 hidden">
                    <div class="text-xs <?php echo getThemeComponentClasses('text-secondary'); ?> mb-1">Password strength:</div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="strengthBar" class="h-2 rounded-full transition-all duration-300"></div>
                    </div>
                </div>
            </div>
            
            <!-- Confirm Password Field -->
            <div class="mb-6">
                <label for="confirm_password" class="block text-sm font-medium <?php echo getThemeComponentClasses('text-primary'); ?> mb-2">
                    Confirm Password
                </label>
                <input
                    type="password"
                    id="confirm_password"
                    name="confirm_password"
                    class="<?php echo getThemeComponentClasses('input'); ?> w-full px-3 py-2 rounded-lg"
                    required
                    placeholder="Confirm your password"
                >
                <p id="passwordMatch" class="text-xs mt-1 hidden"></p>
            </div>

            <!-- Submit Button -->
            <button
                type="submit"
                class="w-full <?php echo getThemeComponentClasses('button-primary'); ?> text-lg py-3 mb-4 rounded-lg font-medium transition duration-200"
                onclick="return validateForm('registerForm')"
            >
                Create Account
            </button>
        </form>

        <!-- Login Link -->
        <div class="text-center">
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?>">
                Already have an account?
                <a href="login.php" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">Sign in</a>
            </p>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="mt-8 text-center text-sm <?php echo getThemeComponentClasses('text-muted'); ?>">
        <p>By creating an account, you agree to our Terms of Service and Privacy Policy.</p>
    </div>
</div>

<!-- Additional JavaScript for registration form -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const strengthIndicator = document.getElementById('passwordStrength');
    const strengthBar = document.getElementById('strengthBar');
    
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        
        if (password.length > 0) {
            strengthIndicator.classList.remove('hidden');
            const strength = checkPasswordStrength(password);
            
            // Update strength bar
            const percentage = (strength / 5) * 100;
            strengthBar.style.width = percentage + '%';
            
            // Update color based on strength
            if (strength <= 2) {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-red-500';
            } else if (strength <= 3) {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-yellow-500';
            } else {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-green-500';
            }
        } else {
            strengthIndicator.classList.add('hidden');
        }
    });
});
</script>

<?php include 'templates/footer.php'; ?>
