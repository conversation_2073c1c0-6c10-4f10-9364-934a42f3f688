<?php
/**
 * <PERSON><PERSON>t to fix function calls in mini-apps.php to restore original functionality
 */

$file = 'app/chat-bot/templates/mini-apps.php';

if (!file_exists($file)) {
    echo "File not found: $file\n";
    exit(1);
}

echo "Fixing function calls in $file...\n";

$content = file_get_contents($file);
$originalContent = $content;

// Fix the button onclick calls that need to be reverted to original function names
$replacements = [
    'onclick="openApplet(' => 'onclick="openMiniApp(',
    'onclick="integrateApplet(' => 'onclick="integrateMiniApp(',
    'onclick="editApplet(' => 'onclick="editMiniApp(',
    'onclick="showCreateApplet(' => 'onclick="showCreateMiniApp(',
    'onclick="closeCreateApplet(' => 'onclick="closeCreateMiniApp(',
    'onclick="previewApplet(' => 'onclick="previewMiniApp('
];

foreach ($replacements as $search => $replace) {
    $count = 0;
    $content = str_replace($search, $replace, $content, $count);
    if ($count > 0) {
        echo "  - Fixed '$search' to '$replace' ($count times)\n";
    }
}

// Save if changes were made
if ($content !== $originalContent) {
    if (file_put_contents($file, $content)) {
        echo "✅ File updated successfully\n";
    } else {
        echo "❌ Failed to update file\n";
    }
} else {
    echo "ℹ️  No changes needed\n";
}

echo "Function call fixes complete!\n";
?>
