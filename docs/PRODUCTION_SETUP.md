# Production Update System Setup

## Quick Setup Guide

### 1. Disable Demo Mode

By default, the update system shows demo updates for testing. To use in production:

1. Go to **Admin → Settings → Update Management → Manage Updates**
2. In the "Update Configuration" section, click **"Disable Demo"**
3. The system will now check for real updates

### 2. Configure Update Server (Optional)

To enable automatic dashboard updates, you need to set up an update server:

#### Option A: Manual Configuration

Edit `config/update_config.json`:

```json
{
    "dashboard": {
        "current_version": "1.0.0",
        "update_server_url": "https://your-update-server.com/api/check-updates",
        "update_channel": "stable",
        "auto_backup": true
    },
    "settings": {
        "demo_mode": false,
        "backup_retention_days": 30,
        "require_admin_approval": true
    }
}
```

#### Option B: Environment Variables

Set these environment variables:

```bash
DASHBOARD_UPDATE_SERVER_URL=https://your-update-server.com/api/check-updates
DASHBOARD_UPDATE_CHANNEL=stable
```

### 3. Update Server API

Your update server should respond to GET requests with this format:

**Request:**
```
GET https://your-server.com/api/check-updates?current_version=1.0.0
```

**Response:**
```json
{
    "version": "1.1.0",
    "release_notes": "Bug fixes and improvements",
    "download_url": "https://your-server.com/downloads/v1.1.0.zip",
    "size": "2.5 MB",
    "critical": false,
    "compatibility": {
        "php": ">=7.4",
        "mysql": ">=5.7"
    }
}
```

**No Update Available:**
```json
{
    "version": "1.0.0",
    "message": "No updates available"
}
```

### 4. Custom App Integration

For custom apps to use the update system:

```php
<?php
require_once 'includes/update_system.php';

class MyCustomApp implements CustomAppUpdateInterface {
    public function __construct() {
        $updateSystem = new UpdateSystem();
        $updateSystem->registerCustomApp('my_app', [
            'name' => 'My Custom App',
            'version' => '1.0.0',
            'update_handler' => [$this, 'checkForUpdates']
        ]);
    }
    
    public function checkForUpdates() {
        // Your update checking logic
        return [
            'available' => false,
            'current_version' => '1.0.0'
        ];
    }
    
    // Implement other required methods...
}

// Initialize your app
$myApp = new MyCustomApp();
```

### 5. Security Considerations

- Only administrators can access update management
- All operations are CSRF protected
- Backups are created automatically before updates
- Update packages should be verified for integrity
- Use HTTPS for update server communications

### 6. File Permissions

Ensure these directories are writable:

```bash
chmod 755 config/
chmod 755 updates/
chmod 755 backups/
chmod 644 VERSION
```

### 7. Backup Strategy

- Automatic backups are created before updates
- Configure retention period in settings
- Test restore process regularly
- Keep offsite backups for critical deployments

## Configuration Options

### Update Configuration (`config/update_config.json`)

```json
{
    "dashboard": {
        "current_version": "1.0.0",
        "update_server_url": "https://your-server.com/api/check-updates",
        "update_channel": "stable",
        "auto_backup": true
    },
    "custom_apps": {
        "app_id": {
            "name": "App Name",
            "version": "1.0.0",
            "update_url": "https://app-server.com/updates",
            "enabled": true
        }
    },
    "settings": {
        "demo_mode": false,
        "backup_retention_days": 30,
        "max_backup_size_mb": 500,
        "require_admin_approval": true,
        "maintenance_mode_during_update": true
    }
}
```

### Environment Variables

```bash
# Update server configuration
DASHBOARD_UPDATE_SERVER_URL=https://your-server.com/api/check-updates
DASHBOARD_UPDATE_CHANNEL=stable

# Backup configuration
UPDATE_BACKUP_RETENTION_DAYS=30
UPDATE_MAX_BACKUP_SIZE_MB=500

# Security settings
UPDATE_REQUIRE_ADMIN_APPROVAL=true
UPDATE_MAINTENANCE_MODE=true
```

## Troubleshooting

### No Updates Showing

1. Check if demo mode is disabled
2. Verify update server URL is configured
3. Check network connectivity to update server
4. Review logs for error messages

### Update Check Fails

1. Verify update server is accessible
2. Check SSL certificate validity
3. Ensure proper API response format
4. Check firewall settings

### Backup Creation Fails

1. Check directory permissions
2. Verify available disk space
3. Check backup directory exists
4. Review error logs

## Monitoring

Monitor these log files:

- `logs/system.log` - General system logs
- `logs/activity.log` - Update activity logs
- Web server error logs

## Support

For issues with the update system:

1. Check the documentation
2. Review log files
3. Test in demo mode first
4. Verify configuration settings
