# Update System Documentation

## Overview

The Dashboard Update System provides a comprehensive solution for managing updates to both the dashboard boilerplate and integrated custom applications. It features automatic backups, rollback functionality, and a simple API for custom app integration.

## Architecture

### Dual-Layer System

1. **Dashboard Boilerplate Updates**
   - Core system updates
   - Framework improvements
   - Security patches
   - New features

2. **Custom App Updates**
   - Integrated application updates
   - Plugin updates
   - Custom module updates
   - Third-party integrations

## Features

### Core Features
- ✅ **Automatic Backups** - Creates backups before any update
- ✅ **Rollback Support** - One-click restoration from backups
- ✅ **Version Management** - Tracks versions and compatibility
- ✅ **Safe Updates** - Verification and integrity checks
- ✅ **Admin Interface** - Easy-to-use web interface
- ✅ **Activity Logging** - Complete audit trail

### Custom App Integration
- ✅ **Update API** - Simple interface for custom apps
- ✅ **Multiple Integration Methods** - Class-based, function-based, or URL-based
- ✅ **Dependency Management** - Version compatibility checking
- ✅ **Custom Handlers** - App-specific update logic

## Usage

### For Dashboard Administrators

1. **Access Update Management**
   ```
   Admin → Settings → Update Management → Manage Updates
   ```

2. **Check for Updates**
   - Click "Check for Updates" button
   - System checks both dashboard and custom apps
   - Results displayed with available updates

3. **Create Backups**
   - Manual backup creation available
   - Automatic backups before updates
   - Backup management interface

4. **Apply Updates**
   - One-click update process
   - Automatic backup creation
   - Progress monitoring

5. **Rollback if Needed**
   - View available backups
   - One-click restoration
   - Automatic verification

### For Custom App Developers

#### Method 1: Class-Based Integration (Recommended)

```php
<?php
require_once 'includes/update_system.php';

class MyCustomApp implements CustomAppUpdateInterface {
    public function __construct() {
        $updateSystem = new UpdateSystem();
        $updateSystem->registerCustomApp('my_app', [
            'name' => 'My Custom App',
            'version' => '1.0.0',
            'update_handler' => [$this, 'checkForUpdates']
        ]);
    }
    
    public function checkForUpdates() {
        // Your update checking logic
        return [
            'available' => true,
            'current_version' => '1.0.0',
            'new_version' => '1.1.0',
            'release_notes' => 'Bug fixes and improvements'
        ];
    }
    
    public function downloadUpdate($updateInfo) {
        // Download update package
    }
    
    public function applyUpdate($updatePath) {
        // Apply the update
    }
    
    public function rollbackUpdate($backupPath) {
        // Rollback logic
    }
    
    public function getVersion() {
        return '1.0.0';
    }
}
```

#### Method 2: Function-Based Integration

```php
<?php
require_once 'includes/update_system.php';

function myAppUpdateHandler() {
    return [
        'available' => false,
        'current_version' => '2.0.0'
    ];
}

$updateSystem = new UpdateSystem();
$updateSystem->registerCustomApp('my_function_app', [
    'name' => 'Function-based App',
    'version' => '2.0.0',
    'update_handler' => 'myAppUpdateHandler'
]);
```

#### Method 3: URL-Based Integration

```php
<?php
require_once 'includes/update_system.php';

$updateSystem = new UpdateSystem();
$updateSystem->registerCustomApp('my_url_app', [
    'name' => 'URL-based App',
    'version' => '1.5.0',
    'update_url' => 'https://api.myapp.com/check-updates'
]);
```

## API Reference

### UpdateSystem Class

#### Methods

- `registerCustomApp($appId, $config)` - Register a custom app
- `checkDashboardUpdates()` - Check for dashboard updates
- `checkCustomAppUpdates($appId = null)` - Check for custom app updates
- `createBackup($type, $appId = null)` - Create backup
- `getAvailableBackups()` - Get list of backups
- `getCurrentDashboardVersion()` - Get current dashboard version

#### Configuration Options

```php
$config = [
    'name' => 'App Name',                    // Required
    'version' => '1.0.0',                   // Required
    'update_url' => 'https://...',          // Optional
    'update_handler' => 'function_name',    // Optional
    'dependencies' => [                     // Optional
        'dashboard' => '>=1.0.0',
        'php' => '>=7.4'
    ]
];
```

### CustomAppUpdateInterface

Required methods for class-based integration:

```php
interface CustomAppUpdateInterface {
    public function checkForUpdates();
    public function downloadUpdate($updateInfo);
    public function applyUpdate($updatePath);
    public function rollbackUpdate($backupPath);
    public function getVersion();
}
```

## Update Response Format

### Update Check Response

```php
[
    'available' => true,
    'current_version' => '1.0.0',
    'new_version' => '1.1.0',
    'release_notes' => 'Bug fixes and improvements',
    'download_url' => 'https://...',
    'size' => '2.5 MB',
    'critical' => false,
    'compatibility' => [
        'dashboard' => '>=1.0.0',
        'php' => '>=7.4'
    ]
]
```

## File Structure

```
/
├── includes/
│   └── update_system.php          # Core update system
├── admin/
│   └── updates.php               # Admin interface
├── config/
│   └── update_config.json        # Update configuration
├── updates/                      # Downloaded updates
├── backups/
│   └── updates/                  # Update backups
├── examples/
│   └── custom_app_integration.php # Integration examples
├── docs/
│   └── UPDATE_SYSTEM.md          # This documentation
└── VERSION                       # Dashboard version
```

## Security Considerations

1. **Admin Access Required** - Only administrators can manage updates
2. **CSRF Protection** - All update operations protected
3. **Backup Verification** - Integrity checks on backups
4. **Update Verification** - Checksum validation
5. **Activity Logging** - Complete audit trail

## Best Practices

### For Dashboard Administrators
- Always create backups before updates
- Test updates in staging environment first
- Monitor logs during update process
- Keep multiple backup versions

### For Custom App Developers
- Implement proper error handling
- Provide detailed release notes
- Test update process thoroughly
- Support rollback functionality
- Use semantic versioning

## Troubleshooting

### Common Issues

1. **Update Check Fails**
   - Check network connectivity
   - Verify update URL accessibility
   - Check logs for error details

2. **Backup Creation Fails**
   - Check disk space
   - Verify directory permissions
   - Check backup directory exists

3. **Update Application Fails**
   - Check file permissions
   - Verify update package integrity
   - Review application logs

### Log Locations
- System logs: `logs/system.log`
- Update logs: `logs/updates.log`
- Activity logs: Database activity_logs table

## Future Enhancements

- Scheduled automatic updates
- Update notifications
- Staged rollouts
- Update testing framework
- Integration with CI/CD pipelines
