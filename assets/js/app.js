/**
 * Main JavaScript file for AI Dashboard Application
 * Handles client-side functionality and user interactions
 */

// Application namespace
const AIDashboard = {
    // Configuration
    config: {
        passwordMinLength: 8,
        flashMessageDuration: 5000,
        animationDuration: 300
    },
    
    // Initialize application
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.handleFlashMessages();
    },
    
    // Set up global event listeners
    setupEventListeners: function() {
        document.addEventListener('DOMContentLoaded', () => {
            this.init();
        });
        
        // Handle form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.tagName === 'FORM') {
                this.handleFormSubmission(e);
            }
        });
        
        // Handle password visibility toggles
        document.addEventListener('click', (e) => {
            if (e.target.id === 'togglePassword' || e.target.closest('#togglePassword')) {
                this.togglePasswordVisibility(e);
            }
        });
    },
    
    // Initialize components
    initializeComponents: function() {
        this.initializePasswordStrength();
        this.initializeFormValidation();
        this.initializeTooltips();
        // Removed page load animations for better UX
    },
    
    // Handle flash messages
    handleFlashMessages: function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach((alert) => {
            // Add fade-in animation
            alert.classList.add('fade-in');
            
            // Auto-hide success messages
            if (alert.classList.contains('alert-success')) {
                setTimeout(() => {
                    this.fadeOutElement(alert);
                }, this.config.flashMessageDuration);
            }
            
            // Add close button
            this.addCloseButton(alert);
        });
    },
    
    // Add close button to alerts
    addCloseButton: function(alert) {
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.className = 'float-right text-xl font-bold opacity-70 hover:opacity-100 cursor-pointer';
        closeButton.onclick = () => this.fadeOutElement(alert);
        
        alert.insertBefore(closeButton, alert.firstChild);
    },
    
    // Fade out element
    fadeOutElement: function(element) {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.5s ease-out';
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 500);
    },
    

    
    // Initialize password strength indicator
    initializePasswordStrength: function() {
        const passwordInput = document.getElementById('password');
        if (!passwordInput) return;
        
        passwordInput.addEventListener('input', (e) => {
            this.updatePasswordStrength(e.target.value);
        });
    },
    
    // Update password strength indicator
    updatePasswordStrength: function(password) {
        const strengthIndicator = document.getElementById('passwordStrength');
        const strengthBar = document.getElementById('strengthBar');
        
        if (!strengthIndicator || !strengthBar) return;
        
        if (password.length > 0) {
            strengthIndicator.classList.remove('hidden');
            const strength = this.calculatePasswordStrength(password);
            
            // Update strength bar
            const percentage = (strength / 5) * 100;
            strengthBar.style.width = percentage + '%';
            
            // Update color based on strength
            strengthBar.className = 'h-2 rounded-full transition-all duration-300 ' + 
                this.getStrengthColor(strength);
        } else {
            strengthIndicator.classList.add('hidden');
        }
    },
    
    // Calculate password strength
    calculatePasswordStrength: function(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    },
    
    // Get strength color class
    getStrengthColor: function(strength) {
        if (strength <= 2) return 'bg-red-500';
        if (strength <= 3) return 'bg-yellow-500';
        return 'bg-green-500';
    },
    
    // Toggle password visibility
    togglePasswordVisibility: function(e) {
        e.preventDefault();
        const button = e.target.closest('#togglePassword');
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eyeIcon');
        
        if (!passwordInput || !eyeIcon) return;
        
        const isPassword = passwordInput.type === 'password';
        passwordInput.type = isPassword ? 'text' : 'password';
        
        // Update icon
        eyeIcon.innerHTML = isPassword ? this.getEyeOffIcon() : this.getEyeIcon();
    },
    
    // Get eye icon SVG
    getEyeIcon: function() {
        return `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        `;
    },
    
    // Get eye-off icon SVG
    getEyeOffIcon: function() {
        return `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
        `;
    },
    
    // Initialize form validation
    initializeFormValidation: function() {
        const forms = document.querySelectorAll('form');
        forms.forEach((form) => {
            this.setupFormValidation(form);
        });
    },
    
    // Set up validation for a specific form
    setupFormValidation: function(form) {
        const inputs = form.querySelectorAll('input[required]');
        
        inputs.forEach((input) => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                if (input.classList.contains('border-red-500')) {
                    this.validateField(input);
                }
            });
        });
        
        // Password confirmation validation
        const passwordInput = form.querySelector('#password');
        const confirmPasswordInput = form.querySelector('#confirm_password');
        
        if (passwordInput && confirmPasswordInput) {
            [passwordInput, confirmPasswordInput].forEach((input) => {
                input.addEventListener('input', () => {
                    this.validatePasswordMatch(passwordInput, confirmPasswordInput);
                });
            });
        }
    },
    
    // Validate individual field
    validateField: function(input) {
        const isValid = input.checkValidity() && input.value.trim() !== '';
        
        if (isValid) {
            input.classList.remove('border-red-500');
            input.classList.add('border-green-500');
        } else {
            input.classList.remove('border-green-500');
            input.classList.add('border-red-500');
        }
        
        return isValid;
    },
    
    // Validate password match
    validatePasswordMatch: function(passwordInput, confirmPasswordInput) {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        const matchIndicator = document.getElementById('passwordMatch');
        
        if (password && confirmPassword) {
            if (password === confirmPassword) {
                confirmPasswordInput.classList.remove('border-red-500');
                confirmPasswordInput.classList.add('border-green-500');
                confirmPasswordInput.setCustomValidity('');
                
                if (matchIndicator) {
                    matchIndicator.textContent = 'Passwords match ✓';
                    matchIndicator.className = 'text-xs mt-1 text-green-600';
                    matchIndicator.classList.remove('hidden');
                }
            } else {
                confirmPasswordInput.classList.remove('border-green-500');
                confirmPasswordInput.classList.add('border-red-500');
                confirmPasswordInput.setCustomValidity('Passwords do not match');
                
                if (matchIndicator) {
                    matchIndicator.textContent = 'Passwords do not match';
                    matchIndicator.className = 'text-xs mt-1 text-red-600';
                    matchIndicator.classList.remove('hidden');
                }
            }
        } else if (matchIndicator) {
            matchIndicator.classList.add('hidden');
        }
    },
    
    // Handle form submission
    handleFormSubmission: function(e) {
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
            // Add loading state
            const originalText = submitButton.textContent;
            submitButton.innerHTML = '<span class="loading"></span> Processing...';
            submitButton.disabled = true;
            
            // Reset button after a delay (in case of client-side validation failure)
            setTimeout(() => {
                if (submitButton.disabled) {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }, 3000);
        }
    },
    
    // Initialize tooltips
    initializeTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach((element) => {
            this.createTooltip(element);
        });
    },
    
    // Create tooltip for element
    createTooltip: function(element) {
        const tooltipText = element.getAttribute('data-tooltip');
        if (!tooltipText) return;
        
        element.addEventListener('mouseenter', () => {
            this.showTooltip(element, tooltipText);
        });
        
        element.addEventListener('mouseleave', () => {
            this.hideTooltip();
        });
    },
    
    // Show tooltip
    showTooltip: function(element, text) {
        const tooltip = document.createElement('div');
        tooltip.id = 'tooltip';
        tooltip.className = 'absolute bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg z-50';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    },
    
    // Hide tooltip
    hideTooltip: function() {
        const tooltip = document.getElementById('tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }
};

// Global utility functions
window.validateForm = function(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;
    
    const inputs = form.querySelectorAll('input[required]');
    let isValid = true;
    
    inputs.forEach((input) => {
        if (!AIDashboard.validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
};

window.checkPasswordStrength = function(password) {
    return AIDashboard.calculatePasswordStrength(password);
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => AIDashboard.init());
} else {
    AIDashboard.init();
}
