/**
 * Custom CSS for AI Dashboard Application
 * Extends Tailwind CSS with custom styles and components
 */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #4b5563;
    --secondary-hover: #374151;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #f9fafb;
}

/* Custom Utility Classes */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-shadow {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.card-shadow:hover {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
    transform: translateY(-1px);
}

/* Input Styles */
.input-field {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: white;
}

.input-field:focus {
    outline: none;
    ring: 2px;
    ring-color: var(--primary-color);
    border-color: transparent;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-field:invalid {
    border-color: var(--error-color);
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid;
}

.alert-success {
    background-color: #ecfdf5;
    border-color: #10b981;
    color: #065f46;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #ef4444;
    color: #991b1b;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: #f59e0b;
    color: #92400e;
}

.alert-info {
    background-color: #eff6ff;
    border-color: #3b82f6;
    color: #1e40af;
}

/* Navigation Styles */
nav a {
    transition: color 0.2s ease;
}

nav a:hover {
    color: var(--primary-color) !important;
}

/* Logout button should stay red on hover */
nav a.bg-red-600:hover {
    background-color: #dc2626 !important;
    color: white !important;
}

/* Admin dropdown links should have subtle white hover effect in dark mode */

/* Admin parent menu should turn blue on hover in light mode like other nav links */
#adminDropdown:hover {
    color: var(--primary-color) !important;
}

/* In dark mode, all header menu text should turn white on hover */
.dark nav a:hover {
    color: white !important;
}

/* Override admin dropdown button specifically in dark mode */
.dark #adminDropdown:hover {
    color: white !important;
}

/* Fix label alignment with checkboxes - remove bottom margin when next to checkbox */
label[for] + input[type="checkbox"],
input[type="checkbox"] + label,
.flex input[type="checkbox"] + label {
    margin-bottom: 0;
}

/* Ensure checkbox labels align properly */
input[type="checkbox"] + label {
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
}

/* Icon alignment with text */
.align-text-bottom {
    vertical-align: text-bottom;
}

/* Alternative icon alignment for better text flow */
.icon-text-align {
    vertical-align: -0.125em;
}

/* Admin dropdown will be positioned dynamically by JavaScript */

/* Form Styles */
form {
    max-width: 100%;
}

label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-shadow {
        margin: 0 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card-shadow {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}
